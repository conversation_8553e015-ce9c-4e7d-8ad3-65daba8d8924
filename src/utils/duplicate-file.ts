import { constants } from "node:fs";
import { access, copyFile } from "node:fs/promises";
import path from "node:path";

import { environment } from "@constants/environment";

export const duplicateFile = async (
  folderLocation: string,
  fileName: string,
  newFileName: string
): Promise<void> => {
  if (!environment.baseFileLocation) {
    throw new Error(
      "BASE_FILE_LOCATION environment variable is not configured"
    );
  }

  const sourcePath = path.join(
    environment.baseFileLocation,
    folderLocation,
    fileName
  );

  const destinationPath = path.join(
    environment.baseFileLocation,
    folderLocation,
    newFileName
  );

  let destinationFileExists;

  try {
    await access(destinationPath, constants.F_OK);

    destinationFileExists = true;
  } catch {
    destinationFileExists = false;
  }

  if (destinationFileExists) {
    throw new Error(`Destination file already exists: ${destinationPath}`);
  }

  await copyFile(sourcePath, destinationPath);
};
