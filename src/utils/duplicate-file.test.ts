import { access, copyFile } from "node:fs/promises";

import { environment } from "@constants/environment";

import { duplicateFile } from "./duplicate-file";

vi.mock("@constants/environment", () => ({
  environment: {
    baseFileLocation: "/base/location",
  },
}));

vi.mock("node:fs/promises", () => ({
  copyFile: vi.fn(),
  access: vi.fn().mockRejectedValue(new Error("File does not exist")),
}));

describe("duplicateFile", () => {
  const folderLocation = "folder";
  const fileName = "original.txt";
  const newFileName = "copy.txt";

  const expectedSource = "/base/location/folder/original.txt";
  const expectedDestination = "/base/location/folder/copy.txt";

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should call copyFile with correct source and destination paths", async () => {
    await duplicateFile(folderLocation, fileName, newFileName);

    expect(copyFile).toHaveBeenCalledWith(expectedSource, expectedDestination);
  });

  it("should throw error if destination file already exists", async () => {
    vi.mocked(access).mockResolvedValueOnce();

    await expect(
      duplicateFile(folderLocation, fileName, newFileName)
    ).rejects.toThrow("Destination file already exists");

    expect(copyFile).not.toHaveBeenCalled();
  });

  it("should throw error if baseFileLocation is not configured", async () => {
    environment.baseFileLocation = undefined;

    await expect(
      duplicateFile(folderLocation, fileName, newFileName)
    ).rejects.toThrow(
      "BASE_FILE_LOCATION environment variable is not configured"
    );

    expect(copyFile).not.toHaveBeenCalled();
  });
});
