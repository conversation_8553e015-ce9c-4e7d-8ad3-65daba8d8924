import { fetchSqlData } from "./sql/fetch-sql-data";
import { getEndBalanceSql } from "./sql/queries/end-balance-sql";

type Response = Record<
  string,
  {
    customerName: string;
    endBalance: number;
    currentBalance: number | undefined;
    totalBalance: number;
  }
>;

const getEndBalance = async (
  serviceNumbers: string[],
  token: string
): Promise<Response> => {
  const sql = getEndBalanceSql(serviceNumbers);
  const response = await fetchSqlData(sql, token);

  const result: Response = {};

  for (const row of response) {
    const {
      customerName,
      serviceNumber,
      previousTotal,
      currentBalance,
      totalBalance,
    } = row;

    result[serviceNumber!] = {
      customerName: customerName as string,
      endBalance: Number(previousTotal),
      currentBalance: Number(currentBalance),
      totalBalance: Number(totalBalance),
    };
  }

  return result;
};

export { getEndBalance };
