import { environment } from "@constants/environment";

import { request } from "../api-caller/request";

/**
 * Fetches the authentication token for <PERSON><PERSON>.
 */
const getAuthToken = async (): Promise<string> => {
  try {
    const url = `${environment.bluskyBaseUrl}/v1/authentication/login`;

    const body = {
      username: environment.bluskyUsername,
      password: environment.bluskyPassword,
    };
    const response = await request.post<{ access_token: string }>(url, body);

    return response.access_token;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing json response";
    throw new Error(`Unable to Authenticate to <PERSON>skey: ${errorMessage}`);
  }
};

export { getAuthToken };
