import { environment } from "@constants/environment";

import { RequestError } from "../../../utils/errors/request-error";
import { request, type RequestOptions } from "../../api-caller/request";

type SqlResponse = {
  headers: string[];
  values: Array<Array<string | number>>;
};

const fetchSqlData = async (
  rawSql: string,
  token: string
): Promise<Array<Record<string, string | number | undefined>>> => {
  try {
    const options: RequestOptions = {
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Authorization: `Bearer ${token}`,
      },
    };

    const encodedSql = encodeSql(rawSql);
    const bluskySqlUrl = `${environment.bluskyBaseUrl}/datasources/${environment.bluskyDatasource}/sql?query=${encodedSql}`;

    const response: SqlResponse = await request.get(
      bluskySqlUrl,
      "json",
      options
    );

    const camelCaseHeaders = response.headers.map((header) =>
      _toCamelCase(header)
    );

    return response.values.map((row) => {
      const result: Record<string, string | number | undefined> = {};

      for (const [index, header] of camelCaseHeaders.entries()) {
        const value = row[index];
        const isNullish =
          value === undefined ||
          (typeof value === "string" &&
            ["N\\A", "N/A", "NAN"].includes(value.toUpperCase())) ||
          (typeof value === "number" && Number.isNaN(value));

        result[header] = isNullish ? undefined : value;
      }

      return result;
    });
  } catch (error) {
    const error_ =
      error instanceof RequestError
        ? new RequestError(
            `Error fetching SQL data from BluSky: ${error.message}`,
            error.url,
            error.httpCode,
            error.originalError
          )
        : new Error(
            `Error fetching SQL data from BluSky: ${(error as Error).message}`
          );
    throw error_;
  }
};

const _toCamelCase = (string_: string): string => {
  return (
    string_
      // Match both underscores (_) and spaces (\s). The + treats multiple match as a single match.
      .replaceAll(/[\s_]+(.)?/g, (_, chr) => {
        return typeof chr === "string" ? chr.toUpperCase() : "";
      })
      .replace(/^./, (match) => match.toLowerCase())
  );
};

const encodeSql = (rawSql: string) => {
  return encodeURIComponent(rawSql.replaceAll(/\s+/g, " ").trim());
};

export { fetchSqlData };
