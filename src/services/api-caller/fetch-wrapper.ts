import { Readable } from "node:stream";

import StreamObject from "stream-json/streamers/StreamObject.js";

import { RequestError } from "../../utils/errors/request-error";

type RequestOptions = Omit<RequestInit, "body" | "method">;
type ResponseType = "json" | "blob" | "text";

// eslint-disable-next-line max-params
async function fetchWrapper<T>(
  method: string,
  url: string,
  body?: Record<string, unknown> | string,
  reponseType: ResponseType = "json",
  options?: RequestOptions
): Promise<T> {
  const requestOptions: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
    body: body ? JSON.stringify(body) : null,
    ...options,
  };

  try {
    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      await _handleFetchError(response);
    }

    if (reponseType === "blob") {
      return await _parseBlobResponse<T>(response);
    }

    if (reponseType === "text") {
      return await _parseTextResponse<T>(response);
    }

    const contentLength = response.headers.get("content-length");
    // 500 MB threshold in bytes:
    const maxSizeInBytes = 500 * 1024 * 1024;

    if (contentLength) {
      const size = Number.parseInt(contentLength, 10);

      if (!Number.isNaN(size) && size < maxSizeInBytes) {
        return await _parseJsonResponse<T>(response);
      }
    }

    // Stream it for large JSON responses
    return await _parseLargeJsonResponse<T>(response);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error in fetchWrapper:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    throw new RequestError(errorMessage, url, undefined, error);
  }
}

async function _parseJsonResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.json()) as T;

    return responseData;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error in _parseJsonResponse:", error);

    // Response cannot be parsed, either its empty or its not a JSON
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing json response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _parseLargeJsonResponse<T>(response: Response): Promise<T> {
  try {
    const responseData: Partial<T> = {};

    if (!response.body) {
      throw new Error("Response body is null");
    }

    const nodeStream: Readable = readableStreamToNodeReadable(response.body);

    const jsonStream = StreamObject.withParser();

    for await (const chunk of nodeStream.pipe(jsonStream) as AsyncIterable<{
      key: string;
      value: unknown;
    }>) {
      const { key, value } = chunk;

      (responseData as Record<string, unknown>)[key] = value;
    }

    return responseData as T;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error in _parseLargeJsonResponse:", error);

    // Response cannot be parsed, either its empty or its not a JSON
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing json response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _parseBlobResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.blob()) as T;

    return responseData;
  } catch (error) {
    // Response cannot be parsed, either its empty or its not a blob
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing blob response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _parseTextResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.text()) as T;

    return responseData;
  } catch (error) {
    // Response cannot be parsed, either its empty or it's not a text
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing text response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _handleFetchError(response: Response): Promise<never> {
  let errorBody: unknown;
  let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

  try {
    const clonedResponse = response.clone();

    try {
      errorBody = await response.json();
    } catch {
      const textBody = await clonedResponse.text();
      errorBody = textBody?.trim() || null;
    }
  } catch {
    errorBody = null;
  }

  if (typeof errorBody === "string" && errorBody) {
    errorMessage = errorBody;
  } else if (errorBody && typeof errorBody === "object") {
    const bodyObject = errorBody as Record<string, unknown>;
    errorMessage = (bodyObject["message"] ||
      bodyObject["error"] ||
      errorMessage) as string;
  }

  throw new RequestError(
    errorMessage,
    response.url,
    response.status,
    errorBody
  );
}

function readableStreamToNodeReadable(
  readableStream: ReadableStream<Uint8Array>
): Readable {
  const reader = readableStream.getReader();

  return new Readable({
    async read() {
      const { done, value } = await reader.read();

      if (done) {
        this.push(null);
      } else {
        this.push(Buffer.from(value));
      }
    },
  });
}

export { fetchWrapper, type RequestOptions, type ResponseType };
