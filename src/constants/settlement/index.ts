import { type SettlementFilters } from "@lib/settlement/read/types";

export const kycClientPlatformId = 7;

export const statusInit = "INIT";

export const merchantTypeId = 3;

export const settlementTypes = {
  commissionRate: "Commision Rate",
  buyRate: "Buy Rate",
  combination: "Combination",
} as const;

export type SettlementType =
  (typeof settlementTypes)[keyof typeof settlementTypes];

// There are four parts to this number format:
// 1. Positive numbers
//   - Adds a leading space, adds a dollar sign, adds padding spaces to align the number
//     to the right, adds a comma as a thousand separator, rounds to two decimal places,
//     and adds a trailing space to align with the parentheses in the Negative number format.
//   - Example: " $       1,234,567.89 "
// 2. Negative numbers
//   - Adds a leading space, adds a dollar sign, adds padding spaces to align the number
//     to the right, adds a comma as a thousand separator, rounds to two decimal places,
//     and encloses the number in parentheses.
//   - Example: " $      (1,234,567.89)"
// 3. Zero values
//   - Adds a leading space, adds a dollar sign, adds padding spaces to align the number
//     to the right, uses a dash to represent zero, and reserves three trailing spaces
//     to align with the Positive and Negative number formats.
//   - Example: " $                -   "
// 4. Text values
//   - Displayed as is with a leading and trailing space for alignment.
//   - Example: " abcd "
export const excelNumberFormat =
  '_($* #,##0.00_);_($* (#,##0.00);_($* "-"??_);_(@_)';

export const excelTransactionColumns = [
  { header: "Created_Date", key: "Created_Date", width: 25 },
  { header: "Updated_Date", key: "Updated_Date", width: 25 },
  { header: "Service_Number", key: "Service_Number", width: 10 },
  { header: "Original_Amt", key: "Original_Amt", width: 15 },
  { header: "Refund_Amt", key: "Refund_Amt", width: 15 },
  { header: "Final_Amt", key: "Final_Amt" },
  { header: "Currency", key: "Currency" },
  { header: "Country", key: "Country" },
  { header: "Billable", key: "Billable" },
  { header: "Platform", key: "Platform" },
  { header: "Cust_Number", key: "Cust_Number" },
  { header: "Rcode", key: "Rcode" },
  { header: "Integrator_Name", key: "Integrator_Name" },
  { header: "Program_Name", key: "Program_Name" },
  { header: "Billing_Name", key: "Billing_Name" },
  { header: "Transaction_ID", key: "Transaction_ID" },
  { header: "Receipt_ID", key: "Receipt_ID" },
  { header: "Interac_Ref", key: "Interac_Ref" },
  { header: "FI_Name", key: "FI_Name" },
  { header: "Status", key: "Status" },
];

export const excelSummaryColumns = [
  { key: "1", width: 16 },
  { key: "2", width: 30 },
  { key: "3", width: 16 },
  { key: "4", width: 20 },
  { key: "5", width: 15 },
  { key: "6", width: 15 },
];

export const kycSummaryColumns = [
  { header: "", key: "1", width: 10 },
  { header: "", key: "2", width: 10 },
  { header: "", key: "3", width: 20 },
  { header: "", key: "4", width: 12 },
  { header: "", key: "5", width: 15 },
];

export const kycTransactionColumns = [
  { header: "Service Number", key: "SERVICE_NUMBER", width: 20 },
  { header: "Name", key: "Name", width: 25 },
  { header: "Email", key: "Email", width: 25 },
  { header: "Type", key: "Type", width: 15 },
  { header: "Client User Id", key: "clientUserId", width: 20 },
  { header: "Status", key: "status", width: 15 },
  { header: "Created Date", key: "DispCreated", width: 20 },
  { header: "Updated Date", key: "DispUpdated", width: 20 },
];

export const platformCodes = {
  summary: "SUMMARY",
  idp: "IDP",
  eti: "ETI",
  etf: "ETF",
  rfm: "RFM",
  eto: "ETO",
  rto: "RTO",
  rtx: "RTX",
  ach: "ACH",
  anr: "ANR",
  anx: "ANX",
  kyc: "KYC",
} as const;

export const rtoTransactionSuffixes = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  R: "_R",
  // eslint-disable-next-line @typescript-eslint/naming-convention
  P: "_P",
} as const;

export const defaultSettlementPreferences: SettlementFilters = {
  filters: {
    textInputValue: "",
    clientType: "",
    displayAdjusted: "Show All",
    state: "",
    status: "No Filter",
    frequency: "",
    startDate: "",
    endDate: "",
  },
  sortKey: "fromDate",
  sortOrder: "desc",
  pageNumber: 1,
  recordsPerPage: 20,
} as const;
