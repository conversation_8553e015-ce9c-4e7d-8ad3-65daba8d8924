import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import { type PrismaClient } from "@prisma/client";
import { addDays, addMonths, endOfMonth, setDate } from "date-fns";
import { describe, expect, it, vi } from "vitest";

import { createWireRecord } from "./repository";
import { createWire, getExpectedWireDate } from "./service";
import { type CreateWireProperties, type Wire } from "./types";

vi.mock("./repository");
vi.mock("@prisma/client");

const mockCreateWireRecord = vi.mocked(createWireRecord);

describe("createWire", () => {
  const mockPrisma = {} as unknown as PrismaClient;
  const mockWire: Wire = {
    wireId: 1,
    expectedWireDate: new Date("2024-01-16"),
    finalWireDate: new Date("2024-01-16"),
    expectedWireAmount: 10_000,
    finalWireAmount: 10_000,
    isHold: false,
    reference: "TEST-REF",
    frequencyId: 1,
    isCancelled: false,
    customerId: 123,
    settlementId: 456,
    paymentRailId: 789,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockCreateWireRecord.mockResolvedValue(mockWire);
  });

  const createMockSettlement = (
    overrides?: Partial<ApprovalCustomerSettlement>
  ): ApprovalCustomerSettlement => ({
    customerSettlementsId: 456,
    customerId: 123,
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
    endBalance: 10_000,
    regenerationCount: 0,
    platform: {
      platformId: 2,
      platformCode: "ETI",
    },
    customer: {
      customerName: "Test Customer",
      serviceNumber: "**********",
      customerTradingName: "Test Trading",
      entity: {
        entityName: "Test Entity",
        logoName: "test-logo.png",
      },
      wireGroup: {
        wireConfiguration: {
          isHold: false,
          thresholdAmount: 5000,
        },
        beneficiary: {
          reference: "TEST-REF",
          bankAccount: {
            bankPaymentRailId: 789,
          },
        },
      },
      emailConfiguration: {
        isEmailEnabled: true,
      },
      customerSettlementType: {
        description: "Test Settlement Type",
      },
    },
    customerCustomerType: {
      statementFolderLocation: "/test/path",
      statementFrequency: {
        statementFrequencyId: 1,
        statementFrequencyCode: "M",
      },
      customerType: {
        customerTypeName: "Test Type",
      },
    },
    ...overrides,
  });

  it("should create a wire with bank payment rail when no crypto account", async () => {
    const settlement = createMockSettlement();
    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    const result = await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(mockPrisma, {
      expectedWireDate: addDays(new Date("2024-01-01"), 15),
      finalWireDate: addDays(new Date("2024-01-01"), 15),
      expectedWireAmount: 10_000,
      finalWireAmount: 10_000,
      customerId: 123,
      settlementId: 456,
      frequencyId: 1,
      isHold: false,
      reference: "TEST-REF",
      paymentRailId: 789,
    });

    expect(result).toBe(mockWire);
  });

  it("should create a wire with crypto payment rail when crypto account exists", async () => {
    const settlement = createMockSettlement({
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 5000,
          },
          beneficiary: {
            reference: "TEST-REF",
            bankAccount: {
              bankPaymentRailId: 789,
            },
            cryptoAccount: {
              cryptoPaymentRailId: 999,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
        customerSettlementType: {
          description: "Test Settlement Type",
        },
      },
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        paymentRailId: 999,
      })
    );
  });

  it("should set isHold to true when end balance is below threshold", async () => {
    const settlement = createMockSettlement({
      endBalance: 3000,
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        isHold: true,
      })
    );
  });

  it("should set isHold to true when wire configuration isHold is true", async () => {
    const settlement = createMockSettlement({
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: true,
            thresholdAmount: 5000,
          },
          beneficiary: {
            reference: "TEST-REF",
            bankAccount: {
              bankPaymentRailId: 789,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
        customerSettlementType: {
          description: "Test Settlement Type",
        },
      },
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        isHold: true,
      })
    );
  });

  it("should handle undefined reference", async () => {
    const settlement = createMockSettlement({
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 5000,
          },
          beneficiary: {
            // @ts-expect-error - reference should be undefined for test
            reference: undefined,
            bankAccount: {
              bankPaymentRailId: 789,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
        customerSettlementType: {
          description: "Test Settlement Type",
        },
      },
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        reference: undefined,
      })
    );
  });

  it("should handle missing payment rail", async () => {
    const settlement = createMockSettlement({
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 5000,
          },
          beneficiary: {
            reference: "TEST-REF",
            bankAccount: {
              // @ts-expect-error - bankPaymentRailId should be undefined for test
              bankPaymentRailId: undefined,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
        customerSettlementType: {
          description: "Test Settlement Type",
        },
      },
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        paymentRailId: undefined,
      })
    );
  });

  it("should handle missing frequency", async () => {
    const settlement = createMockSettlement({
      customerCustomerType: {
        statementFolderLocation: "/test/path",
        statementFrequency: {
          // @ts-expect-error - statementFrequencyId should be undefined for test
          statementFrequencyId: undefined,
        },
        customerType: {
          customerTypeName: "Test Type",
        },
      },
    });

    const properties: CreateWireProperties = {
      settlement,
      wireAmount: 10_000,
      toDate: "2024-01-01",
    };

    await createWire(mockPrisma, properties);

    expect(mockCreateWireRecord).toHaveBeenCalledWith(
      mockPrisma,
      expect.objectContaining({
        frequencyId: undefined,
      })
    );
  });
});

describe("getExpectedWireDate", () => {
  it("should return date plus 15 days for monthly frequency (M)", () => {
    const result = getExpectedWireDate("M", "2024-01-01");
    expect(result).toEqual(addDays(new Date("2024-01-01"), 15));
  });

  it("should return end of month for semi-monthly frequency (SM) when date is 15th", () => {
    const result = getExpectedWireDate("SM", "2024-01-15");
    expect(result).toEqual(endOfMonth(new Date("2024-01-15")));
  });

  it("should return 15th of next month for semi-monthly frequency (SM) when date is not 15th", () => {
    const result = getExpectedWireDate("SM", "2024-01-31");
    expect(result).toEqual(setDate(addMonths(new Date("2024-01-31"), 1), 15));
  });

  it("should return date plus 4 days for twice a week frequency (TaW)", () => {
    const result = getExpectedWireDate("TaW", "2024-01-01");
    expect(result).toEqual(addDays(new Date("2024-01-01"), 4));
  });

  it("should return date plus 8 days for weekly Monday frequency (WM)", () => {
    const result = getExpectedWireDate("WM", "2024-01-01");
    expect(result).toEqual(addDays(new Date("2024-01-01"), 8));
  });

  it("should return date plus 7 days for weekly Friday frequency (WF)", () => {
    const result = getExpectedWireDate("WF", "2024-01-01");
    expect(result).toEqual(addDays(new Date("2024-01-01"), 7));
  });

  it("should return date plus 1 day for unknown frequency", () => {
    const result = getExpectedWireDate("UNKNOWN", "2024-01-01");
    expect(result).toEqual(addDays(new Date("2024-01-01"), 1));
  });

  it("should handle different date formats", () => {
    const result = getExpectedWireDate("M", "2024-12-31");
    expect(result).toEqual(addDays(new Date("2024-12-31"), 15));
  });
});
