import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import { createCustomerSettlementGenerationSchedule } from "./repository";
import { type CreateScheduleData } from "./type";

type MockPrismaClient = {
  customerSettlementGenerationSchedule: {
    create: ReturnType<typeof vi.fn>;
  };
};

const mockPrismaClient: MockPrismaClient = {
  customerSettlementGenerationSchedule: {
    create: vi.fn(),
  },
};

const createMockScheduleData = (
  overrides?: Partial<CreateScheduleData>
): CreateScheduleData => ({
  serviceNumber: "1234567890",
  fromDate: "2025-04-05",
  toDate: "2025-04-11",
  customerCustomerTypeId: 1,
  generationType: "INITIAL",
  generationStatus: "PENDING",
  ...overrides,
});

const createMockCreatedSchedule = (
  data: CreateScheduleData,
  mockCurrentDate: Date,
  id = 111
) => ({
  customerSettlementGenerationScheduleId: id,
  serviceNumber: data.serviceNumber,
  fromDate: new Date(data.fromDate),
  toDate: new Date(data.toDate),
  generationDate: mockCurrentDate,
  startDate: mockCurrentDate,
  completionDate: data.completionDate ?? null,
  generationType: data.generationType,
  generationStatus: data.generationStatus,
  errorMessage: data.errorMessage ?? null,
  parentScheduleId: null,
  createdAt: mockCurrentDate,
  updatedAt: mockCurrentDate,
  deletedAt: null,
  customerCustomerTypeId: data.customerCustomerTypeId,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  fromUI: null,
});

describe("Customer Settlement Generation Schedule Repository", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("createCustomerSettlementGenerationSchedule", () => {
    it("should create an INITIAL schedule", async () => {
      const mockCurrentDate = new Date("2025-07-11T03:01:44.000Z");
      vi.setSystemTime(mockCurrentDate);

      const createScheduleData = createMockScheduleData();
      const mockCreatedSchedule = createMockCreatedSchedule(
        createScheduleData,
        mockCurrentDate
      );

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockResolvedValue(
        mockCreatedSchedule
      );

      const result = await createCustomerSettlementGenerationSchedule(
        mockPrismaClient as unknown as PrismaClient,
        createScheduleData
      );

      expect(
        mockPrismaClient.customerSettlementGenerationSchedule.create
      ).toHaveBeenCalledWith({
        data: {
          serviceNumber: "1234567890",
          fromDate: new Date("2025-04-05"),
          toDate: new Date("2025-04-11"),
          customerCustomerTypeId: 1,
          generationType: "INITIAL",
          generationStatus: "PENDING",
          generationDate: mockCurrentDate,
          startDate: mockCurrentDate,
          completionDate: null,
          errorMessage: null,
        },
      });
      expect(result).toEqual(mockCreatedSchedule);
    });

    it("should create a FINAL schedule", async () => {
      const mockCurrentDate = new Date("2025-07-11T03:01:44.000Z");
      vi.setSystemTime(mockCurrentDate);

      const createScheduleData = createMockScheduleData({
        generationType: "FINAL",
        generationStatus: "STARTED",
      });

      const mockCreatedSchedule = createMockCreatedSchedule(
        createScheduleData,
        mockCurrentDate,
        222
      );

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockResolvedValue(
        mockCreatedSchedule
      );

      const result = await createCustomerSettlementGenerationSchedule(
        mockPrismaClient as unknown as PrismaClient,
        createScheduleData
      );

      expect(
        mockPrismaClient.customerSettlementGenerationSchedule.create
      ).toHaveBeenCalledWith({
        data: {
          serviceNumber: "1234567890",
          fromDate: new Date("2025-04-05"),
          toDate: new Date("2025-04-11"),
          customerCustomerTypeId: 1,
          generationType: "FINAL",
          generationStatus: "STARTED",
          generationDate: mockCurrentDate,
          startDate: mockCurrentDate,
          completionDate: null,
          errorMessage: null,
        },
      });
      expect(result).toEqual(mockCreatedSchedule);
    });

    it("should create schedule with different service number and customer type", async () => {
      const mockCurrentDate = new Date("2025-07-10T19:25:28.000Z");
      vi.setSystemTime(mockCurrentDate);

      const createScheduleData = createMockScheduleData({
        serviceNumber: "3333333333",
        customerCustomerTypeId: 21,
        fromDate: "2025-07-01",
        toDate: "2025-07-31",
      });

      const mockCreatedSchedule = createMockCreatedSchedule(
        createScheduleData,
        mockCurrentDate,
        222
      );

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockResolvedValue(
        mockCreatedSchedule
      );

      const result = await createCustomerSettlementGenerationSchedule(
        mockPrismaClient as unknown as PrismaClient,
        createScheduleData
      );

      expect(
        mockPrismaClient.customerSettlementGenerationSchedule.create
      ).toHaveBeenCalledWith({
        data: {
          serviceNumber: "3333333333",
          fromDate: new Date("2025-07-01"),
          toDate: new Date("2025-07-31"),
          customerCustomerTypeId: 21,
          generationType: "INITIAL",
          generationStatus: "PENDING",
          generationDate: mockCurrentDate,
          startDate: mockCurrentDate,
          completionDate: null,
          errorMessage: null,
        },
      });
      expect(result).toEqual(mockCreatedSchedule);
    });

    it("should create a schedule with completion date", async () => {
      const mockCurrentDate = new Date("2025-07-11T03:01:44.000Z");
      vi.setSystemTime(mockCurrentDate);

      const mockCompletionDate = new Date("2025-07-11T03:01:43.000Z");

      const createScheduleData = createMockScheduleData({
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        completionDate: mockCompletionDate,
      });

      const mockCreatedSchedule = createMockCreatedSchedule(
        createScheduleData,
        mockCurrentDate,
        222
      );

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockResolvedValue(
        mockCreatedSchedule
      );

      const result = await createCustomerSettlementGenerationSchedule(
        mockPrismaClient as unknown as PrismaClient,
        createScheduleData
      );

      expect(
        mockPrismaClient.customerSettlementGenerationSchedule.create
      ).toHaveBeenCalledWith({
        data: {
          serviceNumber: "1234567890",
          fromDate: new Date("2025-04-05"),
          toDate: new Date("2025-04-11"),
          customerCustomerTypeId: 1,
          generationType: "FINAL",
          generationStatus: "COMPLETE",
          generationDate: mockCurrentDate,
          startDate: mockCurrentDate,
          completionDate: mockCompletionDate,
          errorMessage: null,
        },
      });
      expect(result.completionDate).toEqual(mockCompletionDate);
      expect(result).toEqual(mockCreatedSchedule);
    });

    it("should create a schedule with error message", async () => {
      const mockCurrentDate = new Date("2025-07-11T03:01:44.000Z");
      vi.setSystemTime(mockCurrentDate);

      const mockErrorMessage = "An error occurred";

      const createScheduleData = createMockScheduleData({
        generationType: "FINAL",
        generationStatus: "ERROR",
        errorMessage: mockErrorMessage,
      });

      const mockCreatedSchedule = createMockCreatedSchedule(
        createScheduleData,
        mockCurrentDate,
        222
      );

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockResolvedValue(
        mockCreatedSchedule
      );

      const result = await createCustomerSettlementGenerationSchedule(
        mockPrismaClient as unknown as PrismaClient,
        createScheduleData
      );

      expect(
        mockPrismaClient.customerSettlementGenerationSchedule.create
      ).toHaveBeenCalledWith({
        data: {
          serviceNumber: "1234567890",
          fromDate: new Date("2025-04-05"),
          toDate: new Date("2025-04-11"),
          customerCustomerTypeId: 1,
          generationType: "FINAL",
          generationStatus: "ERROR",
          generationDate: mockCurrentDate,
          startDate: mockCurrentDate,
          completionDate: null,
          errorMessage: mockErrorMessage,
        },
      });
      expect(result.errorMessage).toEqual(mockErrorMessage);
      expect(result).toEqual(mockCreatedSchedule);
    });

    it("should handle database errors properly", async () => {
      const createScheduleData = createMockScheduleData();
      const mockError = new Error("Database connection failed");

      mockPrismaClient.customerSettlementGenerationSchedule.create.mockRejectedValue(
        mockError
      );

      await expect(
        createCustomerSettlementGenerationSchedule(
          mockPrismaClient as unknown as PrismaClient,
          createScheduleData
        )
      ).rejects.toThrow("Database connection failed");
    });
  });
});
