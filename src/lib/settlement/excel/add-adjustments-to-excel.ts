import path from "node:path";

import { environment } from "@constants/environment";
import {
  buyRateExcelPlatformColumnMap,
  platformCodes,
  type SettlementType,
  settlementTypes,
} from "@constants/settlement";
import { payOutGroup, type Platform } from "@constants/transactions/platform";
import ExcelJS from "exceljs";

import { writeExcelFile } from "./helpers/write-excel-file";

import type { AdjustmentDetails, PlatformCode } from "../repository/types";

export const addAdjustmentsToNonMerchantExcel = async (
  folderLocation: string,
  fileName: string,
  settlementType: SettlementType,
  adjustments: Partial<Record<PlatformCode, AdjustmentDetails[]>>
) => {
  const workbook = new ExcelJS.Workbook();

  const filePath = path.join(
    environment.baseFileLocation!,
    folderLocation,
    fileName
  );

  await workbook.xlsx.readFile(filePath);

  const worksheet = workbook.getWorksheet("SUMMARY");

  if (!worksheet) {
    throw new Error("SUMMARY worksheet not found in Excel file.");
  }

  const finalRow = worksheet.lastRow?.number;

  if (!finalRow) {
    throw new Error(`No rows found in SUMMARY worksheet.`);
  }

  switch (settlementType) {
    case settlementTypes.commissionRate: {
      addAdjustmentsToCommissionRateExcel(adjustments, finalRow, worksheet);
      break;
    }

    case settlementTypes.buyRate: {
      addAdjustmentsToBuyRateExcel(adjustments, finalRow, worksheet);
      break;
    }

    case settlementTypes.combination: {
      addAdjustmentsToCombinationExcel(adjustments, finalRow, worksheet);
      break;
    }
  }

  await writeExcelFile(workbook, folderLocation, fileName);
};

const addAdjustmentsToCommissionRateExcel = (
  adjustments: Partial<Record<PlatformCode, AdjustmentDetails[]>>,
  finalRow: number,
  worksheet: ExcelJS.Worksheet
) => {
  worksheet.getColumn("A").font = {
    bold: true,
  };

  let currentRow = finalRow - 1;

  for (const [platformCode, adjustmentDetails] of Object.entries(adjustments)) {
    for (const adjustment of adjustmentDetails) {
      const platform = platformCode === "SUMMARY" ? "Summary" : platformCode;

      const { label } = adjustment;

      const amount = getAdjustmentAmount(
        adjustment,
        platformCode as PlatformCode
      );

      const comment = adjustment.displayCommentExcel ? adjustment.comment : "";

      // Each cell in the array is a cell in the Excel row
      const rowData = [platform, label, "", "", amount, comment];

      worksheet.insertRow(currentRow, rowData);

      currentRow += 1;
    }
  }

  // Update the total payable formula after adjustment rows were added
  const updatedFinalRow = currentRow + 1;

  worksheet.getCell(`E${updatedFinalRow}`).value = {
    formula: `SUM(E12:E${currentRow - 1})`,
  };
};

const addAdjustmentsToBuyRateExcel = (
  adjustments: Partial<Record<PlatformCode, AdjustmentDetails[]>>,
  finalRow: number,
  worksheet: ExcelJS.Worksheet
) => {
  let currentRow = finalRow - 1;

  for (const [platformCode, adjustmentDetails] of Object.entries(adjustments)) {
    for (const adjustment of adjustmentDetails) {
      const { label } = adjustment;

      const amount = getAdjustmentAmount(
        adjustment,
        platformCode as PlatformCode
      );

      const comment = adjustment.displayCommentExcel ? adjustment.comment : "";

      // Each cell in the array is a cell in the Excel row
      const rowData = ["", label, comment, amount];

      worksheet.insertRow(currentRow, rowData);

      worksheet.getCell(`B${currentRow}`).font = {
        bold: true,
      };

      if (platformCode === platformCodes.summary) {
        worksheet.getCell(`Q${currentRow}`).value = amount;
      } else {
        const column = buyRateExcelPlatformColumnMap[platformCode as Platform];

        worksheet.getCell(`${column}${currentRow}`).value = amount;
      }

      currentRow += 1;
    }
  }

  // Update every total payable formula after adjustment rows were added
  const updatedFinalRow = currentRow + 1;

  worksheet.getCell(`D${updatedFinalRow}`).value = {
    formula: `SUM(D13:D${currentRow - 1})`,
  };

  worksheet.getCell(`Q${updatedFinalRow}`).value = {
    formula: `SUM(Q13:Q${currentRow - 1})`,
  };

  for (const column of Object.values(buyRateExcelPlatformColumnMap)) {
    const platformTotalCell = worksheet.getCell(`${column}${updatedFinalRow}`);

    platformTotalCell.value = {
      formula: `SUM(${column}13:${column}${currentRow - 1})`,
    };
  }
};

const addAdjustmentsToCombinationExcel = (
  adjustments: Partial<Record<PlatformCode, AdjustmentDetails[]>>,
  finalRow: number,
  worksheet: ExcelJS.Worksheet
) => {
  worksheet.getColumn("A").font = {
    bold: true,
  };

  let currentRow = finalRow - 1;

  for (const [platformCode, adjustmentDetails] of Object.entries(adjustments)) {
    for (const adjustment of adjustmentDetails) {
      const platform = platformCode === "SUMMARY" ? "Summary" : platformCode;

      const { label } = adjustment;

      const amount = getAdjustmentAmount(
        adjustment,
        platformCode as PlatformCode
      );

      const comment = adjustment.displayCommentExcel ? adjustment.comment : "";

      // Each cell in the array is a cell in the Excel row
      const rowData = [platform, label, amount, comment];

      worksheet.insertRow(currentRow, rowData);

      currentRow += 1;
    }
  }

  // Update the total payable formula after adjustment rows were added
  const updatedFinalRow = currentRow + 1;

  worksheet.getCell(`C${updatedFinalRow}`).value = {
    formula: `SUM(C13:C${currentRow - 1})`,
  };
};

const getAdjustmentAmount = (
  adjustment: AdjustmentDetails,
  platformCode: PlatformCode
): number => {
  // Adjustments for pay-outs are positive
  // Adjustments for pay-ins and summary are negative
  return payOutGroup.includes(platformCode as Platform)
    ? adjustment.amount
    : -adjustment.amount;
};
