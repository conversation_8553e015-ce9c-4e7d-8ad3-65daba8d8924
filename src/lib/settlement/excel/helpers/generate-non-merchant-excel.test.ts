/* eslint-disable @typescript-eslint/naming-convention */
import { readFileSync } from "node:fs";

import { entities, type Entity } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";
import { settlementTypes } from "@constants/settlement";

import { generateNonMerchantExcel } from "./generate-non-merchant-excel";
import {
  type BuyRateSummary,
  generateBuyRatePlatformSheet,
  generateBuyRateSummarySheet,
} from "./non-merchant/buy-rate";
import { checkSettlementDatesMatch } from "./non-merchant/check-settlement-dates";
import { generateCombinationSummarySheet } from "./non-merchant/combination";
import {
  type CommissionRateSummary,
  generateCommissionRatePlatformSheet,
  generateCommissionRateSummarySheet,
} from "./non-merchant/commission-rate";

import type {
  NonMerchantFeesBreakdown,
  SettlementResult,
} from "../../functions/workers/types";
import type { Customer } from "@lib/customer/repository/types";

vi.mock("exceljs", () => {
  return {
    default: {
      Workbook: vi.fn(() => mockWorkbook),
    },
  };
});

vi.mock("./non-merchant/check-settlement-dates", () => ({
  checkSettlementDatesMatch: vi.fn(),
}));

vi.mock("node:fs", () => ({
  readFileSync: vi.fn(),
}));

vi.mock("./non-merchant/commission-rate", () => ({
  generateCommissionRateSummarySheet: vi.fn(),
  generateCommissionRatePlatformSheet: vi.fn(),
}));

vi.mock("./non-merchant/buy-rate", () => ({
  generateBuyRateSummarySheet: vi.fn(),
  generateBuyRatePlatformSheet: vi.fn(),
}));

vi.mock("./non-merchant/combination", () => ({
  generateCombinationSummarySheet: vi.fn(),
}));

vi.mock("./write-excel-file", () => ({
  writeExcelFile: vi.fn(),
}));

const imageId = 1;
const mockWorkbookAddImage = vi.fn(() => imageId);
const mockWorksheetAddImage = vi.fn();
const mockEachSheet = vi.fn((callback) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  callback({
    addImage: mockWorksheetAddImage,
  });
});
const mockWorkbook = {
  addImage: mockWorkbookAddImage,
  eachSheet: mockEachSheet,
};

const createMockCustomer = (
  settlementType: string,
  entityName: Entity = entities.gigadat
) =>
  ({
    nonMerchantSettlementType: settlementType,
    entityLogoName: "logo.png",
    entityName,
    customerTradingName: "TestTrading",
    statementFolderLocation: "/tmp",
  }) as unknown as Customer;

const interval = {
  fromDate: {
    year: 2025,
    month: 1,
    day: 1,
  },
  toDate: {
    year: 2025,
    month: 1,
    day: 31,
  },
};

const mockSettlementResult = {
  settlement: {
    ETI: {
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      totalTransactionSummary: {
        transactionCount: 10,
        totalTransactionAmount: 1000,
      },
      feesBreakdown: {
        totalTransactionCommission: 5,
        interval,
        merchantBreakdown: {
          180: {
            feeCollectedFromMerchant: 100,
            commission: 8,
            fees: {
              transactionCommission: 3,
              salesCommission: 5,
            },
            merchantName: "DEF Merchant",
            serviceNumber: "9991800000",
          },
          444: {
            feeCollectedFromMerchant: 400,
            commission: 6,
            fees: {
              transactionCommission: 2,
              salesCommission: 4,
            },
            merchantName: "ABC Merchant",
            serviceNumber: "9994440000",
          },
        },
      },
    },
    RTO: {
      platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
      totalTransactionSummary: {
        transactionCount: 9,
        totalTransactionAmount: 900,
      },
      feesBreakdown: {
        totalTransactionCommission: 6,
        interval,
        merchantBreakdown: {
          180: {
            feeCollectedFromMerchant: 100,
            commission: 12,
            fees: {
              transactionCommission: 2,
              salesCommission: 10,
            },
            merchantName: "DEF Merchant",
            serviceNumber: "9991800000",
          },
          444: {
            feeCollectedFromMerchant: 400,
            commission: 48,
            fees: {
              transactionCommission: 4,
              salesCommission: 44,
            },
            merchantName: "ABC Merchant",
            serviceNumber: "9994440000",
          },
        },
      },
    },
  },
} as unknown as SettlementResult;

const expectedCommissionRateSummaries: CommissionRateSummary[] = [
  {
    platformDescription: "Interac e-Transfer Pay-In (ETI)",
    transactionCount: 10,
    totalTransactionAmount: 1000,
    commission: 5,
  },
  {
    platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
    transactionCount: 9,
    totalTransactionAmount: 900,
    commission: 6,
  },
];

const expectedBuyRateSummaries: BuyRateSummary[] = [
  {
    merchantName: "ABC Merchant",
    platformCommission: {
      ETI: {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        commission: 4,
      },
      RTO: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        commission: 44,
      },
    },
  },
  {
    merchantName: "DEF Merchant",
    platformCommission: {
      ETI: {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        commission: 5,
      },
      RTO: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        commission: 10,
      },
    },
  },
];

const expectedEtiData1 = {
  customerTradingName: "TestTrading",
  fromDate: interval.fromDate,
  toDate: interval.toDate,
  platform: "ETI",
  platformDescription: "Interac e-Transfer Pay-In (ETI)",
  merchantPlatformCommission: (
    mockSettlementResult.settlement["ETI"]!
      .feesBreakdown! as NonMerchantFeesBreakdown
  ).merchantBreakdown["180"],
};

const expectedEtiData2 = {
  customerTradingName: "TestTrading",
  fromDate: interval.fromDate,
  toDate: interval.toDate,
  platform: "ETI",
  platformDescription: "Interac e-Transfer Pay-In (ETI)",
  merchantPlatformCommission: (
    mockSettlementResult.settlement["ETI"]!
      .feesBreakdown! as NonMerchantFeesBreakdown
  ).merchantBreakdown["444"],
};

const expectedRtoData1 = {
  customerTradingName: "TestTrading",
  fromDate: interval.fromDate,
  toDate: interval.toDate,
  platform: "RTO",
  platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
  merchantPlatformCommission: (
    mockSettlementResult.settlement["RTO"]!
      .feesBreakdown! as NonMerchantFeesBreakdown
  ).merchantBreakdown["180"],
};

const expectedRtoData2 = {
  customerTradingName: "TestTrading",
  fromDate: interval.fromDate,
  toDate: interval.toDate,
  platform: "RTO",
  platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
  merchantPlatformCommission: (
    mockSettlementResult.settlement["RTO"]!
      .feesBreakdown! as NonMerchantFeesBreakdown
  ).merchantBreakdown["444"],
};

const fileName = "test-file";

describe("generateNonMerchantExcel", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("throws error if settlement dates do not match", async () => {
    const customer = createMockCustomer(settlementTypes.commissionRate);

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(false);

    await expect(
      generateNonMerchantExcel(mockSettlementResult, customer, fileName)
    ).rejects.toThrow("Settlement dates do not match");
  });

  it("throws error for unknown settlement type", async () => {
    const customer = createMockCustomer("Half Rate");

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await expect(
      generateNonMerchantExcel(mockSettlementResult, customer, fileName)
    ).rejects.toThrow("Unknown non-merchant settlement type");
  });

  it("generates commission rate sheets if customer settlement type is commission rate", async () => {
    const customer = createMockCustomer(settlementTypes.commissionRate);

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    const expectedSummaries = [
      {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        transactionCount: 10,
        totalTransactionAmount: 1000,
        commission: 5,
      },
      {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        transactionCount: 9,
        totalTransactionAmount: 900,
        commission: 6,
      },
    ];

    expect(generateCommissionRateSummarySheet).toHaveBeenCalledWith(
      expectedSummaries,
      customer.customerTradingName,
      interval,
      mockWorkbook
    );
    expect(generateCommissionRatePlatformSheet).toHaveBeenCalledWith({
      customerTradingName: customer.customerTradingName,
      platform: "ETI",
      platformSettlement: mockSettlementResult.settlement["ETI"],
      workbook: mockWorkbook,
      options: {
        feeType: "salesFee",
      },
    });
    expect(generateCommissionRatePlatformSheet).toHaveBeenCalledWith({
      customerTradingName: customer.customerTradingName,
      platform: "RTO",
      platformSettlement: mockSettlementResult.settlement["RTO"],
      workbook: mockWorkbook,
      options: {
        feeType: "transactionFee",
      },
    });
  });

  it("adds correct logo image to workbook", async () => {
    const customer = createMockCustomer(settlementTypes.commissionRate);
    const logoPath = `${baseDirectoryName}/${customer.entityLogoName}`;
    const mockBuffer = Buffer.from("fake-image-data");

    vi.mocked(readFileSync).mockReturnValue(mockBuffer);
    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(readFileSync).toHaveBeenCalledWith(logoPath);
    expect(mockWorkbookAddImage).toHaveBeenCalledWith({
      buffer: mockBuffer,
      extension: "png",
    });
  });

  it("adds logo image to each sheet", async () => {
    const customer = createMockCustomer(
      settlementTypes.commissionRate,
      entities.gigadat
    );

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(mockEachSheet).toHaveBeenCalled();
    expect(mockWorksheetAddImage).toHaveBeenCalled();
  });

  it("adds logo image to correct cell range for gigadat entity", async () => {
    const customer = createMockCustomer(
      settlementTypes.commissionRate,
      entities.gigadat
    );

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(mockWorksheetAddImage).toHaveBeenCalledWith(imageId, "B1:C4");
  });

  it("adds logo image to correct cell range for non-gigadat entity", async () => {
    const customer = createMockCustomer(
      settlementTypes.commissionRate,
      entities.wyzia
    );

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(mockWorksheetAddImage).toHaveBeenCalledWith(imageId, "C1:C5");
  });

  it("generates buy rate sheets if customer settlement type is buy rate", async () => {
    const customer = createMockCustomer(settlementTypes.buyRate);

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(generateBuyRateSummarySheet).toHaveBeenCalledWith(
      expectedBuyRateSummaries,
      customer.customerTradingName,
      interval,
      mockWorkbook
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenCalledWith(
      expectedEtiData1,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenCalledWith(
      expectedEtiData2,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenCalledWith(
      expectedRtoData1,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenCalledWith(
      expectedRtoData2,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
  });

  it("generates platform sheets in the right order for buy rate", async () => {
    const customer = createMockCustomer(settlementTypes.buyRate);

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      1,
      expectedEtiData2,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      2,
      expectedRtoData2,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      3,
      expectedEtiData1,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      4,
      expectedRtoData1,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
  });

  it("generates correct sheets for settlement type combination", async () => {
    const customer = createMockCustomer(
      settlementTypes.combination,
      entities.gigadat
    );

    vi.mocked(checkSettlementDatesMatch).mockReturnValue(true);

    await generateNonMerchantExcel(mockSettlementResult, customer, fileName);

    expect(generateCombinationSummarySheet).toHaveBeenCalledWith({
      commissionRateSummaries: expectedCommissionRateSummaries,
      buyRateSummaries: expectedBuyRateSummaries,
      customerTradingName: customer.customerTradingName,
      interval,
      workbook: mockWorkbook,
    });

    expect(generateCommissionRatePlatformSheet).toHaveBeenCalledWith({
      customerTradingName: customer.customerTradingName,
      platform: "ETI",
      platformSettlement: mockSettlementResult.settlement["ETI"],
      workbook: mockWorkbook,
      options: {
        feeType: "salesFee",
      },
    });
    expect(generateCommissionRatePlatformSheet).toHaveBeenCalledWith({
      customerTradingName: customer.customerTradingName,
      platform: "RTO",
      platformSettlement: mockSettlementResult.settlement["RTO"],
      workbook: mockWorkbook,
      options: {
        feeType: "transactionFee",
      },
    });

    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      1,
      expectedEtiData2,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      2,
      expectedRtoData2,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      3,
      expectedEtiData1,
      mockWorkbook,
      {
        includeGatewayFee: true,
      }
    );
    expect(generateBuyRatePlatformSheet).toHaveBeenNthCalledWith(
      4,
      expectedRtoData1,
      mockWorkbook,
      {
        includeGatewayFee: false,
      }
    );
  });
});
