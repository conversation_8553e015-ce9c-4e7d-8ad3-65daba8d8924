import { type KycTxn } from "../../../../services/blusky/transactions";

import type ExcelJS from "exceljs";

export const addKycTransactionRow = (
  sheetName: ExcelJS.Worksheet,
  kycTransaction: KycTxn,
  rowNumber: number
): void => {
  sheetName.getCell(`A${rowNumber}`).value = kycTransaction.serviceNumber;
  sheetName.getCell(`B${rowNumber}`).value = kycTransaction.name;
  sheetName.getCell(`C${rowNumber}`).value = kycTransaction.email;
  sheetName.getCell(`D${rowNumber}`).value = kycTransaction.type;
  sheetName.getCell(`E${rowNumber}`).value = kycTransaction.clientUserId;
  sheetName.getCell(`F${rowNumber}`).value = kycTransaction.status;
  sheetName.getCell(`G${rowNumber}`).value = kycTransaction.dispCreated;
  sheetName.getCell(`H${rowNumber}`).value = kycTransaction.dispUpdated;
};
