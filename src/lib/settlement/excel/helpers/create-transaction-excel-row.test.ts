/* eslint-disable @typescript-eslint/naming-convention */
import ExcelJS from "exceljs";

import { addTransactionRow } from "./create-transaction-excel-row";

import type { Transaction } from "../../../../services/blusky/transactions";

describe("addTransactionRow", () => {
  let workbook: ExcelJS.Workbook;
  let worksheet: ExcelJS.Worksheet;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
    worksheet = workbook.addWorksheet("Test");
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should populate all transaction fields in the correct cells", () => {
    const mockTransaction: Transaction = {
      createdDate: "2024-01-15T10:30:00Z",
      updatedDate: "2024-01-15T11:45:00Z",
      serviceNumber: "**********",
      originalAmt: 100.5,
      refundAmt: 0,
      finalAmt: 100.5,
      currency: "CAD",
      country: "CA",
      billable: true,
      platform: "ETI",
      custNumber: "CUST123",
      rcode: 200,
      integratorName: "Test Integrator",
      programName: "Test Program",
      billingName: "Test Billing",
      transactionID: "TXN123456",
      receiptID: "RCP789012",
      interacRef: "INT345678",
      fIName: "Test Bank",
      status: "STATUS_SUCCESS",
    };

    const rowNumber = 5;

    addTransactionRow(worksheet, mockTransaction, rowNumber);

    const row = worksheet.getRow(rowNumber);

    expect(row.getCell(1).value).toBe("2024-01-15T10:30:00Z"); // CreatedDate
    expect(row.getCell(2).value).toBe("2024-01-15T11:45:00Z"); // UpdatedDate
    expect(row.getCell(3).value).toBe(1_234_567_890); // ServiceNumber as number
    expect(row.getCell(4).value).toBe(100.5); // OriginalAmt
    expect(row.getCell(5).value).toBe(0); // RefundAmt
    expect(row.getCell(6).value).toBe(100.5); // FinalAmt
    expect(row.getCell(7).value).toBe("CAD"); // Currency
    expect(row.getCell(8).value).toBe("CA"); // Country
    expect(row.getCell(9).value).toBe("True"); // Billable as string
    expect(row.getCell(10).value).toBe("ETI"); // Platform
    expect(row.getCell(11).value).toBe("CUST123"); // CustNumber
    expect(row.getCell(12).value).toBe("200"); // Rcode as string
    expect(row.getCell(13).value).toBe("Test Integrator"); // IntegratorName
    expect(row.getCell(14).value).toBe("Test Program"); // ProgramName
    expect(row.getCell(15).value).toBe("Test Billing"); // BillingName
    expect(row.getCell(16).value).toBe("TXN123456"); // TransactionID
    expect(row.getCell(17).value).toBe("RCP789012"); // ReceiptID
    expect(row.getCell(18).value).toBe("INT345678"); // InteracRef
    expect(row.getCell(19).value).toBe("Test Bank"); // FIName
    expect(row.getCell(20).value).toBe("STATUS_SUCCESS"); // Status
  });
});
