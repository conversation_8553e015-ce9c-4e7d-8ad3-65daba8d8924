/* eslint-disable @typescript-eslint/naming-convention */
import ExcelJS from "exceljs";

import {
  generateBuyRateSummarySheet,
  generateBuyRatePlatformSheet,
  type BuyRateSummary,
} from "./buy-rate";
import { validateWorksheetName } from "../validate-worksheet-name";

import type { Platform } from "@constants/transactions/platform";
import type { MerchantPlatformCommission } from "@lib/settlement/functions/workers/calculate-non-merchant-settlement";
import type { DateOnly } from "@utils/date-only";

vi.mock("../validate-worksheet-name", () => ({
  validateWorksheetName: vi.fn(),
}));

describe("generateBuyRateSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  it("should create a summary worksheet with correct values", () => {
    const customerName = "Test Customer";

    const merchantName1 = "Alpha";
    const commissionValues1 = {
      IDP: {
        platformDescription: "Interac Online Pay-In (IDP)",
        commission: 1,
      },
      ETF: {
        platformDescription: "Interac e-Transfer Pay-In (ETF)",
        commission: 3.33,
      },
      ETI: {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        commission: 10,
      },
      RFM: {
        platformDescription: "Interac e-Transfer Request Money (RFM)",
        commission: 20,
      },
      ETO: {
        platformDescription: "Interac e-Transfer Pay-Out (ETO)",
        commission: 5,
      },
      RTO: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        commission: 15,
      },
      RTX: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTX)",
        commission: 2,
      },
      ACH: {
        platformDescription: "eCashout Pay-Outs (ACH)",
        commission: 999.99,
      },
      ANR: {
        platformDescription: "Real Time eCashout (ANR)",
        commission: 4.5,
      },
      ANX: {
        platformDescription: "Real Time eCashout (ANX)",
        commission: 6.1,
      },
    };

    const merchantName2 = "Beta";
    const commissionValues2 = {
      IDP: {
        platformDescription: "Interac Online Pay-In (IDP)",
        commission: 3,
      },
      ETF: {
        platformDescription: "Interac e-Transfer Pay-In (ETF)",
        commission: 6.33,
      },
      ETI: {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        commission: 30,
      },
      RFM: {
        platformDescription: "Interac e-Transfer Request Money (RFM)",
        commission: 40,
      },
      ETO: {
        platformDescription: "Interac e-Transfer Pay-Out (ETO)",
        commission: 11,
      },
      RTO: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        commission: 25,
      },
      RTX: {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTX)",
        commission: 77.32,
      },
      ACH: {
        platformDescription: "eCashout Pay-Outs (ACH)",
        commission: 1234.56,
      },
      ANR: {
        platformDescription: "Real Time eCashout (ANR)",
        commission: 7.5,
      },
      ANX: {
        platformDescription: "Real Time eCashout (ANX)",
        commission: 8.1,
      },
    };

    const summaries: BuyRateSummary[] = [
      {
        merchantName: merchantName1,
        platformCommission: commissionValues1,
      },
      {
        merchantName: merchantName2,
        platformCommission: commissionValues2,
      },
    ];

    generateBuyRateSummarySheet(
      summaries,
      customerName,
      {
        fromDate: { year: 2025, month: 5, day: 1 },
        toDate: { year: 2025, month: 5, day: 31 },
      },
      workbook
    );

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B7").value).toBe(customerName);
    expect(worksheet!.getCell("B8").value).toBe("Summary Settlement");
    expect(worksheet!.getCell("B9").value).toBe("May 01 2025 - May 31 2025");

    expect(worksheet!.getCell("D11").value).toBe("CAD");
    expect(worksheet!.getCell("G11").value).toBe("IDP");
    expect(worksheet!.getCell("H11").value).toBe("ETI");
    expect(worksheet!.getCell("I11").value).toBe("ETO");
    expect(worksheet!.getCell("J11").value).toBe("ACH");
    expect(worksheet!.getCell("K11").value).toBe("RFM");
    expect(worksheet!.getCell("L11").value).toBe("RTO");
    expect(worksheet!.getCell("M11").value).toBe("RTX");
    expect(worksheet!.getCell("N11").value).toBe("ETF");
    expect(worksheet!.getCell("O11").value).toBe("ANR");
    expect(worksheet!.getCell("P11").value).toBe("ANX");
    expect(worksheet!.getCell("Q11").value).toBe("Adjustments");

    expect(worksheet!.getCell("B13").value).toBe(merchantName1);
    expect(worksheet!.getCell("D13").value).toEqual({
      formula: "SUM(G13:P13)",
    });
    expect(worksheet!.getCell("G13").value).toBe(
      commissionValues1.IDP.commission
    );
    expect(worksheet!.getCell("H13").value).toBe(
      commissionValues1.ETI.commission
    );
    expect(worksheet!.getCell("I13").value).toBe(
      commissionValues1.ETO.commission
    );
    expect(worksheet!.getCell("J13").value).toBe(
      commissionValues1.ACH.commission
    );
    expect(worksheet!.getCell("K13").value).toBe(
      commissionValues1.RFM.commission
    );
    expect(worksheet!.getCell("L13").value).toBe(
      commissionValues1.RTO.commission
    );
    expect(worksheet!.getCell("M13").value).toBe(
      commissionValues1.RTX.commission
    );
    expect(worksheet!.getCell("N13").value).toBe(
      commissionValues1.ETF.commission
    );
    expect(worksheet!.getCell("O13").value).toBe(
      commissionValues1.ANR.commission
    );
    expect(worksheet!.getCell("P13").value).toBe(
      commissionValues1.ANX.commission
    );

    expect(worksheet!.getCell("B14").value).toBe(merchantName2);
    expect(worksheet!.getCell("D14").value).toEqual({
      formula: "SUM(G14:P14)",
    });
    expect(worksheet!.getCell("G14").value).toBe(
      commissionValues2.IDP.commission
    );
    expect(worksheet!.getCell("H14").value).toBe(
      commissionValues2.ETI.commission
    );
    expect(worksheet!.getCell("I14").value).toBe(
      commissionValues2.ETO.commission
    );
    expect(worksheet!.getCell("J14").value).toBe(
      commissionValues2.ACH.commission
    );
    expect(worksheet!.getCell("K14").value).toBe(
      commissionValues2.RFM.commission
    );
    expect(worksheet!.getCell("L14").value).toBe(
      commissionValues2.RTO.commission
    );
    expect(worksheet!.getCell("M14").value).toBe(
      commissionValues2.RTX.commission
    );
    expect(worksheet!.getCell("N14").value).toBe(
      commissionValues2.ETF.commission
    );
    expect(worksheet!.getCell("O14").value).toBe(
      commissionValues2.ANR.commission
    );
    expect(worksheet!.getCell("P14").value).toBe(
      commissionValues2.ANX.commission
    );

    expect(worksheet!.getCell("B16").value).toBe("Total");
    expect(worksheet!.getCell("D16").value).toEqual({
      formula: "SUM(D13:D14)",
    });
    expect(worksheet!.getCell("G16").value).toEqual({
      formula: "SUM(G13:G14)",
    });
    expect(worksheet!.getCell("H16").value).toEqual({
      formula: "SUM(H13:H14)",
    });
    expect(worksheet!.getCell("I16").value).toEqual({
      formula: "SUM(I13:I14)",
    });
    expect(worksheet!.getCell("J16").value).toEqual({
      formula: "SUM(J13:J14)",
    });
    expect(worksheet!.getCell("K16").value).toEqual({
      formula: "SUM(K13:K14)",
    });
    expect(worksheet!.getCell("L16").value).toEqual({
      formula: "SUM(L13:L14)",
    });
    expect(worksheet!.getCell("M16").value).toEqual({
      formula: "SUM(M13:M14)",
    });
    expect(worksheet!.getCell("N16").value).toEqual({
      formula: "SUM(N13:N14)",
    });
    expect(worksheet!.getCell("O16").value).toEqual({
      formula: "SUM(O13:O14)",
    });
    expect(worksheet!.getCell("P16").value).toEqual({
      formula: "SUM(P13:P14)",
    });
    expect(worksheet!.getCell("Q16").value).toEqual({
      formula: "SUM(Q13:Q14)",
    });
  });
});

describe("generateBuyRatePlatformSheet", () => {
  let workbook: ExcelJS.Workbook;

  const data = {
    customerTradingName: "Test Customer",
    fromDate: { year: 2025, month: 5, day: 1 },
    toDate: { year: 2025, month: 5, day: 31 },
    platform: "ETI",
    platformDescription: "Interac e-Transfer Pay-In (ETI)",
    merchantPlatformCommission: {
      merchantName: "Alpha",
      serviceNumber: "1234567890",
      rateDeterminingInterval: {
        fromDate: { year: 2025, month: 5, day: 1 },
        toDate: { year: 2025, month: 5, day: 31 },
      },
      feeCollectedFromMerchant: {
        meta: { transactionCount: 5, totalTransactionAmount: 1000 },
        totalCollectedFee: 50,
      },
      fees: {
        rates: {
          gatewayFee: 0.01,
          salesTierItem: { transactionFee: 0.02, salesFee: 0.03 },
        },
      },
    },
  } as unknown as {
    customerTradingName: string;
    fromDate: DateOnly;
    toDate: DateOnly;
    platform: Platform;
    platformDescription: string;
    merchantPlatformCommission: MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: {
        fromDate: DateOnly;
        toDate: DateOnly;
      };
    };
  };

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a platform worksheet with correct values when options is not provided", () => {
    generateBuyRatePlatformSheet(data, workbook);

    const worksheetName = `${data.merchantPlatformCommission.merchantName} ${data.platform}`;

    expect(validateWorksheetName).toHaveBeenCalledWith(worksheetName);

    const worksheet = workbook.getWorksheet(worksheetName);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("B7").value).toBe(data.customerTradingName);
    expect(worksheet!.getCell("B8").value).toBe(
      `${data.platformDescription} Settlement`
    );
    expect(worksheet!.getCell("B10").value).toBe("May 01 2025 - May 31 2025");

    expect(worksheet!.getCell("C12").value).toBe("# of Transactions");
    expect(worksheet!.getCell("D12").value).toBe("CAD");

    expect(worksheet!.getCell("B13").value).toBe(data.platformDescription);
    expect(worksheet!.getCell("C13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .transactionCount
    );
    expect(worksheet!.getCell("D13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .totalTransactionAmount
    );

    // No gateway fee included
    expect(worksheet!.getCell("C15").value).toBeNull();
    expect(worksheet!.getCell("D15").value).toBeNull();

    expect(worksheet!.getCell("C16").value).toBe("Transaction Fee");
    expect(worksheet!.getCell("D16").value).toEqual({
      formula: `C13*${data.merchantPlatformCommission.fees.rates.salesTierItem!.transactionFee}`,
    });

    expect(worksheet!.getCell("C17").value).toBe("Sales Fee");
    expect(worksheet!.getCell("D17").value).toEqual({
      formula: `D13*${data.merchantPlatformCommission.fees.rates.salesTierItem!.salesFee}`,
    });

    expect(worksheet!.getCell("C18").value).toBe("TOTAL COSTS");
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: `SUM(D15:D17)`,
    });

    expect(worksheet!.getCell("B20").value).toBe(
      `COSTS COLLECTED FROM ${data.merchantPlatformCommission.merchantName.toUpperCase()}`
    );
    expect(worksheet!.getCell("D20").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.totalCollectedFee
    );

    expect(worksheet!.getCell("B22").value).toBe(
      `DIFFERENCE PAYABLE TO ${data.customerTradingName}`
    );
    expect(worksheet!.getCell("D22").value).toEqual({
      formula: `D20-D18`,
    });
  });

  it("should create a platform worksheet with correct values when includeGatewayFee option is true", () => {
    generateBuyRatePlatformSheet(data, workbook, {
      includeGatewayFee: true,
    });

    const worksheetName = `${data.merchantPlatformCommission.merchantName} ${data.platform}`;

    expect(validateWorksheetName).toHaveBeenCalledWith(worksheetName);

    const worksheet = workbook.getWorksheet(worksheetName);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("B7").value).toBe(data.customerTradingName);
    expect(worksheet!.getCell("B8").value).toBe(
      `${data.platformDescription} Settlement`
    );
    expect(worksheet!.getCell("B10").value).toBe("May 01 2025 - May 31 2025");

    expect(worksheet!.getCell("C12").value).toBe("# of Transactions");
    expect(worksheet!.getCell("D12").value).toBe("CAD");

    expect(worksheet!.getCell("B13").value).toBe(data.platformDescription);
    expect(worksheet!.getCell("C13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .transactionCount
    );
    expect(worksheet!.getCell("D13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .totalTransactionAmount
    );

    expect(worksheet!.getCell("C15").value).toBe("Gateway Fee");
    expect(worksheet!.getCell("D15").value).toEqual({
      formula: `D13*${data.merchantPlatformCommission.fees.rates.gatewayFee}`,
    });

    expect(worksheet!.getCell("C16").value).toBe("Transaction Fee");
    expect(worksheet!.getCell("D16").value).toEqual({
      formula: `C13*${data.merchantPlatformCommission.fees.rates.salesTierItem!.transactionFee}`,
    });

    expect(worksheet!.getCell("C17").value).toBe("Sales Fee");
    expect(worksheet!.getCell("D17").value).toEqual({
      formula: `D13*${data.merchantPlatformCommission.fees.rates.salesTierItem!.salesFee}`,
    });

    expect(worksheet!.getCell("C18").value).toBe("TOTAL COSTS");
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: `SUM(D15:D17)`,
    });

    expect(worksheet!.getCell("B20").value).toBe(
      `COSTS COLLECTED FROM ${data.merchantPlatformCommission.merchantName.toUpperCase()}`
    );
    expect(worksheet!.getCell("D20").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.totalCollectedFee
    );

    expect(worksheet!.getCell("B22").value).toBe(
      `DIFFERENCE PAYABLE TO ${data.customerTradingName}`
    );
    expect(worksheet!.getCell("D22").value).toEqual({
      formula: `D20-D18`,
    });
  });

  it("should create a platform worksheet with transactionFee and salesFee set to 0 if the salesTierItem does not exist", () => {
    const noSalesTierData = {
      ...data,
      merchantPlatformCommission: {
        ...data.merchantPlatformCommission,
        fees: {
          rates: {
            gatewayFee: 0.01,
          },
        },
      },
    } as unknown as {
      customerTradingName: string;
      fromDate: DateOnly;
      toDate: DateOnly;
      platform: Platform;
      platformDescription: string;
      merchantPlatformCommission: MerchantPlatformCommission & {
        merchantName: string;
        serviceNumber: string;
        rateDeterminingInterval: {
          fromDate: DateOnly;
          toDate: DateOnly;
        };
      };
    };

    generateBuyRatePlatformSheet(noSalesTierData, workbook);

    const worksheetName = `${data.merchantPlatformCommission.merchantName} ${data.platform}`;

    expect(validateWorksheetName).toHaveBeenCalledWith(worksheetName);

    const worksheet = workbook.getWorksheet(worksheetName);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("B7").value).toBe(data.customerTradingName);
    expect(worksheet!.getCell("B8").value).toBe(
      `${data.platformDescription} Settlement`
    );
    expect(worksheet!.getCell("B10").value).toBe("May 01 2025 - May 31 2025");

    expect(worksheet!.getCell("C12").value).toBe("# of Transactions");
    expect(worksheet!.getCell("D12").value).toBe("CAD");

    expect(worksheet!.getCell("B13").value).toBe(data.platformDescription);
    expect(worksheet!.getCell("C13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .transactionCount
    );
    expect(worksheet!.getCell("D13").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.meta
        .totalTransactionAmount
    );

    // No gateway fee included
    expect(worksheet!.getCell("C15").value).toBeNull();
    expect(worksheet!.getCell("D15").value).toBeNull();

    expect(worksheet!.getCell("C16").value).toBe("Transaction Fee");
    expect(worksheet!.getCell("D16").value).toEqual({
      formula: `C13*0`,
    });

    expect(worksheet!.getCell("C17").value).toBe("Sales Fee");
    expect(worksheet!.getCell("D17").value).toEqual({
      formula: `D13*0`,
    });

    expect(worksheet!.getCell("C18").value).toBe("TOTAL COSTS");
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: `SUM(D15:D17)`,
    });

    expect(worksheet!.getCell("B20").value).toBe(
      `COSTS COLLECTED FROM ${data.merchantPlatformCommission.merchantName.toUpperCase()}`
    );
    expect(worksheet!.getCell("D20").value).toBe(
      data.merchantPlatformCommission.feeCollectedFromMerchant.totalCollectedFee
    );

    expect(worksheet!.getCell("B22").value).toBe(
      `DIFFERENCE PAYABLE TO ${data.customerTradingName}`
    );
    expect(worksheet!.getCell("D22").value).toEqual({
      formula: `D20-D18`,
    });
  });

  it("should propagate error if the worksheet name is invalid", () => {
    const errorMessage = "Worksheet name cannot contain invalid characters.";

    vi.mocked(validateWorksheetName).mockImplementationOnce(() => {
      throw new Error(errorMessage);
    });

    expect(() => {
      generateBuyRatePlatformSheet(data, workbook);
    }).toThrow(errorMessage);
  });
});
