/* eslint-disable @typescript-eslint/naming-convention */
import ExcelJS from "exceljs";

import { generateCombinationSummarySheet } from "./combination";

import type { BuyRateSummary } from "./buy-rate";
import type { CommissionRateSummary } from "./commission-rate";

describe("generateCombinationSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  it("should create a combination summary worksheet with correct values", () => {
    const commissionRateSummaries: CommissionRateSummary[] = [
      {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        transactionCount: 10,
        totalTransactionAmount: 1000,
        commission: 10,
      },
      {
        platformDescription: "Interac e-Transfer Request Money (RFM)",
        transactionCount: 25,
        totalTransactionAmount: 2500,
        commission: 25,
      },
      {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        transactionCount: 20,
        totalTransactionAmount: 2000,
        commission: 20,
      },
    ];
    const buyRateSummaries: BuyRateSummary[] = [
      {
        merchantName: "Merchant 1",
        platformCommission: {
          ETI: {
            platformDescription: "Interac e-Transfer Pay-In (ETI)",
            commission: 1,
          },
          RFM: {
            platformDescription: "Interac e-Transfer Request Money (RFM)",
            commission: 7,
          },
          RTO: {
            platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
            commission: 2,
          },
        },
      },
      {
        merchantName: "Merchant 2",
        platformCommission: {
          ETI: {
            platformDescription: "Interac e-Transfer Pay-In (ETI)",
            commission: 3,
          },
          RFM: {
            platformDescription: "Interac e-Transfer Request Money (RFM)",
            commission: 9,
          },
          RTO: {
            platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
            commission: 4,
          },
        },
      },
    ];
    const customerTradingName = "Test Customer";
    const interval = {
      fromDate: { year: 2025, month: 5, day: 1 },
      toDate: { year: 2025, month: 5, day: 31 },
    };

    generateCombinationSummarySheet({
      commissionRateSummaries,
      buyRateSummaries,
      customerTradingName,
      interval,
      workbook,
    });

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe(customerTradingName);
    expect(worksheet!.getCell("B8").value).toBe("Summary Settlement");
    expect(worksheet!.getCell("B9").value).toBe("May 1st 2025 - May 31st 2025");

    // Commission rate section
    expect(worksheet!.getCell("B12").value).toBe("Commission");
    expect(worksheet!.getCell("B13").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("C13").value).toBe(10);
    expect(worksheet!.getCell("B14").value).toBe(
      "Interac e-Transfer Request Money (RFM)"
    );
    expect(worksheet!.getCell("C14").value).toBe(25);
    expect(worksheet!.getCell("B15").value).toBe(
      "Real-time Interac e-Transfer Pay-Out (RTO)"
    );
    expect(worksheet!.getCell("C15").value).toBe(20);

    // Buy rate section
    expect(worksheet!.getCell("B17").value).toBe("Margin");

    expect(worksheet!.getCell("B18").value).toBe(
      "Merchant 1-Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("C18").value).toBe(1);

    expect(worksheet!.getCell("B19").value).toBe(
      "Merchant 1-Interac e-Transfer Request Money (RFM)"
    );
    expect(worksheet!.getCell("C19").value).toBe(7);

    expect(worksheet!.getCell("B20").value).toBe(
      "Merchant 1-Real-time Interac e-Transfer Pay-Out (RTO)"
    );
    expect(worksheet!.getCell("C20").value).toBe(2);

    expect(worksheet!.getCell("B22").value).toBe(
      "Merchant 2-Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("C22").value).toBe(3);

    expect(worksheet!.getCell("B23").value).toBe(
      "Merchant 2-Interac e-Transfer Request Money (RFM)"
    );
    expect(worksheet!.getCell("C23").value).toBe(9);

    expect(worksheet!.getCell("B24").value).toBe(
      "Merchant 2-Real-time Interac e-Transfer Pay-Out (RTO)"
    );
    expect(worksheet!.getCell("C24").value).toBe(4);

    expect(worksheet!.getCell("B27").value).toBe("Total");
    expect(worksheet!.getCell("C27").value).toEqual({
      formula: "SUM(C13:C24)",
    });
  });
});
