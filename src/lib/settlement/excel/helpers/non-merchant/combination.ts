import { excelNumberFormat } from "@constants/settlement";
import { type DateOnly, fromDateOnlyToStringLong } from "@utils/date-only";

import type { BuyRateSummary } from "./buy-rate";
import type { CommissionRateSummary } from "./commission-rate";
import type ExcelJS from "exceljs";

const generateCombinationSummarySheet = ({
  commissionRateSummaries,
  buyRateSummaries,
  customerTradingName,
  interval,
  workbook,
}: {
  commissionRateSummaries: CommissionRateSummary[];
  buyRateSummaries: BuyRateSummary[];
  customerTradingName: string;
  interval: { fromDate: DateOnly; toDate: DateOnly };
  workbook: ExcelJS.Workbook;
}) => {
  const { fromDate, toDate } = interval;

  const worksheet = workbook.addWorksheet("SUMMARY", {
    views: [{ showGridLines: false }],
  });

  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 40;
  worksheet.getColumn("C").width = 20;
  worksheet.getColumn("D").width = 20;
  worksheet.getColumn("E").width = 20;

  worksheet.getColumn("C").numFmt = excelNumberFormat;

  worksheet.mergeCells("B6:D6");
  worksheet.getCell("B6").alignment = { horizontal: "center" };
  worksheet.getCell("B6").font = { bold: true };
  worksheet.getCell("B6").value = customerTradingName;

  worksheet.mergeCells("B8:D8");
  worksheet.getCell("B8").alignment = { horizontal: "center" };
  worksheet.getCell("B8").font = {
    bold: true,
  };
  worksheet.getCell("B8").value = "Summary Settlement";

  worksheet.mergeCells("B9:D9");
  worksheet.getCell("B9").alignment = { horizontal: "center" };
  worksheet.getCell("B9").font = {
    bold: true,
  };
  worksheet.getCell("B9").value =
    `${fromDateOnlyToStringLong(fromDate, true)} - ${fromDateOnlyToStringLong(toDate, true)}`;

  worksheet.getCell("B11").border = { bottom: { style: "thick" } };

  worksheet.getCell("C11").alignment = {
    horizontal: "right",
  };
  worksheet.getCell("C11").border = { bottom: { style: "thick" } };
  worksheet.getCell("C11").font = { bold: true };
  worksheet.getCell("C11").value = "CAD";

  worksheet.getCell("D11").alignment = { horizontal: "center" };
  worksheet.getCell("D11").border = { bottom: { style: "thick" } };
  worksheet.getCell("D11").font = { bold: true };

  worksheet.getCell("E11").alignment = {
    wrapText: true,
    horizontal: "center",
  };
  worksheet.getCell("E11").border = { bottom: { style: "thick" } };
  worksheet.getCell("E11").font = { bold: true };

  worksheet.getCell("B12").font = { bold: true };
  worksheet.getCell("B12").value = "Commission";

  const initialRow = 13;
  let currentRow = initialRow;

  for (const summary of commissionRateSummaries) {
    const { platformDescription, commission } = summary;

    worksheet.getCell(`B${currentRow}`).value = platformDescription;

    worksheet.getCell(`C${currentRow}`).value = commission;

    currentRow += 1;
  }

  // Add a blank row before Buy Rate summary
  currentRow += 1;

  worksheet.getCell(`B${currentRow}`).font = { bold: true };
  worksheet.getCell(`B${currentRow}`).value = "Margin";
  currentRow += 1;

  for (const summary of buyRateSummaries) {
    const { merchantName, platformCommission } = summary;

    for (const { platformDescription, commission } of Object.values(
      platformCommission
    )) {
      worksheet.getCell(`B${currentRow}`).value =
        `${merchantName}-${platformDescription}`;

      worksheet.getCell(`C${currentRow}`).value = commission;

      currentRow += 1;
    }

    // Add a blank row after each merchant's summary
    currentRow += 1;
  }

  const finalRow = currentRow + 1;

  worksheet.getCell(`B${finalRow}`).font = { bold: true };
  worksheet.getCell(`B${finalRow}`).value = "Total";

  worksheet.getCell(`C${finalRow}`).font = { bold: true };
  worksheet.getCell(`C${finalRow}`).value = {
    formula: `SUM(C${initialRow}:C${currentRow - 2})`,
  };
};

export { generateCombinationSummarySheet };
