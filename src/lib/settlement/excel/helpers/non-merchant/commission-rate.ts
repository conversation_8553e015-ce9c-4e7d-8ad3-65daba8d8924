import { excelNumberFormat } from "@constants/settlement";
import { type DateOnly, fromDateOnlyToStringLong } from "@utils/date-only";

import type { Platform } from "@constants/transactions/platform";
import type {
  NonMerchantFeesBreakdown,
  PlatformSettlementDetail,
} from "@lib/settlement/functions/workers/types";
import type ExcelJS from "exceljs";

type CommissionRateSummary = {
  platformDescription: string;
  transactionCount: number;
  totalTransactionAmount: number;
  commission: number;
};

const generateCommissionRateSummarySheet = (
  summaries: CommissionRateSummary[],
  customerTradingName: string,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { fromDate, toDate } = interval;

  const worksheet = workbook.addWorksheet("SUMMARY", {
    views: [{ showGridLines: false }],
  });

  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 40;
  worksheet.getColumn("C").width = 20;
  worksheet.getColumn("D").width = 20;
  worksheet.getColumn("E").width = 20;

  worksheet.getColumn("D").numFmt = excelNumberFormat;
  worksheet.getColumn("E").numFmt = excelNumberFormat;

  worksheet.mergeCells("B6:D6");
  worksheet.getCell("B6").alignment = { horizontal: "center" };
  worksheet.getCell("B6").font = { bold: true };
  worksheet.getCell("B6").value = customerTradingName;

  worksheet.mergeCells("B8:D8");
  worksheet.getCell("B8").alignment = { horizontal: "center" };
  worksheet.getCell("B8").font = {
    bold: true,
  };
  worksheet.getCell("B8").value = "Summary Settlement";

  worksheet.mergeCells("B9:D9");
  worksheet.getCell("B9").alignment = { horizontal: "center" };
  worksheet.getCell("B9").font = {
    bold: true,
  };
  worksheet.getCell("B9").value =
    `${fromDateOnlyToStringLong(fromDate, true)} - ${fromDateOnlyToStringLong(toDate, true)}`;

  worksheet.getCell("B11").border = { bottom: { style: "thick" } };

  worksheet.getCell("C11").alignment = { horizontal: "center" };
  worksheet.getCell("C11").border = { bottom: { style: "thick" } };
  worksheet.getCell("C11").font = { bold: true };
  worksheet.getCell("C11").value = "# of Transactions";

  worksheet.getCell("D11").alignment = { horizontal: "center" };
  worksheet.getCell("D11").border = { bottom: { style: "thick" } };
  worksheet.getCell("D11").font = { bold: true };
  worksheet.getCell("D11").value = "Transactions Value";

  worksheet.getCell("E11").alignment = {
    wrapText: true,
    horizontal: "center",
  };
  worksheet.getCell("E11").border = { bottom: { style: "thick" } };
  worksheet.getCell("E11").font = { bold: true };
  worksheet.getCell("E11").value = "Amount Payable/ (Receivable)";

  const initialRow = 12;
  let currentRow = initialRow;

  for (const {
    platformDescription,
    transactionCount,
    totalTransactionAmount,
    commission,
  } of summaries) {
    worksheet.getCell(`B${currentRow}`).value = platformDescription;

    worksheet.getCell(`C${currentRow}`).alignment = {
      horizontal: "center",
    };
    worksheet.getCell(`C${currentRow}`).value = transactionCount;

    worksheet.getCell(`D${currentRow}`).value = totalTransactionAmount;

    worksheet.getCell(`E${currentRow}`).value = commission;

    currentRow += 1;
  }

  const finalRow = currentRow + 1;

  worksheet.getCell(`B${finalRow}`).alignment = {
    horizontal: "left",
  };
  worksheet.getCell(`B${finalRow}`).font = { bold: true };
  worksheet.getCell(`B${finalRow}`).value = "Total";

  worksheet.getCell(`C${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`C${finalRow}`).font = { bold: true };
  worksheet.getCell(`C${finalRow}`).value = {
    formula: `SUM(C${initialRow}:C${currentRow - 1})`,
  };

  worksheet.getCell(`D${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`D${finalRow}`).font = { bold: true };
  worksheet.getCell(`D${finalRow}`).value = {
    formula: `SUM(D${initialRow}:D${currentRow - 1})`,
  };

  worksheet.getCell(`E${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`E${finalRow}`).font = { bold: true };
  worksheet.getCell(`E${finalRow}`).value = {
    formula: `SUM(E${initialRow}:E${currentRow - 1})`,
  };
};

const generateCommissionRatePlatformSheet = ({
  customerTradingName,
  platform,
  platformSettlement,
  workbook,
  options,
}: {
  customerTradingName: string;
  platform: Platform;
  platformSettlement: PlatformSettlementDetail;
  workbook: ExcelJS.Workbook;
  options: {
    feeType: "transactionFee" | "salesFee";
  };
}): void => {
  const feesBreakdown =
    platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

  const { fromDate, toDate } = feesBreakdown.interval;

  const worksheet = workbook.addWorksheet(platform, {
    views: [{ showGridLines: false }],
  });

  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 30;
  worksheet.getColumn("C").width = 20;
  worksheet.getColumn("D").width = 20;
  worksheet.getColumn("E").width = 20;

  worksheet.getColumn("D").numFmt = excelNumberFormat;
  worksheet.getColumn("E").numFmt = excelNumberFormat;

  worksheet.mergeCells("B6:D6");
  worksheet.getCell("B6").alignment = { horizontal: "center" };
  worksheet.getCell("B6").font = { bold: true };
  worksheet.getCell("B6").value = customerTradingName;

  worksheet.mergeCells("B7:D7");
  worksheet.getCell("B7").alignment = { horizontal: "center" };
  worksheet.getCell("B7").font = { bold: true };
  worksheet.getCell("B7").value = platformSettlement.platformDescription;

  worksheet.mergeCells("B9:D9");
  worksheet.getCell("B9").alignment = { horizontal: "center" };
  worksheet.getCell("B9").font = { bold: true };
  worksheet.getCell("B9").value =
    `${fromDateOnlyToStringLong(fromDate)} - ${fromDateOnlyToStringLong(toDate)}`;

  worksheet.getCell("C12").alignment = { horizontal: "center" };
  worksheet.getCell("C12").font = { bold: true };
  worksheet.getCell("C12").value = "# of Transactions";

  worksheet.getCell("D12").alignment = { horizontal: "center" };
  worksheet.getCell("D12").font = { bold: true };
  worksheet.getCell("D12").value = "CAD";

  worksheet.getCell("E12").alignment = { horizontal: "center" };
  worksheet.getCell("E12").font = { bold: true };
  worksheet.getCell("E12").value = "Commission";

  // Sort by ascending alphabetical order of merchant names
  const sortedMerchantBreakdown = Object.values(
    feesBreakdown.merchantBreakdown
  ).sort((a, b) => a.merchantName.localeCompare(b.merchantName, "en"));

  const initialRow = 14;
  let currentRow = initialRow;

  for (const merchantData of sortedMerchantBreakdown) {
    const { merchantName } = merchantData;
    const { transactionCount, totalTransactionAmount } =
      merchantData.feeCollectedFromMerchant.meta;
    const salesFee = merchantData.fees.rates.transactionTierItem?.salesFee ?? 0;
    const transactionFee =
      merchantData.fees.rates.transactionTierItem?.transactionFee ?? 0;

    worksheet.getCell(`B${currentRow}`).alignment = { horizontal: "left" };
    worksheet.getCell(`B${currentRow}`).value = merchantName;

    worksheet.getCell(`C${currentRow}`).alignment = {
      horizontal: "center",
    };
    worksheet.getCell(`C${currentRow}`).value = transactionCount;

    worksheet.getCell(`D${currentRow}`).value = totalTransactionAmount;

    // Only provide the formula and not the result,
    // so that the numbers cannot change when the formula is reapplied
    worksheet.getCell(`E${currentRow}`).value = {
      formula:
        options.feeType === "transactionFee"
          ? `C${currentRow}*${transactionFee}`
          : `D${currentRow}*${salesFee}`,
    };

    currentRow += 1;
  }

  const finalRow = currentRow + 2;

  worksheet.getCell(`B${finalRow}`).alignment = {
    horizontal: "left",
  };
  worksheet.getCell(`B${finalRow}`).font = { bold: true };
  worksheet.getCell(`B${finalRow}`).value = "Total";

  worksheet.getCell(`C${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`C${finalRow}`).font = { bold: true };
  worksheet.getCell(`C${finalRow}`).value = {
    formula: `SUM(C${initialRow}:C${currentRow - 1})`,
  };

  worksheet.getCell(`D${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`D${finalRow}`).font = { bold: true };
  worksheet.getCell(`D${finalRow}`).value = {
    formula: `SUM(D${initialRow}:D${currentRow - 1})`,
  };

  worksheet.getCell(`E${finalRow}`).alignment = {
    horizontal: "center",
  };
  worksheet.getCell(`E${finalRow}`).font = { bold: true };
  worksheet.getCell(`E${finalRow}`).value = {
    formula: `SUM(E${initialRow}:E${currentRow - 1})`,
  };
};

export {
  type CommissionRateSummary,
  generateCommissionRateSummarySheet,
  generateCommissionRatePlatformSheet,
};
