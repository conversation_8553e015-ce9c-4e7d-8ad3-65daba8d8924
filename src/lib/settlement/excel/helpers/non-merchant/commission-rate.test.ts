/* eslint-disable @typescript-eslint/naming-convention */

import ExcelJS from "exceljs";

import {
  generateCommissionRateSummarySheet,
  generateCommissionRatePlatformSheet,
  type CommissionRateSummary,
} from "./commission-rate";

import type { PlatformSettlementDetail } from "@lib/settlement/functions/workers/types";

describe("generateCommissionRateSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a summary worksheet with correct values", async () => {
    const summaries: CommissionRateSummary[] = [
      {
        platformDescription: "Interac Online Pay-In (IDP)",
        transactionCount: 0,
        totalTransactionAmount: 0,
        commission: 0,
      },
      {
        platformDescription: "Interac e-Transfer Pay-In (ETI)",
        transactionCount: 5,
        totalTransactionAmount: 1000,
        commission: 50,
      },
      {
        platformDescription: "Interac e-Transfer Request Money (RFM)",
        transactionCount: 3,
        totalTransactionAmount: 500,
        commission: 25,
      },
      {
        platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
        transactionCount: 10,
        totalTransactionAmount: 2000,
        commission: 100,
      },
      {
        platformDescription: "Real Time eCashout (ANX)",
        transactionCount: 4,
        totalTransactionAmount: 800,
        commission: 40,
      },
    ];

    const customerTradingName = "Test Customer";

    const interval = {
      fromDate: {
        year: 2025,
        month: 1,
        day: 1,
      },
      toDate: {
        year: 2025,
        month: 1,
        day: 31,
      },
    };

    generateCommissionRateSummarySheet(
      summaries,
      customerTradingName,
      interval,
      workbook
    );

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B9").value).toBe(
      "January 1st 2025 - January 31st 2025"
    );

    expect(worksheet!.getCell("B12").value).toBe("Interac Online Pay-In (IDP)");
    expect(worksheet!.getCell("C12").value).toBe(0);
    expect(worksheet!.getCell("D12").value).toBe(0);
    expect(worksheet!.getCell("E12").value).toBe(0);

    expect(worksheet!.getCell("B13").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("C13").value).toBe(5);
    expect(worksheet!.getCell("D13").value).toBe(1000);
    expect(worksheet!.getCell("E13").value).toBe(50);

    expect(worksheet!.getCell("B14").value).toBe(
      "Interac e-Transfer Request Money (RFM)"
    );
    expect(worksheet!.getCell("C14").value).toBe(3);
    expect(worksheet!.getCell("D14").value).toBe(500);
    expect(worksheet!.getCell("E14").value).toBe(25);

    expect(worksheet!.getCell("B15").value).toBe(
      "Real-time Interac e-Transfer Pay-Out (RTO)"
    );
    expect(worksheet!.getCell("C15").value).toBe(10);
    expect(worksheet!.getCell("D15").value).toBe(2000);
    expect(worksheet!.getCell("E15").value).toBe(100);

    expect(worksheet!.getCell("B16").value).toBe("Real Time eCashout (ANX)");
    expect(worksheet!.getCell("C16").value).toBe(4);
    expect(worksheet!.getCell("D16").value).toBe(800);
    expect(worksheet!.getCell("E16").value).toBe(40);

    expect(worksheet!.getCell("C18").value).toEqual({
      formula: "SUM(C12:C16)",
    });
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D12:D16)",
    });
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E12:E16)",
    });
  });
});

describe("generateCommissionRatePlatformSheet", () => {
  let workbook: ExcelJS.Workbook;

  const customerTradingName = "Test Customer";
  const platform = "ETI";

  const platformSettlement = {
    platformDescription: "Interac e-Transfer Pay-In (ETI)",
    feesBreakdown: {
      interval: {
        fromDate: {
          year: 2025,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2025,
          month: 1,
          day: 31,
        },
      },
      merchantBreakdown: {
        123: {
          merchantName: "Alpha",
          feeCollectedFromMerchant: {
            meta: { transactionCount: 2, totalTransactionAmount: 100 },
          },
          fees: {
            rates: {
              transactionTierItem: { salesFee: 0.025, transactionFee: 0.2 },
            },
          },
        },
        456: {
          merchantName: "Beta",
          feeCollectedFromMerchant: {
            meta: { transactionCount: 3, totalTransactionAmount: 200 },
          },
          fees: {
            rates: {
              transactionTierItem: { salesFee: 0.015, transactionFee: 0.4 },
            },
          },
        },
      },
    },
  } as unknown as PlatformSettlementDetail;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  it("should create a platform worksheet with the correct values when feeType is salesFee", async () => {
    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: { feeType: "salesFee" },
    });

    const worksheet = workbook.getWorksheet("ETI");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B9").value).toBe(
      "January 01 2025 - January 31 2025"
    );

    expect(worksheet!.getCell("B14").value).toBe("Alpha");
    expect(worksheet!.getCell("C14").value).toBe(2);
    expect(worksheet!.getCell("D14").value).toBe(100);
    expect(worksheet!.getCell("E14").value).toEqual({
      formula: "D14*0.025",
    });

    expect(worksheet!.getCell("B15").value).toBe("Beta");
    expect(worksheet!.getCell("C15").value).toBe(3);
    expect(worksheet!.getCell("D15").value).toBe(200);
    expect(worksheet!.getCell("E15").value).toEqual({
      formula: "D15*0.015",
    });

    expect(worksheet!.getCell("C18").value).toEqual({
      formula: "SUM(C14:C15)",
    });
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D14:D15)",
    });
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E14:E15)",
    });
  });

  it("should create a platform worksheet with the correct values when feeType is transactionFee", async () => {
    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: { feeType: "transactionFee" },
    });

    const worksheet = workbook.getWorksheet("ETI");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B9").value).toBe(
      "January 01 2025 - January 31 2025"
    );

    expect(worksheet!.getCell("B14").value).toBe("Alpha");
    expect(worksheet!.getCell("C14").value).toBe(2);
    expect(worksheet!.getCell("D14").value).toBe(100);
    expect(worksheet!.getCell("E14").value).toEqual({
      formula: "C14*0.2",
    });

    expect(worksheet!.getCell("B15").value).toBe("Beta");
    expect(worksheet!.getCell("C15").value).toBe(3);
    expect(worksheet!.getCell("D15").value).toBe(200);
    expect(worksheet!.getCell("E15").value).toEqual({
      formula: "C15*0.4",
    });

    expect(worksheet!.getCell("C18").value).toEqual({
      formula: "SUM(C14:C15)",
    });
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D14:D15)",
    });
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E14:E15)",
    });
  });

  it("should create a platform worksheet where the merchants are sorted alphabetically", async () => {
    const platformSettlement = {
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      feesBreakdown: {
        interval: {
          fromDate: {
            year: 2025,
            month: 1,
            day: 1,
          },
          toDate: {
            year: 2025,
            month: 1,
            day: 31,
          },
        },
        merchantBreakdown: {
          456: {
            merchantName: "Beta",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 3, totalTransactionAmount: 200 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.015, transactionFee: 0.4 },
              },
            },
          },
          123: {
            merchantName: "Alpha",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 2, totalTransactionAmount: 100 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.025, transactionFee: 0.2 },
              },
            },
          },
          2: {
            merchantName: "charlie",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 1, totalTransactionAmount: 50 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.01, transactionFee: 0.1 },
              },
            },
          },
          6: {
            merchantName: "1Delta",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 4, totalTransactionAmount: 300 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.03, transactionFee: 0.5 },
              },
            },
          },
        },
      },
    } as unknown as PlatformSettlementDetail;

    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: { feeType: "transactionFee" },
    });

    const worksheet = workbook.getWorksheet("ETI");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B9").value).toBe(
      "January 01 2025 - January 31 2025"
    );

    expect(worksheet!.getCell("B14").value).toBe("1Delta");
    expect(worksheet!.getCell("C14").value).toBe(4);
    expect(worksheet!.getCell("D14").value).toBe(300);
    expect(worksheet!.getCell("E14").value).toEqual({
      formula: "C14*0.5",
    });

    expect(worksheet!.getCell("B15").value).toBe("Alpha");
    expect(worksheet!.getCell("C15").value).toBe(2);
    expect(worksheet!.getCell("D15").value).toBe(100);
    expect(worksheet!.getCell("E15").value).toEqual({
      formula: "C15*0.2",
    });

    expect(worksheet!.getCell("B16").value).toBe("Beta");
    expect(worksheet!.getCell("C16").value).toBe(3);
    expect(worksheet!.getCell("D16").value).toBe(200);
    expect(worksheet!.getCell("E16").value).toEqual({
      formula: "C16*0.4",
    });

    expect(worksheet!.getCell("B17").value).toBe("charlie");
    expect(worksheet!.getCell("C17").value).toBe(1);
    expect(worksheet!.getCell("D17").value).toBe(50);
    expect(worksheet!.getCell("E17").value).toEqual({
      formula: "C17*0.1",
    });

    expect(worksheet!.getCell("C20").value).toEqual({
      formula: "SUM(C14:C17)",
    });
    expect(worksheet!.getCell("D20").value).toEqual({
      formula: "SUM(D14:D17)",
    });
    expect(worksheet!.getCell("E20").value).toEqual({
      formula: "SUM(E14:E17)",
    });
  });

  it("should use 0 if sales fee is not found", async () => {
    const platformSettlement = {
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      feesBreakdown: {
        interval: {
          fromDate: {
            year: 2025,
            month: 1,
            day: 1,
          },
          toDate: {
            year: 2025,
            month: 1,
            day: 31,
          },
        },
        merchantBreakdown: {
          123: {
            merchantName: "Alpha",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 2, totalTransactionAmount: 100 },
            },
            fees: {
              rates: {},
            },
          },
          456: {
            merchantName: "Beta",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 3, totalTransactionAmount: 200 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.015, transactionFee: 0.4 },
              },
            },
          },
        },
      },
    } as unknown as PlatformSettlementDetail;

    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: { feeType: "salesFee" },
    });

    const worksheet = workbook.getWorksheet("ETI");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B9").value).toBe(
      "January 01 2025 - January 31 2025"
    );

    expect(worksheet!.getCell("B14").value).toBe("Alpha");
    expect(worksheet!.getCell("C14").value).toBe(2);
    expect(worksheet!.getCell("D14").value).toBe(100);
    expect(worksheet!.getCell("E14").value).toEqual({
      formula: "D14*0",
    });

    expect(worksheet!.getCell("B15").value).toBe("Beta");
    expect(worksheet!.getCell("C15").value).toBe(3);
    expect(worksheet!.getCell("D15").value).toBe(200);
    expect(worksheet!.getCell("E15").value).toEqual({
      formula: "D15*0.015",
    });

    expect(worksheet!.getCell("C18").value).toEqual({
      formula: "SUM(C14:C15)",
    });
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D14:D15)",
    });
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E14:E15)",
    });
  });

  it("should use 0 if transactionFee is not found", async () => {
    const platformSettlement = {
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      feesBreakdown: {
        interval: {
          fromDate: {
            year: 2025,
            month: 1,
            day: 1,
          },
          toDate: {
            year: 2025,
            month: 1,
            day: 31,
          },
        },
        merchantBreakdown: {
          123: {
            merchantName: "Alpha",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 2, totalTransactionAmount: 100 },
            },
            fees: {
              rates: {
                transactionTierItem: { salesFee: 0.025, transactionFee: 0.2 },
              },
            },
          },
          456: {
            merchantName: "Beta",
            feeCollectedFromMerchant: {
              meta: { transactionCount: 3, totalTransactionAmount: 200 },
            },
            fees: {
              rates: {},
            },
          },
        },
      },
    } as unknown as PlatformSettlementDetail;

    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: { feeType: "transactionFee" },
    });

    const worksheet = workbook.getWorksheet("ETI");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B9").value).toBe(
      "January 01 2025 - January 31 2025"
    );

    expect(worksheet!.getCell("B14").value).toBe("Alpha");
    expect(worksheet!.getCell("C14").value).toBe(2);
    expect(worksheet!.getCell("D14").value).toBe(100);
    expect(worksheet!.getCell("E14").value).toEqual({
      formula: "C14*0.2",
    });

    expect(worksheet!.getCell("B15").value).toBe("Beta");
    expect(worksheet!.getCell("C15").value).toBe(3);
    expect(worksheet!.getCell("D15").value).toBe(200);
    expect(worksheet!.getCell("E15").value).toEqual({
      formula: "C15*0",
    });

    expect(worksheet!.getCell("C18").value).toEqual({
      formula: "SUM(C14:C15)",
    });
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D14:D15)",
    });
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E14:E15)",
    });
  });
});
