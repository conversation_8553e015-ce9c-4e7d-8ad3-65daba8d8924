import { platformCodes } from "@constants/settlement";
import {
  type SummaryInformation,
  type ExcelConfig,
} from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach } from "vitest";

import { createSummaryExcelFile } from "./create-summary-platform-file";

const defaultConfig: ExcelConfig = {
  platformCode: "SUMMARY",
  customerTradingName: "Test Customer",
  period: {
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
  },
  fileName: "test-summary.xlsx",
  folderPath: "/test/path",
  data: {
    labelName: "Summary",
    transactionCount: 100,
    totalTransactionAmount: 10_000,
    refundCount: 5,
    totalRefundAmount: 500,
    gatewayFee: 50,
    transactionFee: 30,
    salesFee: 20,
    refundFee: 2.5,
    totalFailedAmount: 0,
    endBalance: 9000,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnAmountRTO_R: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnAmountRTO: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnCountRTO: 0,
    isAdjusted: false,
    adjustments: [],
  },
  customer: {
    entityName: "Gigadat",
    entityLogoName: "logo-gigadat.png",
  },
};

const createMockSummaryInformation = (): SummaryInformation => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  ETI: {
    labelName: "Interac e-Transfer Pay-In (ETI)",
    transactionCount: 100,
    totalTransactionAmount: 10_000,
    refundCount: 5,
    totalRefundAmount: 500,
    gatewayFee: 50,
    transactionFee: 30,
    salesFee: 20,
    refundFee: 2.5,
    totalFailedAmount: 0,
    endBalance: 9000,
    displaySequence: 1,
    minimumFeeTotal: 0,
    adjustments: [],
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  KYC: {
    labelName: "Know Your Customer (KYC)",
    transactionCount: 25,
    totalTransactionAmount: 1000,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 0,
    displaySequence: 2,
    minimumFeeTotal: 0,
    adjustments: [],
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  IDP: {
    labelName: "Interac Direct Payment (IDP)",
    transactionCount: 50,
    totalTransactionAmount: 5000,
    refundCount: 2,
    totalRefundAmount: 200,
    gatewayFee: 25,
    transactionFee: 15,
    salesFee: 10,
    refundFee: 1,
    totalFailedAmount: 0,
    endBalance: 4500,
    displaySequence: 3,
    minimumFeeTotal: 0,
    adjustments: [],
  },
});

describe("createSummaryExcelFile", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  it("should create summary worksheet with correct name", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();
  });

  it("should include all platforms in platform sequence", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("B13").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
    expect(worksheet!.getCell("B14").value).toBe("Know Your Customer (KYC)");
  });

  it("should set correct transaction count for each platform", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("C13").value).toBe(100);
    expect(worksheet!.getCell("C14").value).toBe(25);
  });

  it("should set transaction value to 0 for KYC", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("D14").value).toBe(0);
  });

  it("should calculate transaction value for non-KYC platforms", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    // Expected: 10000 - 0 = 10000 (no failed amount)
    expect(worksheet!.getCell("D13").value).toBe(10_000);
  });

  it("should calculate total payable for pay-in platforms (ETI)", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    // Expected: 10000 - 30 - 50 - 20 - 2.5 - 500 = 9397.5
    expect(worksheet!.getCell("E13").value).toBe(9397.5);
  });

  it("should calculate total payable for KYC platform", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    // Expected: -1000 (negative because KYC is a cost to merchant)
    expect(worksheet!.getCell("E14").value).toBe(-1000);
  });

  it("should add net payout row with formula", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    // Net payout should be at row 17 (13 + 2 platforms + 2 gap rows)
    expect(worksheet!.getCell("D17").value).toBe("Net Payout");
    expect(worksheet!.getCell("E17").value).toEqual({
      formula: "SUM(E13:E15)",
    });
  });

  it("should set correct cell alignments for transaction count", () => {
    const summaryInformation = createMockSummaryInformation();

    createSummaryExcelFile(workbook, defaultConfig, summaryInformation);

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("C13").alignment).toEqual({
      horizontal: "center",
    });
  });

  it("should handle platforms without displaySequence gracefully", () => {
    const summaryInformationWithMissingSequence: SummaryInformation = {
      ...createMockSummaryInformation(),
      newPlatform: {
        labelName: "New Platform",
        transactionCount: 75,
        totalTransactionAmount: 7500,
        refundCount: 3,
        totalRefundAmount: 300,
        gatewayFee: 40,
        transactionFee: 25,
        salesFee: 15,
        refundFee: 2,
        totalFailedAmount: 0,
        endBalance: 7000,
        displaySequence: undefined, // Intentionally missing displaySequence
        minimumFeeTotal: 0,
        adjustments: [],
      },
    };

    expect(() => {
      createSummaryExcelFile(
        workbook,
        defaultConfig,
        summaryInformationWithMissingSequence
      );
    }).not.toThrow();

    const worksheet = workbook.getWorksheet(platformCodes.summary);
    expect(worksheet).toBeDefined();

    // The new platform should appear at the end (after platforms with displaySequence)
    // Since it has no displaySequence, it gets Number.MAX_SAFE_INTEGER and appears last
    expect(worksheet!.getCell("B15").value).toBe("New Platform");
  });
});
