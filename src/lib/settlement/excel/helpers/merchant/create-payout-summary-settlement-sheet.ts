/* eslint-disable @typescript-eslint/naming-convention */
import {
  excelNumberFormat,
  excelSummaryColumns,
  platformCodes,
} from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { format } from "date-fns";

import { createAdjustmentsSection } from "./create-adjustment-section";
import { setupWorksheetLogo } from "./setup-worksheet-logo";

import type ExcelJS from "exceljs";

export const createPayOutSummarySheet = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): void => {
  const {
    customerTradingName,
    platformCode,
    customer,
    data: { labelName, meta, adjustments },
    period: { fromDate, toDate },
  } = config;

  const rates = meta?.feesBreakdown?.[0]?.meta?.rates;
  const transactionFee = rates?.transactionFee ?? 0;

  const rejectOneTierTransactionFee = rates?.rejectOneTierItem?.transactionFee;

  const summaryWorksheet = workbook.addWorksheet(
    platformCodes.summary as string,
    {
      views: [{ showGridLines: false }],
    }
  );

  summaryWorksheet.columns = excelSummaryColumns;

  setupWorksheetLogo(workbook, summaryWorksheet, customer);

  summaryWorksheet.getColumn("D").numFmt = excelNumberFormat;

  summaryWorksheet.mergeCells("B6:D6");
  summaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B6").value = customerTradingName;

  summaryWorksheet.mergeCells("B7:D7");
  summaryWorksheet.getCell("B7").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B7").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B7").value = labelName;

  summaryWorksheet.mergeCells("B9:D9");
  summaryWorksheet.getCell("B9").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B9").font = { bold: true };
  summaryWorksheet.getCell("B9").value =
    `${format(fromDate, "MMMM dd")} - ${format(toDate, "MMMM dd")}`;

  let rowNumber = 12;

  summaryWorksheet.getCell(`B${rowNumber}`).border = {
    bottom: { style: "thick" },
  };

  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "# of Transactions";

  summaryWorksheet.getCell(`D${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`D${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`D${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = "CAD";

  rowNumber++;

  const platformNameMap: Record<string, string> = {
    RTX: "Realtime Out",
    ETO: "Interac e-Transfer Pay-Outs",
    ACH: "Express Connect ACH Pay-Outs",
    RTO: "Real Time E-Transfer-Out",
    ANR: "Express Connect ANR Pay-Outs",
    ANX: "Express Connect ANX Pay-Outs",
  };

  summaryWorksheet.getCell(`B${rowNumber}`).value =
    platformNameMap[platformCode] ?? platformCode;

  if (platformCode === platformCodes.rto) {
    summaryWorksheet.getCell(`C${rowNumber}`).value = {
      formula: "COUNT(Transactions!C:C)-COUNT(RTO_R!F:F)",
    };
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula:
        "SUM(Transactions!F:F)-SUM(Failed!F:F)-SUM(RTO_R!F:F)-SUM(RTO_P!F:F)",
    };
    summaryWorksheet.getCell(`B${rowNumber + 1}`).value =
      "Rejected1 Transactions";
    summaryWorksheet.getCell(`C${rowNumber + 1}`).value = {
      formula: "COUNT(REJECTED1!F:F)",
    };
    rowNumber++;
  } else {
    summaryWorksheet.getCell(`C${rowNumber}`).value = {
      formula: "COUNT(Transactions!C:C)",
    };
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: "SUM(Transactions!F:F)-SUM(Failed!F:F)",
    };
  }

  rowNumber += 2;

  const effectiveTransactionFee =
    typeof rejectOneTierTransactionFee === "number"
      ? rejectOneTierTransactionFee
      : (transactionFee ?? 0);

  const transactionFeeFormula =
    platformCode === platformCodes.rto
      ? `C13*${transactionFee} + C14*${effectiveTransactionFee}`
      : `C13*${transactionFee}`;

  summaryWorksheet.getCell(`C${rowNumber}`).value = "Transaction Fee";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: transactionFeeFormula,
  };

  rowNumber++;

  const rowNumberAfterAdjustmentsAdded = createAdjustmentsSection(
    summaryWorksheet,
    adjustments,
    rowNumber
  );

  rowNumber = rowNumberAfterAdjustmentsAdded + 1;

  summaryWorksheet.getCell(`C${rowNumber}`).value = "TOTAL COSTS";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = {
    bold: true,
  };

  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `SUM(D15:D${rowNumber - 1})`,
  };

  rowNumber++;

  summaryWorksheet.mergeCells(`B${rowNumber}:C${rowNumber}`);
  summaryWorksheet.getCell(`C${rowNumber}`).value =
    "Total to Net against Pay-In (CAD)";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = {
    bold: true,
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `D13+D${rowNumber - 1}`,
  };
};
