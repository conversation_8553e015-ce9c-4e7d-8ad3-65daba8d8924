/* eslint-disable @typescript-eslint/naming-convention */
import { readFileSync } from "node:fs";

import { entities } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";
import {
  excelNumberFormat,
  excelSummaryColumns,
  platformCodes,
} from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { format } from "date-fns";

import type ExcelJS from "exceljs";

export const createPayOutSummarySheet = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): void => {
  const {
    customerTradingName,
    platformCode,
    customer,
    data: { labelName, meta },
    period: { fromDate, toDate },
  } = config;

  const rates = meta?.feesBreakdown?.[0]?.meta?.rates;
  const transactionFee = rates?.transactionFee;

  const rejectOneTierTransactionFee = rates?.rejectOneTierItem.transactionFee;

  const summaryWorksheet = workbook.addWorksheet(
    platformCodes.summary as string,
    {
      views: [{ showGridLines: false }],
    }
  );

  summaryWorksheet.columns = excelSummaryColumns;

  const logoName: string = customer?.entityLogoName ?? "logo-gigadat.png";
  const logoPath: string = baseDirectoryName + "/" + logoName;

  const imageBuffer = readFileSync(logoPath);

  const imageId = workbook.addImage({
    buffer: imageBuffer,
    extension: "png",
  });

  const entityName: string = customer?.entityName ?? entities.gigadat;

  if (entityName === entities.gigadat) {
    summaryWorksheet.addImage(imageId, "B1:C4");
  } else {
    summaryWorksheet.addImage(imageId, "C1:C5");
  }

  summaryWorksheet.getColumn("D").numFmt = excelNumberFormat;

  summaryWorksheet.mergeCells("B6:D6");
  summaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B6").value = customerTradingName;

  summaryWorksheet.mergeCells("B7:D7");
  summaryWorksheet.getCell("B7").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B7").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B7").value = labelName;

  summaryWorksheet.mergeCells("B9:D9");
  summaryWorksheet.getCell("B9").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B9").font = { bold: true };
  summaryWorksheet.getCell("B9").value =
    `${format(fromDate, "MMMM dd")} - ${format(toDate, "MMMM dd")}`;

  let rowNumber = 12;

  summaryWorksheet.getCell(`B${rowNumber}`).border = {
    bottom: { style: "thick" },
  };

  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "# of Transactions";

  summaryWorksheet.getCell(`D${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`D${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`D${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = "CAD";

  rowNumber++;

  const platformNameMap: Record<string, string> = {
    RTX: "Realtime Out",
    ETO: "Interac e-Transfer Pay-Outs",
    ACH: "Express Connect ACH Pay-Outs",
    RTO: "Real Time E-Transfer-Out",
    ANR: "Express Connect ANR Pay-Outs",
    ANX: "Express Connect ANX Pay-Outs",
  };

  summaryWorksheet.getCell(`B${rowNumber}`).value =
    platformNameMap[platformCode];

  if (platformCode === platformCodes.rto) {
    summaryWorksheet.getCell(`C${rowNumber}`).value = {
      formula: "COUNT(Transactions!C:C)-COUNT(RTO_R!F:F)",
    };
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula:
        "SUM(Transactions!F:F)-SUM(Failed!F:F)-SUM(RTO_R!F:F)-SUM(RTO_P!F:F)",
    };
    summaryWorksheet.getCell(`B${rowNumber + 1}`).value =
      "Rejected1 Transactions";
    summaryWorksheet.getCell(`C${rowNumber + 1}`).value = {
      formula: "COUNT(REJECTED1!F:F)",
    };
    rowNumber++;
  } else {
    summaryWorksheet.getCell(`C${rowNumber}`).value = {
      formula: "COUNT(Transactions!C:C)",
    };
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: "SUM(Transactions!F:F)-SUM(Failed!F:F)",
    };
  }

  rowNumber += 2;

  const effectiveTransactionFee =
    typeof rejectOneTierTransactionFee === "number"
      ? rejectOneTierTransactionFee
      : (transactionFee ?? 0);

  const transactionFeeFormula =
    platformCode === platformCodes.rto
      ? `C13*${transactionFee} + C14*${effectiveTransactionFee}`
      : `C13*${transactionFee}`;

  summaryWorksheet.getCell(`C${rowNumber}`).value = "Transaction Fee";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: transactionFeeFormula,
  };

  rowNumber += 2;

  summaryWorksheet.getCell(`C${rowNumber}`).value = "TOTAL COSTS";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = {
    bold: true,
  };

  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `SUM(D15:D${rowNumber - 1})`,
  };

  rowNumber++;

  summaryWorksheet.mergeCells(`B${rowNumber}:C${rowNumber}`);
  summaryWorksheet.getCell(`C${rowNumber}`).value =
    "Total to Net against Pay-In (CAD)";
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = {
    bold: true,
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `D13+D${rowNumber - 1}`,
  };
};
