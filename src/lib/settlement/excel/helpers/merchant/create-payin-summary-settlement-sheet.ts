import {
  excelNumberFormat,
  excelSummaryColumns,
  platformCodes,
} from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { format } from "date-fns";

import { createAdjustmentsSection } from "./create-adjustment-section";
import { setupWorksheetLogo } from "./setup-worksheet-logo";

import type ExcelJS from "exceljs";

export const createPayInSummarySheet = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): void => {
  const {
    customerTradingName,
    platformCode,
    customer,
    data: {
      labelName,
      transactionCount,
      totalTransactionAmount,
      minimumFeeCount,
      totalMinimumAmount,
      adjustments,
      meta,
    },
    period: { fromDate, toDate },
  } = config;

  const feesBreakdown = meta?.feesBreakdown ?? [];
  const rates = feesBreakdown[0]?.meta.rates;
  const minimumCharge = rates?.minimumCharge ?? 0;
  const salesFee = rates?.salesFee ?? 0;
  const gatewayFee = rates?.gatewayFee ?? 0;
  const refundFee = rates?.refundFee ?? 0;
  const transactionFee = rates?.transactionFee ?? 0;

  const summaryWorksheet = workbook.addWorksheet(
    platformCodes.summary as string,
    {
      views: [{ showGridLines: false }],
    }
  );

  summaryWorksheet.columns = excelSummaryColumns;

  setupWorksheetLogo(workbook, summaryWorksheet, customer);

  summaryWorksheet.getColumn("D").numFmt = excelNumberFormat;

  summaryWorksheet.mergeCells("B6:D6");
  summaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B6").value = customerTradingName;

  summaryWorksheet.mergeCells("B7:D7");
  summaryWorksheet.getCell("B7").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B7").font = { bold: true, size: 11 };
  summaryWorksheet.getCell("B7").value = labelName;

  summaryWorksheet.mergeCells("B9:D9");
  summaryWorksheet.getCell("B9").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B9").font = { bold: true };
  summaryWorksheet.getCell("B9").value =
    `${format(fromDate, "MMMM dd")} - ${format(toDate, "MMMM dd")}`;

  let rowNumber = 12;

  // B12
  summaryWorksheet.getCell(`B${rowNumber}`).border = {
    bottom: { style: "thick" },
  };

  // C12
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "# of Transactions";

  // D12
  summaryWorksheet.getCell(`D${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`D${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`D${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = "CAD";

  rowNumber++;

  // B13
  summaryWorksheet.getCell(`B${rowNumber}`).value = labelName;

  // C13
  summaryWorksheet.getCell(`C${rowNumber}`).value =
    transactionCount - minimumFeeCount;
  summaryWorksheet.getCell(`C${rowNumber}`).border = { top: { style: "thin" } };

  // D13
  summaryWorksheet.getCell(`D${rowNumber}`).value =
    totalTransactionAmount - totalMinimumAmount;
  summaryWorksheet.getCell(`D${rowNumber}`).border = { top: { style: "thin" } };

  if (minimumFeeCount) {
    rowNumber++;
    summaryWorksheet.getCell(`B${rowNumber}`).value =
      "Minimum Fee Transactions";
    summaryWorksheet.getCell(`C${rowNumber}`).value = minimumFeeCount;
    summaryWorksheet.getCell(`D${rowNumber}`).value = totalMinimumAmount;
  }

  rowNumber += 2;

  // C15
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "Gateway Fee";

  // D15
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `D13*${gatewayFee}`,
  };

  rowNumber++;

  // C16
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "Transaction Fee";

  // D16
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `C13*${transactionFee}`,
  };

  rowNumber++;

  // C17
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "Sales Fee";

  // D17
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: `D13*${salesFee}`,
  };

  rowNumber++;

  if (minimumFeeCount) {
    summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
      horizontal: "right",
    };
    summaryWorksheet.getCell(`C${rowNumber}`).font = {
      bold: false,
    };
    summaryWorksheet.getCell(`C${rowNumber}`).value = "Minimum Fee";
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: `C14*${minimumCharge}`,
    };

    rowNumber++;
  }

  const rowNumberAfterAdjustmentsAdded = createAdjustmentsSection(
    summaryWorksheet,
    adjustments,
    rowNumber
  );

  rowNumber = rowNumberAfterAdjustmentsAdded;

  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };

  if (platformCode === "IDP") {
    summaryWorksheet.getCell(`C${rowNumber}`).value = "Refund Fee";
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: `COUNT(Refunds!E:E)*${refundFee}`,
    };

    rowNumber++;

    summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
      horizontal: "right",
    };
    summaryWorksheet.getCell(`C${rowNumber}`).value = "Refunds";
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: "-(SUM(Refunds!E:E))",
    };

    rowNumber += 2;
  } else {
    summaryWorksheet.getCell(`C${rowNumber}`).value = "Return Fee";
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: `COUNT(Refunds!E:E)*${refundFee}`,
    };

    rowNumber++;

    summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
      horizontal: "right",
    };
    summaryWorksheet.getCell(`C${rowNumber}`).value = "Returns";
    summaryWorksheet.getCell(`D${rowNumber}`).value = {
      formula: "-(SUM(Refunds!E:E))",
    };

    rowNumber++;
  }

  // C (TOTAL COSTS)
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "TOTAL COSTS";

  // D (TOTAL COSTS)
  const totalCostsFormula = `SUM(D15:D${rowNumber - 1})`;
  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: totalCostsFormula,
  };

  if (platformCode !== "IDP") {
    summaryWorksheet.getCell(`D${rowNumber}`).border = {
      top: { style: "thin" },
    };
    summaryWorksheet.getCell(`C${rowNumber}`).border = {
      top: { style: "thin" },
    };
  }

  rowNumber += 2;

  // C (Total Payable)
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = { horizontal: "right" };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).value = "Total Payable in CAD";
  summaryWorksheet.getCell(`C${rowNumber}`).border = { top: { style: "thin" } };

  // D (Total Payable)
  let totalPayableFormula = "";

  // Reference to the total costs cell (sum of all fees and charges)
  const deductionCell = `D${rowNumber - 2}`;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const isRFMorETI = [platformCodes.rfm, platformCodes.eti].includes(
    platformCode as "ETI" | "RFM"
  );
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const isIDPorETF = [platformCodes.idp, platformCodes.etf].includes(
    platformCode as "IDP" | "ETF"
  );

  // Calculate total payable:
  // If there are minimum fees, total payable = (transaction amount
  // + minimum fees transaction amount) - total costs
  if (minimumFeeCount && isRFMorETI) {
    totalPayableFormula = `(D13+D14)-${deductionCell}`;
  }
  // If there are no minimum fees, the total payable is the transaction amount minus the total costs
  else if (!minimumFeeCount && (isRFMorETI || isIDPorETF)) {
    totalPayableFormula = `D13-${deductionCell}`;
  }

  summaryWorksheet.getCell(`D${rowNumber}`).value = {
    formula: totalPayableFormula,
  };
  summaryWorksheet.getCell(`D${rowNumber}`).border = { top: { style: "thin" } };
};
