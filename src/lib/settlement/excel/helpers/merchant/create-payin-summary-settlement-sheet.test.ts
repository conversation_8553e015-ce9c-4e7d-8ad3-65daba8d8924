import { type ExcelConfig } from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import { createPayInSummarySheet } from "./create-payin-summary-settlement-sheet";

const defaultConfig: ExcelConfig = {
  platformCode: "ETI",
  customerTradingName: "Super Customer",
  period: {
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
  },
  fileName: "test-file.xlsx",
  folderPath: "/test/path",
  data: {
    labelName: "Interac e-Transfer Pay-In (ETI)",
    transactionCount: 100,
    totalTransactionAmount: 10_000.5,
    refundCount: 5,
    totalRefundAmount: 500.25,
    gatewayFee: 50,
    transactionFee: 30,
    salesFee: 20,
    refundFee: 2.5,
    totalFailedAmount: 0,
    endBalance: 9000,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnAmountRTO_R: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnAmountRTO: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnCountRTO: 0,
    isAdjusted: false,
    adjustments: [],
    meta: {
      feesBreakdown: [
        {
          interval: {
            fromDate: { year: 2024, month: 1, day: 1 },
            toDate: { year: 2024, month: 1, day: 31 },
          },
          chargedFees: {
            gatewayFeeTotal: 50,
            transactionFeeTotal: 30,
            salesFeeTotal: 20,
            refundFeeTotal: 2.5,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          meta: {
            rates: {
              gatewayFee: 0.005,
              transactionFee: 0.03,
              salesFee: 0.02,
              refundFee: 0.0025,
              reject1Fee: 0.03,
              minimumThreshold: 0,
              minimumCharge: 0,
              isMinimumFeeApplicable: false,
              transactionTierItem: {
                id: 1,
                maxAmount: 1_000_000,
                minAmount: 0,
                transactionFee: 0.03,
                salesFee: 0.02,
              },
              rejectOneTierItem: {
                id: 1,
                maxAmount: 1_000_000,
                minAmount: 0,
                transactionFee: 0.03,
                salesFee: 0.02,
              },
              delayMonths: {
                isDelayApplicable: false,
                delayInMonths: 0,
                tierItem: undefined,
              },
            },
            rateDeterminingProfile: {
              fromDate: { year: 2024, month: 1, day: 1 },
              toDate: { year: 2024, month: 1, day: 31 },
              combineTotal: 10_000.5,
              isCombineIdpTierSet: false,
              isCombineAchTierSet: false,
              isCombineRtoTierSet: false,
              serviceNumbers: [],
              isCombineMultipleServicesEnabled: false,
            },
            transactionSummary: {
              transactionCount: 100,
              totalTransactionAmount: 10_000.5,
              totalFailedAmount: 0,
              failedCount: 0,
              refundCount: 5,
              totalRefundAmount: 500.25,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              total_RAmount: 0,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              _RCount: 0,
              rejected1Count: 0,
              totalRejected1Amount: 0,
              minimumAmountCount: 0,
              totalMinimumAmount: 0,
              partialReturnCount: 0,
              totalPartialReturnAmount: 0,
            },
          },
        },
      ],
    },
  },
  customer: {
    entityName: "Gigadat",
    entityLogoName: "logo-gigadat.png",
  },
};

describe("createPayInSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a SUMMARY worksheet with correct configuration", () => {
    createPayInSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Super Customer");
    expect(worksheet!.getCell("B7").value).toBe(
      "Interac e-Transfer Pay-In (ETI)"
    );
  });

  it("should set transaction count and amount correctly (excluding minimum fees)", () => {
    const config: ExcelConfig = {
      ...defaultConfig,
      customerTradingName: "Test Customer",
    };

    createPayInSummarySheet(workbook, config);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // Check transaction count (100 - 0 = 100)
    expect(worksheet!.getCell("C13").value).toBe(100);
    // Check transaction amount (10000.50 - 0.00 = 10000.50)
    expect(worksheet!.getCell("D13").value).toBe(10_000.5);
  });

  it("should set fee values correctly using rates from meta", () => {
    createPayInSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // Gateway Fee should use formula with rate
    expect(worksheet!.getCell("D15").value).toEqual({
      formula: "D13*0.005",
    });
    // Transaction Fee should use formula with rate
    expect(worksheet!.getCell("D16").value).toEqual({
      formula: "C13*0.03",
    });
    // Sales Fee should use formula with rate
    expect(worksheet!.getCell("D17").value).toEqual({
      formula: "D13*0.02",
    });
  });

  it("should handle minimum fees correctly when present", () => {
    const configWithMinimumFees: ExcelConfig = {
      ...defaultConfig,
      data: {
        ...defaultConfig.data,
        minimumFeeCount: 5,
        totalMinimumAmount: 25,
        meta: {
          feesBreakdown: [
            {
              ...defaultConfig.data.meta!.feesBreakdown[0]!,
              meta: {
                ...defaultConfig.data.meta!.feesBreakdown[0]!.meta,
                rates: {
                  ...defaultConfig.data.meta!.feesBreakdown[0]!.meta.rates,
                  minimumCharge: 5,
                },
              },
            },
          ],
        },
      },
    };

    createPayInSummarySheet(workbook, configWithMinimumFees);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // Should show minimum fee transactions
    expect(worksheet!.getCell("B14").value).toBe("Minimum Fee Transactions");
    expect(worksheet!.getCell("C14").value).toBe(5);
    expect(worksheet!.getCell("D14").value).toBe(25);

    // Should include minimum fee calculation
    // The minimum fee appears after the sales fee
    let minimumFeeFound = false;

    for (let rowIndex = 15; rowIndex <= 20; rowIndex++) {
      const cellValue = worksheet!.getCell(`C${rowIndex}`).value;

      if (cellValue === "Minimum Fee") {
        expect(worksheet!.getCell(`D${rowIndex}`).value).toEqual({
          formula: "C14*5",
        });
        minimumFeeFound = true;
        break;
      }
    }

    expect(minimumFeeFound).toBe(true);
  });

  it("should handle non-IDP platform (ETI) refund logic correctly", () => {
    createPayInSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // Should show "Return Fee" for non-IDP platforms
    expect(worksheet!.getCell("C18").value).toBe("Return Fee");
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "COUNT(Refunds!E:E)*0.0025",
    });
    // Should show "Returns" label for non-IDP platforms
    expect(worksheet!.getCell("C19").value).toBe("Returns");
    expect(worksheet!.getCell("D19").value).toEqual({
      formula: "-(SUM(Refunds!E:E))",
    });
  });

  it("should handle IDP platform refund logic correctly", () => {
    const idpConfig: ExcelConfig = {
      ...defaultConfig,
      platformCode: "IDP",
      data: {
        ...defaultConfig.data,
        labelName: "Interac Online Pay-In (IDP)",
      },
    };

    createPayInSummarySheet(workbook, idpConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // Should show "Refund Fee" for IDP platform
    expect(worksheet!.getCell("C18").value).toBe("Refund Fee");
    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "COUNT(Refunds!E:E)*0.0025",
    });
    // Should show "Refunds" label for IDP platform
    expect(worksheet!.getCell("C19").value).toBe("Refunds");
    expect(worksheet!.getCell("D19").value).toEqual({
      formula: "-(SUM(Refunds!E:E))",
    });
  });

  it("should calculate total payable formula correctly for RFM/ETI with minimum fees", () => {
    const configWithMinimumFees: ExcelConfig = {
      ...defaultConfig,
      platformCode: "RFM",
      data: {
        ...defaultConfig.data,
        minimumFeeCount: 5,
        totalMinimumAmount: 25,
        meta: {
          feesBreakdown: [
            {
              ...defaultConfig.data.meta!.feesBreakdown[0]!,
              meta: {
                ...defaultConfig.data.meta!.feesBreakdown[0]!.meta,
                rates: {
                  ...defaultConfig.data.meta!.feesBreakdown[0]!.meta.rates,
                  minimumCharge: 5,
                },
              },
            },
          ],
        },
      },
    };

    createPayInSummarySheet(workbook, configWithMinimumFees);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    // The total costs should be in row 22, so total payable should use that as deduction
    expect(worksheet!.getCell("D24").value).toEqual({
      formula: "(D13+D14)-D22",
    });
  });

  it("should calculate total payable formula correctly for IDP/ETF without minimum fees", () => {
    const idpConfig: ExcelConfig = {
      ...defaultConfig,
      platformCode: "IDP",
      data: {
        ...defaultConfig.data,
        labelName: "Interac Online Pay-In (IDP)",
      },
    };

    createPayInSummarySheet(workbook, idpConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // For IDP without minimum fees, total payable should be D13 - total costs
    // Total costs is in row 21, so total payable should be in row 23
    expect(worksheet!.getCell("D23").value).toEqual({
      formula: "D13-D21",
    });
  });

  it("should display adjustments when they exist", () => {
    const configWithAdjustments = {
      ...defaultConfig,
      data: {
        ...defaultConfig.data,
        adjustments: [
          {
            id: 1,
            label: "Customer Service Fee",
            amount: 50,
            displayCommentExcel: true,
            comment: "Service fee for support",
          },
          {
            id: 2,
            label: "Late Payment Fee",
            amount: 25,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
    };

    createPayInSummarySheet(workbook, configWithAdjustments);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    let customerServiceFeeFound = false;
    let latePaymentFeeFound = false;

    // Check rows after the refund/return fees (around row 20-25)
    for (let rowIndex = 17; rowIndex <= 25; rowIndex++) {
      const cellValue = worksheet!.getCell(`C${rowIndex}`).value;

      if (cellValue === "Customer Service Fee") {
        expect(worksheet!.getCell(`D${rowIndex}`).value).toBe(50);
        expect(worksheet!.getCell(`F${rowIndex}`).value).toBe(
          "Service fee for support"
        );
        customerServiceFeeFound = true;
      }

      if (cellValue === "Late Payment Fee") {
        expect(worksheet!.getCell(`D${rowIndex}`).value).toBe(25);
        // Should not have comment since displayCommentExcel is false
        expect(worksheet!.getCell(`F${rowIndex}`).value).toBeNull();
        latePaymentFeeFound = true;
      }
    }

    expect(customerServiceFeeFound).toBe(true);
    expect(latePaymentFeeFound).toBe(true);
  });
});
