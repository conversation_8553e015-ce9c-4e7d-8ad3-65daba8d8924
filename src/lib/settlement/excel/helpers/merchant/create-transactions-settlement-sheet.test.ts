/* eslint-disable @typescript-eslint/naming-convention */
import { beforeEach, describe, expect, it, vi } from "vitest";

import { createTransactionsSheets } from "./create-transactions-settlement-sheet";
import {
  isTransactionCreatedInDateRange,
  isTransactionUpdatedInDateRange,
  isValidFinalAmount,
  isValidPlatform,
  isBillableTransaction,
  isNotSpecialRTOTransaction,
} from "./create-transactions-settlement-sheet/transaction-validators";
import { addTransactionRow } from "../create-transaction-excel-row";

import type { Transaction } from "../../../../../services/blusky/transactions";
import type { ExcelConfig } from "@lib/settlement/approve/types";
import type ExcelJS from "exceljs";

vi.mock("../create-transaction-excel-row", () => ({
  addTransactionRow: vi.fn(),
}));

vi.mock("@constants/settlement", () => ({
  excelTransactionColumns: [
    { header: "Created_Date", key: "Created_Date", width: 25 },
    { header: "Updated_Date", key: "Updated_Date", width: 25 },
    { header: "Service_Number", key: "Service_Number", width: 10 },
  ],
  platformCodes: {
    summary: "SUMMARY",
    rto: "RTO",
    kyc: "KYC",
    eti: "ETI",
    etf: "ETF",
    rfm: "RFM",
    eto: "ETO",
    rtx: "RTX",
    ach: "ACH",
    anr: "ANR",
    anx: "ANX",
    idp: "IDP",
  },
}));

vi.mock("@utils/date", () => ({
  isDateInRange: vi.fn(),
}));

vi.mock(
  "./create-transactions-settlement-sheet/transaction-validators",
  () => ({
    isTransactionCreatedInDateRange: vi.fn(),
    isTransactionUpdatedInDateRange: vi.fn(),
    isValidFinalAmount: vi.fn(),
    isValidPlatform: vi.fn(),
    isBillableTransaction: vi.fn(),
    isNotSpecialRTOTransaction: vi.fn(),
  })
);

const defaultTransaction: Transaction = {
  createdDate: "2024-01-15T10:30:00Z",
  updatedDate: "2024-01-15T11:45:00Z",
  serviceNumber: "**********",
  originalAmt: 100.5,
  refundAmt: 0,
  finalAmt: 100.5,
  currency: "CAD",
  country: "CA",
  billable: true,
  platform: "ETI",
  custNumber: "CUST123",
  rcode: 200,
  integratorName: "Test Integrator",
  programName: "Test Program",
  billingName: "Test Billing",
  transactionID: "TXN123456",
  receiptID: "RCP789012",
  interacRef: "INT345678",
  fIName: "Test Bank",
  status: "STATUS_SUCCESS",
};

describe("createTransactionsSheets", () => {
  let mockWorkbook: ExcelJS.Workbook;
  let mockTransactionsWorksheet: ExcelJS.Worksheet;
  let mockRefundsWorksheet: ExcelJS.Worksheet;
  let mockConfig: ExcelConfig;

  beforeEach(() => {
    vi.mocked(isTransactionCreatedInDateRange).mockReturnValue(true);
    vi.mocked(isTransactionUpdatedInDateRange).mockReturnValue(true);
    vi.mocked(isValidFinalAmount).mockReturnValue(true);
    vi.mocked(isValidPlatform).mockReturnValue(true);
    vi.mocked(isBillableTransaction).mockReturnValue(true);
    vi.mocked(isNotSpecialRTOTransaction).mockReturnValue(true);

    mockTransactionsWorksheet = {
      columns: undefined,
    } as unknown as ExcelJS.Worksheet;

    mockRefundsWorksheet = {
      columns: undefined,
    } as unknown as ExcelJS.Worksheet;

    mockWorkbook = {
      addWorksheet: vi
        .fn()
        .mockReturnValueOnce(mockTransactionsWorksheet)
        .mockReturnValueOnce(mockRefundsWorksheet),
    } as unknown as ExcelJS.Workbook;

    mockConfig = {
      platformCode: "ETI",
      customerTradingName: "Test Customer",
      period: {
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
      },
      fileName: "test-file.xlsx",
      folderPath: "/test/path",
      data: {
        labelName: "Test Label",
        transactionCount: 10,
        totalTransactionAmount: 1000,
        refundCount: 2,
        totalRefundAmount: 200,
        gatewayFee: 50,
        transactionFee: 30,
        salesFee: 20,
        refundFee: 10,
        totalFailedAmount: 0,
        endBalance: 800,
        total2FaRejectAmount: 0,
        total2FaRejectCount: 0,
        txnAmountRTO_R: 0,
        txnCountETI_R1: 0,
        minimumFeeTotal: 0,
        minimumFeeCount: 0,
        totalMinimumAmount: 0,
        partialReturnAmountRTO: 0,
        partialReturnCountRTO: 0,
        isAdjusted: false,
        adjustments: [],
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("creates both Transactions and Refunds worksheets and sets columns", () => {
    createTransactionsSheets(mockWorkbook, mockConfig);

    expect(mockWorkbook.addWorksheet).toHaveBeenCalledTimes(2);
    expect(mockWorkbook.addWorksheet).toHaveBeenNthCalledWith(
      1,
      "Transactions"
    );
    expect(mockWorkbook.addWorksheet).toHaveBeenNthCalledWith(2, "Refunds");
    expect(mockTransactionsWorksheet.columns).toEqual([
      { header: "Created_Date", key: "Created_Date", width: 25 },
      { header: "Updated_Date", key: "Updated_Date", width: 25 },
      { header: "Service_Number", key: "Service_Number", width: 10 },
    ]);
    expect(mockRefundsWorksheet.columns).toEqual([
      { header: "Created_Date", key: "Created_Date", width: 25 },
      { header: "Updated_Date", key: "Updated_Date", width: 25 },
      { header: "Service_Number", key: "Service_Number", width: 10 },
    ]);
  });

  it("returns early if no platformTransactions provided", () => {
    createTransactionsSheets(mockWorkbook, mockConfig);
    expect(addTransactionRow).not.toHaveBeenCalled();
  });

  it("returns early if empty platformTransactions array provided", () => {
    createTransactionsSheets(mockWorkbook, mockConfig, []);
    expect(addTransactionRow).not.toHaveBeenCalled();
  });

  it("processes successful transactions in date range", () => {
    vi.mocked(isTransactionUpdatedInDateRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      { ...defaultTransaction },
      {
        ...defaultTransaction,
        createdDate: "2024-01-16T10:30:00Z",
        updatedDate: "2024-01-16T11:45:00Z",
        serviceNumber: "1234567891",
        originalAmt: 200.75,
        finalAmt: 200.75,
        custNumber: "CUST124",
        transactionID: "TXN123457",
        receiptID: "RCP789013",
        interacRef: "INT345679",
      },
    ];

    createTransactionsSheets(mockWorkbook, mockConfig, mockTransactions);

    expect(isTransactionUpdatedInDateRange).toHaveBeenCalled();
    expect(addTransactionRow).toHaveBeenCalledTimes(2);
    expect(addTransactionRow).toHaveBeenNthCalledWith(
      1,
      mockTransactionsWorksheet,
      mockTransactions[0],
      2
    );
    expect(addTransactionRow).toHaveBeenNthCalledWith(
      2,
      mockTransactionsWorksheet,
      mockTransactions[1],
      3
    );
  });

  it("processes refunded transactions in date range", () => {
    const mockTransactions: Transaction[] = [
      {
        ...defaultTransaction,
        refundAmt: 100.5,
        finalAmt: 0,
        status: "STATUS_REFUNDED",
      },
    ];

    createTransactionsSheets(mockWorkbook, mockConfig, mockTransactions);

    expect(isTransactionUpdatedInDateRange).toHaveBeenCalled();
    expect(addTransactionRow).toHaveBeenCalledTimes(1);
    expect(addTransactionRow).toHaveBeenCalledWith(
      mockRefundsWorksheet,
      mockTransactions[0],
      2
    );
  });

  it("skips transactions outside date range", () => {
    vi.mocked(isTransactionUpdatedInDateRange).mockReturnValue(false);

    const mockTransactions: Transaction[] = [
      {
        ...defaultTransaction,
        createdDate: "2023-12-15T10:30:00Z",
        updatedDate: "2023-12-15T11:45:00Z",
      },
    ];

    createTransactionsSheets(mockWorkbook, mockConfig, mockTransactions);

    expect(isTransactionUpdatedInDateRange).toHaveBeenCalled();
    expect(addTransactionRow).not.toHaveBeenCalled();
  });

  it("skips transactions with status other than SUCCESS, REFUNDED", () => {
    vi.mocked(isTransactionUpdatedInDateRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      {
        ...defaultTransaction,
        rcode: 400,
        status: "STATUS_REJECTED1",
      },
    ];

    createTransactionsSheets(mockWorkbook, mockConfig, mockTransactions);

    expect(isTransactionUpdatedInDateRange).toHaveBeenCalled();
    expect(addTransactionRow).not.toHaveBeenCalled();
  });

  it("handles mixed transaction types correctly", () => {
    vi.mocked(isTransactionUpdatedInDateRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      { ...defaultTransaction },
      {
        ...defaultTransaction,
        createdDate: "2024-01-16T10:30:00Z",
        updatedDate: "2024-01-16T11:45:00Z",
        serviceNumber: "1234567891",
        originalAmt: 200.75,
        refundAmt: 200.75,
        finalAmt: 0,
        custNumber: "CUST124",
        transactionID: "TXN123457",
        receiptID: "RCP789013",
        interacRef: "INT345679",
        status: "STATUS_REFUNDED",
      },
      {
        ...defaultTransaction,
        createdDate: "2024-01-17T10:30:00Z",
        updatedDate: "2024-01-17T11:45:00Z",
        serviceNumber: "1234567892",
        originalAmt: 300.25,
        finalAmt: 300.25,
        custNumber: "CUST125",
        rcode: 400,
        transactionID: "TXN123458",
        receiptID: "RCP789014",
        interacRef: "INT345680",
        status: "STATUS_FAILED",
      },
    ];

    createTransactionsSheets(mockWorkbook, mockConfig, mockTransactions);

    expect(addTransactionRow).toHaveBeenCalled();
    expect(addTransactionRow).toHaveBeenNthCalledWith(
      1,
      mockTransactionsWorksheet,
      mockTransactions[0],
      2
    );
    expect(addTransactionRow).toHaveBeenNthCalledWith(
      2,
      mockRefundsWorksheet,
      mockTransactions[1],
      2
    );
  });
});
