import { readFileSync } from "node:fs";

import { entities } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";
import {
  excelNumberFormat,
  kycSummaryColumns,
  platformCodes,
} from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { format } from "date-fns";

import type ExcelJS from "exceljs";

// TODO: we can reuse it for other platforms too
const setupWorksheetLogo = (
  workbook: ExcelJS.Workbook,
  worksheet: ExcelJS.Worksheet,
  customer?: { entityName?: string; entityLogoName?: string }
): void => {
  const logoName: string = customer?.entityLogoName ?? "logo-gigadat.png";
  const logoPath: string = baseDirectoryName + "/" + logoName;
  const imageBuffer = readFileSync(logoPath);
  const imageId = workbook.addImage({
    buffer: imageBuffer,
    extension: "png",
  });

  const entityName: string = customer?.entityName ?? entities.gigadat;

  if (entityName === entities.gigadat) {
    worksheet.addImage(imageId, "B1:C4");
  } else {
    worksheet.addImage(imageId, "C1:C5");
  }
};

export const createKycSummarySheet = async (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): Promise<void> => {
  const {
    customerTradingName,
    customer,
    data: { kycDetails },
    period: { fromDate, toDate },
    kycDescriptions,
  } = config;

  const kycSummaryWorksheet = workbook.addWorksheet(platformCodes.summary, {
    views: [{ showGridLines: false }],
  });

  kycSummaryWorksheet.columns = kycSummaryColumns;

  setupWorksheetLogo(workbook, kycSummaryWorksheet, customer);

  kycSummaryWorksheet.getColumn("E").numFmt = excelNumberFormat;

  kycSummaryWorksheet.mergeCells("B6:D6");
  kycSummaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("B6").value = customerTradingName;

  // KYC Summary title
  kycSummaryWorksheet.mergeCells("B8:D8");
  kycSummaryWorksheet.getCell("B8").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B8").font = {
    bold: true,
    size: 11,
  };
  kycSummaryWorksheet.getCell("B8").value = "KYC Summary";

  // Date range
  kycSummaryWorksheet.mergeCells("B10:D10");
  kycSummaryWorksheet.getCell("B10").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B10").font = {
    bold: true,
    size: 11,
  };
  kycSummaryWorksheet.getCell("B10").value =
    `${format(fromDate, "MMMM do yyyy")} - ${format(toDate, "MMMM do yyyy")}`;

  // Entity name (if transaction count is high enough)
  const entityName: string = customer?.entityName ?? entities.gigadat;

  if (entityName !== entities.gigadat) {
    kycSummaryWorksheet.mergeCells("B4:D4");
    kycSummaryWorksheet.getCell("B4").alignment = { horizontal: "center" };
    kycSummaryWorksheet.getCell("B4").font = { bold: true, size: 11 };
    kycSummaryWorksheet.getCell("B4").value = entityName;
  }

  // Column headers
  kycSummaryWorksheet.getCell("B12").value = "KYC Type";
  kycSummaryWorksheet.getCell("C12").value = "KYC Description";
  kycSummaryWorksheet.getCell("D12").value = "Transaction Count";
  kycSummaryWorksheet.getCell("E12").value = "Total Fees";

  // Header formatting
  kycSummaryWorksheet.getCell("B12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("B12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("C12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("C12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("D12").alignment = {
    horizontal: "center",
    wrapText: true,
  };
  kycSummaryWorksheet.getCell("D12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("D12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("E12").alignment = { horizontal: "right" };
  kycSummaryWorksheet.getCell("E12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("E12").border = { bottom: { style: "thick" } };

  // Header

  // setupKycWorksheetHeaders(
  //   kycSummaryWorksheet,
  //   customerTradingName,
  //   fromDate,
  //   toDate,
  //   customer
  // );

  // Add KYC type details
  let kycRow = 15;

  if (kycDetails) {
    const kycTypes = Object.entries(kycDetails).sort((a, b) =>
      a[0].localeCompare(b[0])
    );

    for (const [kycType, details] of kycTypes) {
      if (details && details.transactionCount > 0) {
        // Add KYC type row
        kycSummaryWorksheet.getCell(`B${kycRow}`).font = { size: 11 };
        kycSummaryWorksheet.getCell(`C${kycRow}`).font = { size: 11 };
        kycSummaryWorksheet.getCell(`B${kycRow}`).value = kycType;
        kycSummaryWorksheet.getCell(`C${kycRow}`).value =
          kycDescriptions?.[kycType] ?? `${kycType} Description`;
        kycSummaryWorksheet.getCell(`D${kycRow}`).alignment = {
          horizontal: "center",
        };
        kycSummaryWorksheet.getCell(`D${kycRow}`).value =
          details.transactionCount;
        kycSummaryWorksheet.getCell(`E${kycRow}`).value =
          details.totalTransactionAmount;

        // Create individual KYC type worksheet
        // const kycTypeWorksheet = workbook.addWorksheet(kycType);
        // kycTypeWorksheet.columns = kycTxnColumns;

        // TODO: Add actual KYC transactions to the worksheet
        // This would require fetching KYC transactions from the database
        // and adding them as rows to the worksheet

        kycRow++;
      }
    }
  }

  // Add Sub Total row
  // kycSummaryWorksheet.getCell(`C${kycRow + 2}`).font = { bold: true, size: 10 };
  // kycSummaryWorksheet.getCell(`C${kycRow + 2}`).value = "Sub Total";
  // kycSummaryWorksheet.getCell(`D${kycRow + 2}`).alignment = {
  //   horizontal: "center",
  // };
  // kycSummaryWorksheet.getCell(`D${kycRow + 2}`).value = totalTransactionCount;
  // kycSummaryWorksheet.getCell(`E${kycRow + 2}`).value = totalFees;

  // // Add adjustments
  // let adjustmentRow = kycRow + 3;

  // if (adjustments && adjustments.length > 0) {
  //   for (const adjustment of adjustments) {
  //     kycSummaryWorksheet.getCell(`C${adjustmentRow}`).font = {
  //       bold: true,
  //       size: 10,
  //     };
  //     kycSummaryWorksheet.getCell(`C${adjustmentRow}`).alignment = {
  //       horizontal: "right",
  //     };
  //     kycSummaryWorksheet.getCell(`E${adjustmentRow}`).alignment = {
  //       horizontal: "right",
  //     };
  //     kycSummaryWorksheet.getCell(`C${adjustmentRow}`).value = adjustment.label;
  //     kycSummaryWorksheet.getCell(`E${adjustmentRow}`).value =
  //       adjustment.amount;

  //     if (adjustment.displayCommentExcel && adjustment.comment) {
  //       kycSummaryWorksheet.getCell(`F${adjustmentRow}`).value =
  //         adjustment.comment;
  //     }

  //     totalFees += adjustment.amount;
  //     adjustmentRow++;
  //   }
  // }

  // // Add Total Payable row
  // kycSummaryWorksheet.getCell(`C${adjustmentRow}`).font = {
  //   bold: true,
  //   size: 10,
  // };
  // kycSummaryWorksheet.getCell(`C${adjustmentRow}`).value = "Total Payable";
  // kycSummaryWorksheet.getCell(`E${adjustmentRow}`).border = {
  //   bottom: { style: "thick" },
  //   top: { style: "thin" },
  // };
  // kycSummaryWorksheet.getCell(`E${adjustmentRow}`).value = {
  //   formula: `SUM(E${kycRow + 2}:E${adjustmentRow - 1})`,
  // };

  // // Add bottom border
  // kycSummaryWorksheet.getCell(`E${adjustmentRow + 1}`).border = {
  //   top: { style: "thick" },
  // };
  // kycSummaryWorksheet.getCell(`E${adjustmentRow + 1}`).value = "   ";
};
