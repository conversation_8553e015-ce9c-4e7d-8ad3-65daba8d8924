import {
  excelNumberFormat,
  kycSummaryColumns,
  platformCodes,
} from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { type KycName } from "@lib/settlement/repository/types";
import { format } from "date-fns";

import { setupWorksheetLogo } from "./setup-worksheet-logo";

import type ExcelJS from "exceljs";

export const createKycSummarySheet = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): void => {
  const {
    customerTradingName,
    customer,
    data: { kycDetails, adjustments },
    period: { fromDate, toDate },
    kycDescriptions,
  } = config;

  const kycSummaryWorksheet = workbook.addWorksheet(platformCodes.summary, {
    views: [{ showGridLines: false }],
  });

  kycSummaryWorksheet.columns = kycSummaryColumns;

  setupWorksheetLogo(workbook, kycSummaryWorksheet, customer);

  kycSummaryWorksheet.getColumn("E").numFmt = excelNumberFormat;

  kycSummaryWorksheet.mergeCells("B6:D6");
  kycSummaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("B6").value = customerTradingName;

  // KYC Summary title
  kycSummaryWorksheet.mergeCells("B8:D8");
  kycSummaryWorksheet.getCell("B8").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B8").font = {
    bold: true,
    size: 11,
  };
  kycSummaryWorksheet.getCell("B8").value = "KYC Summary";

  // Date range
  kycSummaryWorksheet.mergeCells("B10:D10");
  kycSummaryWorksheet.getCell("B10").alignment = { horizontal: "center" };
  kycSummaryWorksheet.getCell("B10").font = {
    bold: true,
    size: 11,
  };
  kycSummaryWorksheet.getCell("B10").value =
    `${format(fromDate, "MMMM do yyyy")} - ${format(toDate, "MMMM do yyyy")}`;

  // Column headers
  kycSummaryWorksheet.getCell("B12").value = "KYC Type";
  kycSummaryWorksheet.getCell("C12").value = "KYC Description";
  kycSummaryWorksheet.getCell("D12").value = "Transaction Count";
  kycSummaryWorksheet.getCell("E12").value = "Total Fees";

  // Header formatting
  kycSummaryWorksheet.getCell("B12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("B12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("C12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("C12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("D12").alignment = {
    horizontal: "center",
    wrapText: true,
  };
  kycSummaryWorksheet.getCell("D12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("D12").border = { bottom: { style: "thick" } };
  kycSummaryWorksheet.getCell("E12").alignment = { horizontal: "right" };
  kycSummaryWorksheet.getCell("E12").font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell("E12").border = { bottom: { style: "thick" } };

  // Add KYC type details
  let currentRow = 15;
  let totalTransactionCount = 0;
  let totalFees = 0;

  if (kycDetails) {
    const kycTypes = Object.entries(kycDetails).sort((a, b) =>
      a[0].localeCompare(b[0])
    );

    for (const [kycType, details] of kycTypes) {
      kycSummaryWorksheet.getCell(`B${currentRow}`).font = { size: 11 };
      kycSummaryWorksheet.getCell(`C${currentRow}`).font = { size: 11 };
      kycSummaryWorksheet.getCell(`B${currentRow}`).value = kycType;
      kycSummaryWorksheet.getCell(`C${currentRow}`).value =
        kycDescriptions?.[kycType as KycName] ?? kycType;
      kycSummaryWorksheet.getCell(`D${currentRow}`).alignment = {
        horizontal: "center",
      };
      kycSummaryWorksheet.getCell(`D${currentRow}`).value =
        details.transactionCount;
      kycSummaryWorksheet.getCell(`E${currentRow}`).value =
        details.totalTransactionAmount;

      totalTransactionCount += details.transactionCount;
      totalFees += details.totalTransactionAmount;
      currentRow++;
    }
  }

  currentRow += 2;

  // Add Sub Total row
  kycSummaryWorksheet.getCell(`C${currentRow}`).font = { bold: true, size: 11 };
  kycSummaryWorksheet.getCell(`C${currentRow}`).value = "Sub Total";
  kycSummaryWorksheet.getCell(`D${currentRow}`).alignment = {
    horizontal: "center",
  };
  kycSummaryWorksheet.getCell(`D${currentRow}`).value = totalTransactionCount;
  kycSummaryWorksheet.getCell(`E${currentRow}`).value = totalFees;

  const subTotalRow = currentRow;

  currentRow += 2;
  // Add adjustments

  if (adjustments && adjustments.length > 0) {
    for (const adjustment of adjustments) {
      kycSummaryWorksheet.getCell(`C${currentRow}`).font = {
        bold: true,
        size: 11,
      };
      kycSummaryWorksheet.getCell(`C${currentRow}`).value = adjustment.label;
      kycSummaryWorksheet.getCell(`E${currentRow}`).alignment = {
        horizontal: "right",
      };
      kycSummaryWorksheet.getCell(`E${currentRow}`).value = adjustment.amount;

      if (adjustment.displayCommentExcel && adjustment.comment) {
        kycSummaryWorksheet.getCell(`F${currentRow}`).value =
          adjustment.comment;
      }

      totalFees += adjustment.amount;
      currentRow++;
    }
  }

  // Add Total Payable row
  kycSummaryWorksheet.getCell(`C${currentRow}`).font = {
    bold: true,
    size: 11,
  };
  kycSummaryWorksheet.getCell(`C${currentRow}`).value = "Total Payable";
  kycSummaryWorksheet.getCell(`E${currentRow}`).border = {
    bottom: { style: "thick" },
    top: { style: "thin" },
  };
  kycSummaryWorksheet.getCell(`E${currentRow}`).value = {
    formula: `SUM(E${subTotalRow}:E${currentRow - 1})`,
  };

  // Add bottom border
  currentRow++;
  kycSummaryWorksheet.getCell(`E${currentRow}`).border = {
    top: { style: "thick" },
  };
};
