import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import {
  type ExcelConfig,
  type TransactionProcessingContext,
} from "@lib/settlement/approve/types";

import {
  processPayInTransactions,
  processPayOutTransactions,
} from "./create-transactions-settlement-sheet/transaction-processors";
import { initializeRowCounters } from "./create-transactions-settlement-sheet/utils";
import { createWorksheets } from "./create-transactions-settlement-sheet/worksheet-creators";
import { type Transaction } from "../../../../../services/blusky/transactions";

import type ExcelJS from "exceljs";

export const createTransactionsSheets = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig,
  platformTransactions?: Transaction[]
): void => {
  const platformCode = config.platformCode as Platform;
  const isPayInPlatform = payInGroup.includes(platformCode);
  const isPayOutPlatform = payOutGroup.includes(platformCode);

  const worksheets = createWorksheets(
    workbook,
    platformCode,
    isPayInPlatform,
    isPayOutPlatform
  );

  if (!platformTransactions?.length) {
    return;
  }

  const rowCounters = initializeRowCounters();

  const context: TransactionProcessingContext = {
    worksheets,
    rowCounters,
    config,
    isPayInPlatform,
  };

  if (isPayInPlatform) {
    processPayInTransactions(platformTransactions, context);
  } else if (isPayOutPlatform) {
    processPayOutTransactions(platformTransactions, context);
  }
};
