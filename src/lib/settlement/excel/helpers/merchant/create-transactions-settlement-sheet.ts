/* eslint-disable @typescript-eslint/naming-convention */
import { excelTransactionColumns, platformCodes } from "@constants/settlement";
import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import {
  type RowCounters,
  type TransactionProcessingContext,
  type WorksheetCollection,
  type ExcelConfig,
} from "@lib/settlement/approve/types";
import { isDateInRange } from "@utils/date";

import {
  transactionStatus,
  type Transaction,
} from "../../../../../services/blusky/transactions";
import { addTransactionRow } from "../create-transaction-excel-row";

import type ExcelJS from "exceljs";

const INITIAL_ROW_COUNT = 2;

const RTO_TRANSACTION_SUFFIXES = {
  R: "_R",
  P: "_P",
} as const;

export const createTransactionsSheets = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig,
  platformTransactions?: Transaction[]
): void => {
  const { platformCode } = config;
  const isPayInPlatform = payInGroup.includes(platformCode as Platform);
  const isPayOutPlatform = payOutGroup.includes(platformCode as Platform);

  const worksheets = createWorksheets(
    workbook,
    platformCode as Platform,
    isPayInPlatform,
    isPayOutPlatform
  );

  if (!platformTransactions?.length) {
    return;
  }

  const rowCounters = initializeRowCounters();

  const context: TransactionProcessingContext = {
    worksheets,
    rowCounters,
    config,
    isPayInPlatform,
  };

  if (isPayInPlatform) {
    processPayInTransactions(platformTransactions, context);
  } else if (isPayOutPlatform) {
    processPayOutTransactions(platformTransactions, context);
  }
};

const createWorksheets = (
  workbook: ExcelJS.Workbook,
  platformCode: Platform,
  isPayInPlatform: boolean,
  isPayOutPlatform: boolean
): WorksheetCollection => {
  const transactionsWorksheet = workbook.addWorksheet("Transactions");
  transactionsWorksheet.columns = excelTransactionColumns;

  const worksheets: WorksheetCollection = {
    transactionsWorksheet,
  };

  if (isPayInPlatform) {
    createPayInWorksheets(workbook, worksheets);
  }

  if (isPayOutPlatform) {
    createPayOutWorksheets(workbook, platformCode, worksheets);
  }

  return worksheets;
};

const createPayInWorksheets = (
  workbook: ExcelJS.Workbook,
  worksheets: WorksheetCollection
): void => {
  const refundsWorksheet = workbook.addWorksheet("Refunds");
  refundsWorksheet.columns = excelTransactionColumns;
  worksheets.refundsOrFailedWorksheet = refundsWorksheet;
};

const createPayOutWorksheets = (
  workbook: ExcelJS.Workbook,
  platformCode: Platform,
  worksheets: WorksheetCollection
): void => {
  const failedWorksheet = workbook.addWorksheet("Failed");
  failedWorksheet.columns = excelTransactionColumns;
  worksheets.refundsOrFailedWorksheet = failedWorksheet;

  if (platformCode === platformCodes.rto) {
    createRTOWorksheets(workbook, worksheets);
  }
};

const createRTOWorksheets = (
  workbook: ExcelJS.Workbook,
  worksheets: WorksheetCollection
): void => {
  const rejected1Worksheet = workbook.addWorksheet("REJECTED1");
  rejected1Worksheet.columns = excelTransactionColumns;
  worksheets.rejected1Worksheet = rejected1Worksheet;

  const RTO_R_Worksheet = workbook.addWorksheet("RTO_R");
  RTO_R_Worksheet.columns = excelTransactionColumns;
  worksheets.RTO_R_Worksheet = RTO_R_Worksheet;

  const RTO_P_Worksheet = workbook.addWorksheet("RTO_P");
  RTO_P_Worksheet.columns = excelTransactionColumns;
  worksheets.RTO_P_Worksheet = RTO_P_Worksheet;
};

const initializeRowCounters = (): RowCounters => ({
  transactions: INITIAL_ROW_COUNT,
  refunds: INITIAL_ROW_COUNT,
  failed: INITIAL_ROW_COUNT,
  rejected1: INITIAL_ROW_COUNT,
  RTO_R: INITIAL_ROW_COUNT,
  RTO_P: INITIAL_ROW_COUNT,
});

const processPayInTransactions = (
  platformTransactions: Transaction[],
  context: TransactionProcessingContext
): void => {
  for (const transaction of platformTransactions) {
    processPayInTransaction(transaction, context);
    processRefundTransactions(transaction, context);
  }
};

const processPayOutTransactions = (
  platformTransactions: Transaction[],
  context: TransactionProcessingContext
): void => {
  const isRTOPlatform = context.config.platformCode === platformCodes.rto;

  for (const transaction of platformTransactions) {
    processPayOutTransaction(transaction, context);
    processFailedTransactions(transaction, context);

    if (isRTOPlatform) {
      processRejected1Transactions(transaction, context);
      processRTOTypeTransactions(transaction, context);
    }
  }
};

const isTransactionCreatedInDateRange = (
  transaction: Transaction,
  config: ExcelConfig
): boolean => {
  return isDateInRange(
    new Date(transaction.createdDate),
    config.period.fromDate,
    config.period.toDate
  );
};

const isTransactionUpdatedInDateRange = (
  transaction: Transaction,
  config: ExcelConfig
): boolean => {
  return isDateInRange(
    new Date(transaction.updatedDate),
    config.period.fromDate,
    config.period.toDate
  );
};

const isValidPlatformTransaction = (
  transaction: Transaction,
  platformCode: string
): boolean => {
  return transaction.platform === platformCode && Boolean(transaction.finalAmt);
};

const isValidBillableTransaction = (
  transaction: Transaction,
  platformCode: string
): boolean => {
  return transaction.platform === platformCode && transaction.billable;
};

const isNotSpecialRTOTransaction = (transaction: Transaction): boolean => {
  return (
    !transaction.transactionID.endsWith(RTO_TRANSACTION_SUFFIXES.R) &&
    !transaction.transactionID.endsWith(RTO_TRANSACTION_SUFFIXES.P)
  );
};

const processPayInTransaction = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    isValidBillableTransaction(transaction, context.config.platformCode) &&
    transaction.status === transactionStatus.success
  ) {
    addTransactionRow(
      context.worksheets.transactionsWorksheet,
      transaction,
      context.rowCounters.transactions
    );
    context.rowCounters.transactions++;
  }
};

const processPayOutTransaction = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionCreatedInDateRange(transaction, context.config) &&
    isValidPlatformTransaction(transaction, context.config.platformCode)
  ) {
    addTransactionRow(
      context.worksheets.transactionsWorksheet,
      transaction,
      context.rowCounters.transactions
    );
    context.rowCounters.transactions++;
  }
};

const processRefundTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  const isRefundStatus =
    transaction.status === transactionStatus.refunded ||
    transaction.status === transactionStatus.fRefund ||
    transaction.status === transactionStatus.refund;

  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    isValidBillableTransaction(transaction, context.config.platformCode) &&
    isRefundStatus &&
    context.worksheets.refundsOrFailedWorksheet
  ) {
    addTransactionRow(
      context.worksheets.refundsOrFailedWorksheet,
      transaction,
      context.rowCounters.refunds
    );
    context.rowCounters.refunds++;
  }
};

const processFailedTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  const { platformCode } = context.config;
  const isRTOPlatform = platformCode === platformCodes.rto;

  if (
    !isTransactionUpdatedInDateRange(transaction, context.config) ||
    transaction.platform !== platformCode ||
    transaction.status !== transactionStatus.failed ||
    !context.worksheets.refundsOrFailedWorksheet ||
    (isRTOPlatform && !isNotSpecialRTOTransaction(transaction))
  ) {
    return;
  }

  addTransactionRow(
    context.worksheets.refundsOrFailedWorksheet,
    transaction,
    context.rowCounters.failed
  );
  context.rowCounters.failed++;
};

const processRejected1Transactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    transaction.status === transactionStatus.rejected1 &&
    context.worksheets.rejected1Worksheet
  ) {
    addTransactionRow(
      context.worksheets.rejected1Worksheet,
      transaction,
      context.rowCounters.rejected1
    );
    context.rowCounters.rejected1++;
  }
};

const processRTOTypeTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    !isTransactionCreatedInDateRange(transaction, context.config) ||
    !isValidPlatformTransaction(transaction, context.config.platformCode)
  ) {
    return;
  }

  const { transactionID } = transaction;

  if (
    transactionID.endsWith(RTO_TRANSACTION_SUFFIXES.R) &&
    context.worksheets.RTO_R_Worksheet
  ) {
    addTransactionRow(
      context.worksheets.RTO_R_Worksheet,
      transaction,
      context.rowCounters.RTO_R
    );
    context.rowCounters.RTO_R++;
  } else if (
    transactionID.endsWith(RTO_TRANSACTION_SUFFIXES.P) &&
    context.worksheets.RTO_P_Worksheet
  ) {
    addTransactionRow(
      context.worksheets.RTO_P_Worksheet,
      transaction,
      context.rowCounters.RTO_P
    );
    context.rowCounters.RTO_P++;
  }
};
