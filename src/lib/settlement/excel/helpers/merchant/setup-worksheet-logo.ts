import { readFileSync } from "node:fs";

import { defaultLogoFilename, entities } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";

import type ExcelJS from "exceljs";

export const setupWorksheetLogo = (
  workbook: ExcelJS.Workbook,
  worksheet: ExcelJS.Worksheet,
  customer?: { entityName?: string; entityLogoName?: string }
): void => {
  const logoName: string = customer?.entityLogoName ?? defaultLogoFilename;
  const logoPath: string = baseDirectoryName + "/" + logoName;

  let imageBuffer;

  try {
    imageBuffer = readFileSync(logoPath);
  } catch {
    throw new Error(`Failed to load logo from ${logoPath}`);
  }

  const imageId = workbook.addImage({
    buffer: imageBuffer,
    extension: "png",
  });

  const entityName: string = customer?.entityName ?? entities.gigadat;

  if (entityName === entities.gigadat) {
    worksheet.addImage(imageId, "B1:C4");
  } else {
    worksheet.addImage(imageId, "C1:C5");
  }
};
