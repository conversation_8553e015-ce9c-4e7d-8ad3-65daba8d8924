import { rtoTransactionSuffixes } from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { isDateInRange } from "@utils/date";

import { type Transaction } from "../../../../../../services/blusky/transactions";

export const isTransactionCreatedInDateRange = (
  transaction: Transaction,
  config: ExcelConfig
): boolean => {
  return isDateInRange(
    new Date(transaction.createdDate),
    config.period.fromDate,
    config.period.toDate
  );
};

export const isTransactionUpdatedInDateRange = (
  transaction: Transaction,
  config: ExcelConfig
): boolean => {
  return isDateInRange(
    new Date(transaction.updatedDate),
    config.period.fromDate,
    config.period.toDate
  );
};

export const isValidPlatformTransaction = (
  transaction: Transaction,
  platformCode: string
): boolean => {
  return transaction.platform === platformCode && Boolean(transaction.finalAmt);
};

export const isValidBillableTransaction = (
  transaction: Transaction,
  platformCode: string
): boolean => {
  return transaction.platform === platformCode && transaction.billable;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const isNotSpecialRTOTransaction = (
  transaction: Transaction
): boolean => {
  return (
    !transaction.transactionID.endsWith(rtoTransactionSuffixes.R) &&
    !transaction.transactionID.endsWith(rtoTransactionSuffixes.P)
  );
};
