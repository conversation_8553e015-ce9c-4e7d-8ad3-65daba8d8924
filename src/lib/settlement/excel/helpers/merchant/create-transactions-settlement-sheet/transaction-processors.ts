import { platformCodes, rtoTransactionSuffixes } from "@constants/settlement";
import { type TransactionProcessingContext } from "@lib/settlement/approve/types";
import { addTransactionRow } from "@lib/settlement/excel/helpers/create-transaction-excel-row";

import {
  isTransactionCreatedInDateRange,
  isTransactionUpdatedInDateRange,
  isValidPlatformTransaction,
  isValidBillableTransaction,
  isNotSpecialRTOTransaction,
} from "./transaction-validators";
import {
  transactionStatus,
  type Transaction,
} from "../../../../../../services/blusky/transactions";

export const processPayInTransactions = (
  platformTransactions: Transaction[],
  context: TransactionProcessingContext
): void => {
  for (const transaction of platformTransactions) {
    processPayInTransaction(transaction, context);
    processRefundTransactions(transaction, context);
  }
};

export const processPayOutTransactions = (
  platformTransactions: Transaction[],
  context: TransactionProcessingContext
): void => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const isRTOPlatform = context.config.platformCode === platformCodes.rto;

  for (const transaction of platformTransactions) {
    processPayOutTransaction(transaction, context);
    processFailedTransactions(transaction, context);

    if (isRTOPlatform) {
      processRejected1Transactions(transaction, context);
      processRTOTypeTransactions(transaction, context);
    }
  }
};

const processPayInTransaction = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    isValidBillableTransaction(transaction, context.config.platformCode) &&
    transaction.status === transactionStatus.success
  ) {
    addTransactionRow(
      context.worksheets.transactionsWorksheet,
      transaction,
      context.rowCounters.transactions
    );
    context.rowCounters.transactions++;
  }
};

const processPayOutTransaction = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionCreatedInDateRange(transaction, context.config) &&
    isValidPlatformTransaction(transaction, context.config.platformCode)
  ) {
    addTransactionRow(
      context.worksheets.transactionsWorksheet,
      transaction,
      context.rowCounters.transactions
    );
    context.rowCounters.transactions++;
  }
};

const processRefundTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  const isRefundStatus =
    transaction.status === transactionStatus.refunded ||
    transaction.status === transactionStatus.fRefund ||
    transaction.status === transactionStatus.refund;

  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    isValidBillableTransaction(transaction, context.config.platformCode) &&
    isRefundStatus &&
    context.worksheets.refundsOrFailedWorksheet
  ) {
    addTransactionRow(
      context.worksheets.refundsOrFailedWorksheet,
      transaction,
      context.rowCounters.refunds
    );
    context.rowCounters.refunds++;
  }
};

const processFailedTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  const { platformCode } = context.config;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const isRTOPlatform = platformCode === platformCodes.rto;

  if (
    !isTransactionUpdatedInDateRange(transaction, context.config) ||
    transaction.platform !== platformCode ||
    transaction.status !== transactionStatus.failed ||
    !context.worksheets.refundsOrFailedWorksheet ||
    (isRTOPlatform && !isNotSpecialRTOTransaction(transaction))
  ) {
    return;
  }

  addTransactionRow(
    context.worksheets.refundsOrFailedWorksheet,
    transaction,
    context.rowCounters.failed
  );
  context.rowCounters.failed++;
};

const processRejected1Transactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    isTransactionUpdatedInDateRange(transaction, context.config) &&
    transaction.status === transactionStatus.rejected1 &&
    context.worksheets.rejected1Worksheet
  ) {
    addTransactionRow(
      context.worksheets.rejected1Worksheet,
      transaction,
      context.rowCounters.rejected1
    );
    context.rowCounters.rejected1++;
  }
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const processRTOTypeTransactions = (
  transaction: Transaction,
  context: TransactionProcessingContext
): void => {
  if (
    !isTransactionCreatedInDateRange(transaction, context.config) ||
    !isValidPlatformTransaction(transaction, context.config.platformCode)
  ) {
    return;
  }

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { transactionID } = transaction;

  if (
    transactionID.endsWith(rtoTransactionSuffixes.R) &&
    context.worksheets.RTO_R_Worksheet
  ) {
    addTransactionRow(
      context.worksheets.RTO_R_Worksheet,
      transaction,
      context.rowCounters.RTO_R
    );
    context.rowCounters.RTO_R++;
  } else if (
    transactionID.endsWith(rtoTransactionSuffixes.P) &&
    context.worksheets.RTO_P_Worksheet
  ) {
    addTransactionRow(
      context.worksheets.RTO_P_Worksheet,
      transaction,
      context.rowCounters.RTO_P
    );
    context.rowCounters.RTO_P++;
  }
};
