import { excelTransactionColumns, platformCodes } from "@constants/settlement";
import { type Platform } from "@constants/transactions/platform";
import { type WorksheetCollection } from "@lib/settlement/approve/types";

import type ExcelJS from "exceljs";

export const createWorksheets = (
  workbook: ExcelJS.Workbook,
  platformCode: Platform,
  isPayInPlatform: boolean,
  isPayOutPlatform: boolean
): WorksheetCollection => {
  const transactionsWorksheet = workbook.addWorksheet("Transactions");
  transactionsWorksheet.columns = excelTransactionColumns;

  const worksheets: WorksheetCollection = {
    transactionsWorksheet,
  };

  if (isPayInPlatform) {
    createPayInWorksheets(workbook, worksheets);
  }

  if (isPayOutPlatform) {
    createPayOutWorksheets(workbook, platformCode, worksheets);
  }

  return worksheets;
};

const createPayInWorksheets = (
  workbook: ExcelJS.Workbook,
  worksheets: WorksheetCollection
): void => {
  const refundsWorksheet = workbook.addWorksheet("Refunds");
  refundsWorksheet.columns = excelTransactionColumns;
  worksheets.refundsOrFailedWorksheet = refundsWorksheet;
};

const createPayOutWorksheets = (
  workbook: ExcelJS.Workbook,
  platformCode: Platform,
  worksheets: WorksheetCollection
): void => {
  const failedWorksheet = workbook.addWorksheet("Failed");
  failedWorksheet.columns = excelTransactionColumns;
  worksheets.refundsOrFailedWorksheet = failedWorksheet;

  if (platformCode === platformCodes.rto) {
    createRTOWorksheets(workbook, worksheets);
  }
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const createRTOWorksheets = (
  workbook: ExcelJS.Workbook,
  worksheets: WorksheetCollection
): void => {
  const rejected1Worksheet = workbook.addWorksheet("REJECTED1");
  rejected1Worksheet.columns = excelTransactionColumns;
  worksheets.rejected1Worksheet = rejected1Worksheet;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const RTO_R_Worksheet = workbook.addWorksheet("RTO_R");
  RTO_R_Worksheet.columns = excelTransactionColumns;
  worksheets.RTO_R_Worksheet = RTO_R_Worksheet;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const RTO_P_Worksheet = workbook.addWorksheet("RTO_P");
  RTO_P_Worksheet.columns = excelTransactionColumns;
  worksheets.RTO_P_Worksheet = RTO_P_Worksheet;
};
