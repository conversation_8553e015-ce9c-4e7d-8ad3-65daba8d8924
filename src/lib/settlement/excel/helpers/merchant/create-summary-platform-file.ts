import { excelNumberFormat, platformCodes } from "@constants/settlement";
import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import {
  type SummaryInformation,
  type ExcelConfig,
  type PlatformData,
} from "@lib/settlement/approve/types";
import { format } from "date-fns";

import { setupWorksheetLogo } from "./setup-worksheet-logo";

import type ExcelJS from "exceljs";

const setupWorksheetFormatting = (worksheet: ExcelJS.Worksheet): void => {
  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 35;
  worksheet.getColumn("C").width = 20;
  worksheet.getColumn("D").width = 20;
  worksheet.getColumn("E").width = 20;

  worksheet.getCell("B12").border = { bottom: { style: "thick" } };
  worksheet.getCell("C12").alignment = { horizontal: "center" };
  worksheet.getCell("C12").font = { bold: true, size: 11 };
  worksheet.getCell("C12").border = { bottom: { style: "thick" } };
  worksheet.getCell("D12").alignment = { horizontal: "center" };
  worksheet.getCell("D12").font = { bold: true, size: 11 };
  worksheet.getCell("D12").border = { bottom: { style: "thick" } };
  worksheet.getCell("E12").font = { bold: true, size: 11 };
  worksheet.getCell("E12").border = { bottom: { style: "thick" } };
  worksheet.getCell("E12").alignment = {
    wrapText: true,
    horizontal: "center",
  };
};

const setupWorksheetHeaders = (
  worksheet: ExcelJS.Worksheet,
  customerTradingName: string,
  fromDate: Date,
  toDate: Date
): void => {
  worksheet.mergeCells("B6:D6");
  worksheet.getCell("B6").alignment = { horizontal: "center" };
  worksheet.getCell("B6").font = { bold: true };
  worksheet.getCell("B6").value = customerTradingName;
  worksheet.getColumn("D").numFmt = excelNumberFormat;
  worksheet.getColumn("E").numFmt = excelNumberFormat;
  worksheet.mergeCells("B8:D8");
  worksheet.getCell("B8").alignment = { horizontal: "center" };
  worksheet.getCell("B8").font = { bold: true };
  worksheet.getCell("B8").value = "Summary Settlement";
  worksheet.mergeCells("B10:D10");
  worksheet.getCell("B10").alignment = { horizontal: "center" };
  worksheet.getCell("B10").font = { bold: true };
  worksheet.getCell("C12").alignment = { horizontal: "center" };
  worksheet.getCell("B13").font = { size: 11 };
  worksheet.getCell("B14").font = { size: 11 };
  worksheet.getCell("B15").font = { size: 11 };
  worksheet.getCell("B16").font = { size: 11 };
  worksheet.getCell("B17").font = { size: 11 };
  worksheet.getCell("B18").font = { size: 11 };
  worksheet.getCell("C10").value =
    format(fromDate, "MMMM do yyyy") + " - " + format(toDate, "MMMM do yyyy");

  worksheet.getCell("C12").value = "# of Transactions";
  worksheet.getCell("D12").value = "Transactions Value";
  worksheet.getCell("E12").value = "Amount Payable/ (Receivable)";
};

const calculatePayInFees = (platform: PlatformData): number => {
  return (
    (platform.totalTransactionAmount ?? 0) -
    (platform.transactionFee ?? 0) -
    (platform.gatewayFee ?? 0) -
    (platform.salesFee ?? 0) -
    (platform.refundFee ?? 0) -
    (platform.totalRefundAmount ?? 0) -
    (platform?.minimumFeeTotal ?? 0)
  );
};

const calculatePayOutFees = (platform: PlatformData): number => {
  return (
    (platform.totalFailedAmount ?? 0) -
    (platform.transactionFee ?? 0) -
    (platform.totalTransactionAmount ?? 0)
  );
};

const calculateExcelSummaryTotalPayable = (
  platformCode: string,
  platform: PlatformData
): number => {
  if (payInGroup.includes(platformCode as Platform)) {
    return calculatePayInFees(platform);
  }

  if (payOutGroup.includes(platformCode as Platform)) {
    return calculatePayOutFees(platform);
  }

  if (platformCode === platformCodes.kyc) {
    return -(platform.totalTransactionAmount ?? 0);
  }

  return 0;
};

export const createSummaryExcelFile = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig,
  summaryInformation: SummaryInformation
) => {
  const {
    customer,
    customerTradingName,
    period: { fromDate, toDate },
    data: { adjustments },
  } = config;

  // Filter and sort platforms for summary display
  // IDP is excluded from the summary sheet following the original business logic:
  // 1. IDP has its own dedicated detailed settlement sheet
  // 2. This follows the established pattern from the original
  // (service/generateStatement.js - oldFA) implementation
  const platformSequence = Object.entries(summaryInformation)
    .sort((a, b) => {
      const aSequence = a[1].displaySequence ?? Number.MAX_SAFE_INTEGER;
      const bSequence = b[1].displaySequence ?? Number.MAX_SAFE_INTEGER;

      return aSequence - bSequence;
    })
    .map(([key]) => key)
    .filter((key) => key !== platformCodes.idp);

  const summaryWorksheet = workbook.addWorksheet(platformCodes.summary, {
    views: [{ showGridLines: false }],
  });

  setupWorksheetLogo(workbook, summaryWorksheet, customer);
  setupWorksheetFormatting(summaryWorksheet);
  setupWorksheetHeaders(
    summaryWorksheet,
    customerTradingName,
    fromDate,
    toDate
  );

  let rowNumber = 13;

  for (const platformCode of platformSequence) {
    const platform = summaryInformation[platformCode];

    if (!platform) {
      continue;
    }

    const platformAdjustmentsTotal = platform.adjustments?.reduce(
      (accumulator, adjustment) => accumulator + adjustment.amount,
      0
    );

    summaryWorksheet.getCell(`B${rowNumber}`).value = platform.labelName;
    summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
      horizontal: "center",
    };
    summaryWorksheet.getCell(`C${rowNumber}`).value = platform.transactionCount;

    if (platformCode === platformCodes.kyc) {
      // KYC does not have transaction amounts - it's charged based on count only
      summaryWorksheet.getCell(`D${rowNumber}`).value = 0;
    } else {
      summaryWorksheet.getCell(`D${rowNumber}`).value =
        (platform.totalTransactionAmount ?? 0) -
        (platform.totalFailedAmount ?? 0);
    }

    const totalPayable = calculateExcelSummaryTotalPayable(
      platformCode,
      platform
    );
    summaryWorksheet.getCell(`E${rowNumber}`).value =
      totalPayable - platformAdjustmentsTotal;
    rowNumber++;
  }

  if (adjustments && adjustments.length > 0) {
    for (const adjustment of adjustments) {
      summaryWorksheet.getCell(`B${rowNumber}`).alignment = {
        horizontal: "right",
      };
      summaryWorksheet.getCell(`B${rowNumber}`).font = { bold: false };
      summaryWorksheet.getCell(`B${rowNumber}`).value = adjustment.label;
      summaryWorksheet.getCell(`E${rowNumber}`).value = -adjustment.amount;

      if (adjustment.displayCommentExcel && adjustment.comment) {
        summaryWorksheet.getCell(`F${rowNumber}`).value = adjustment.comment;
      }

      rowNumber++;
    }
  }

  rowNumber += 2;

  summaryWorksheet.getCell(`D${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`D${rowNumber}`).font = {
    bold: true,
  };
  summaryWorksheet.getCell(`D${rowNumber}`).value = "Net Payout";
  summaryWorksheet.getCell(`E${rowNumber}`).value = {
    formula: `SUM(E13:E${rowNumber - 2})`,
  };
};
