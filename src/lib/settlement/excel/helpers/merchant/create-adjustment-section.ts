import { type AdjustmentDetails } from "@lib/settlement/repository/types";

import type ExcelJS from "exceljs";

export const createAdjustmentsSection = (
  summaryWorksheet: ExcelJS.Worksheet,
  adjustments: AdjustmentDetails[],
  rowNumber: number
): number => {
  if (adjustments && adjustments.length > 0) {
    for (const adjustment of adjustments) {
      summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
        horizontal: "right",
      };
      summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: false };
      summaryWorksheet.getCell(`C${rowNumber}`).value = adjustment.label;
      summaryWorksheet.getCell(`D${rowNumber}`).value = adjustment.amount;

      if (adjustment.displayCommentExcel && adjustment.comment) {
        summaryWorksheet.getCell(`F${rowNumber}`).value = adjustment.comment;
      }

      rowNumber++;
    }
  }

  return rowNumber;
};
