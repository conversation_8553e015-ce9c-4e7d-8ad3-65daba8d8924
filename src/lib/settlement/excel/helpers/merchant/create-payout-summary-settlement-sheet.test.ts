import { type ExcelConfig } from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import { createPayOutSummarySheet } from "./create-payout-summary-settlement-sheet";

const defaultConfig: ExcelConfig = {
  platformCode: "RTO",
  customerTradingName: "Super Customer",
  period: {
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
  },
  fileName: "test-file.xlsx",
  folderPath: "/test/path",
  data: {
    labelName: "Real Time E-Transfer-Out",
    transactionCount: 100,
    totalTransactionAmount: 10_000.5,
    refundCount: 5,
    totalRefundAmount: 500.25,
    gatewayFee: 50,
    transactionFee: 30,
    salesFee: 20,
    refundFee: 2.5,
    totalFailedAmount: 0,
    endBalance: 9000,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnAmountRTO_R: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnAmountRTO: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnCountRTO: 0,
    isAdjusted: false,
    adjustments: [],
    meta: {
      feesBreakdown: [
        {
          interval: {
            fromDate: { year: 2024, month: 1, day: 1 },
            toDate: { year: 2024, month: 1, day: 31 },
          },
          chargedFees: {
            gatewayFeeTotal: 50,
            transactionFeeTotal: 30,
            salesFeeTotal: 20,
            refundFeeTotal: 2.5,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          meta: {
            rates: {
              gatewayFee: 0.005,
              transactionFee: 2.7,
              salesFee: 0.02,
              refundFee: 0.0025,
              reject1Fee: 0.03,
              minimumThreshold: 0,
              minimumCharge: 0,
              isMinimumFeeApplicable: false,
              transactionTierItem: {
                id: 1,
                maxAmount: 1_000_000,
                minAmount: 0,
                transactionFee: 2.7,
                salesFee: 0.02,
              },
              rejectOneTierItem: {
                id: 1,
                maxAmount: 1_000_000,
                minAmount: 0,
                transactionFee: 2.7,
                salesFee: 0.02,
              },
              delayMonths: {
                isDelayApplicable: false,
                delayInMonths: 0,
                tierItem: undefined,
              },
            },
            rateDeterminingProfile: {
              fromDate: { year: 2024, month: 1, day: 1 },
              toDate: { year: 2024, month: 1, day: 31 },
              combineTotal: 10_000.5,
              isCombineIdpTierSet: false,
              isCombineAchTierSet: false,
              isCombineRtoTierSet: false,
              serviceNumbers: [],
              isCombineMultipleServicesEnabled: false,
            },
            transactionSummary: {
              transactionCount: 100,
              totalTransactionAmount: 10_000.5,
              totalFailedAmount: 0,
              failedCount: 0,
              refundCount: 5,
              totalRefundAmount: 500.25,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              total_RAmount: 0,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              _RCount: 0,
              rejected1Count: 0,
              totalRejected1Amount: 0,
              minimumAmountCount: 0,
              totalMinimumAmount: 0,
              partialReturnCount: 0,
              totalPartialReturnAmount: 0,
            },
          },
        },
      ],
    },
  },
  customer: {
    entityName: "Gigadat",
    entityLogoName: "logo-gigadat.png",
  },
};

describe("createPayOutSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a SUMMARY worksheet with correct configuration", () => {
    createPayOutSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Super Customer");
    expect(worksheet!.getCell("B7").value).toBe("Real Time E-Transfer-Out");
  });

  it("should handle RTO platform with special transaction formulas", () => {
    createPayOutSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("C13").value).toEqual({
      formula: "COUNT(Transactions!C:C)-COUNT(RTO_R!F:F)",
    });
    expect(worksheet!.getCell("D13").value).toEqual({
      formula:
        "SUM(Transactions!F:F)-SUM(Failed!F:F)-SUM(RTO_R!F:F)-SUM(RTO_P!F:F)",
    });

    expect(worksheet!.getCell("B14").value).toBe("Rejected1 Transactions");
    expect(worksheet!.getCell("C14").value).toEqual({
      formula: "COUNT(REJECTED1!F:F)",
    });
  });

  it("should handle non-RTO platforms with standard formulas", () => {
    const etoConfig: ExcelConfig = {
      ...defaultConfig,
      platformCode: "ETO",
      data: {
        ...defaultConfig.data,
        labelName: "Interac e-Transfer Pay-Outs",
      },
    };

    createPayOutSummarySheet(workbook, etoConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("C13").value).toEqual({
      formula: "COUNT(Transactions!C:C)",
    });
    expect(worksheet!.getCell("D13").value).toEqual({
      formula: "SUM(Transactions!F:F)-SUM(Failed!F:F)",
    });

    expect(worksheet!.getCell("B14").value).not.toBe("Rejected1 Transactions");
  });

  it("should calculate transaction fee correctly for RTO platform", () => {
    createPayOutSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("D16").value).toEqual({
      formula: "C13*2.7 + C14*2.7",
    });
  });

  it("should calculate transaction fee correctly for non-RTO platforms", () => {
    const achConfig: ExcelConfig = {
      ...defaultConfig,
      platformCode: "ACH",
      data: {
        ...defaultConfig.data,
        labelName: "Express Connect ACH Pay-Outs",
      },
    };

    createPayOutSummarySheet(workbook, achConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("D15").value).toEqual({
      formula: "C13*2.7",
    });
  });

  it("should display correct platform names", () => {
    const platformTests = [
      { code: "RTX", expectedName: "Realtime Out" },
      { code: "ETO", expectedName: "Interac e-Transfer Pay-Outs" },
      { code: "ACH", expectedName: "Express Connect ACH Pay-Outs" },
      { code: "RTO", expectedName: "Real Time E-Transfer-Out" },
      { code: "ANR", expectedName: "Express Connect ANR Pay-Outs" },
      { code: "ANX", expectedName: "Express Connect ANX Pay-Outs" },
    ];

    for (const { code, expectedName } of platformTests) {
      const testConfig: ExcelConfig = {
        ...defaultConfig,
        platformCode: code,
        data: {
          ...defaultConfig.data,
          labelName: expectedName,
        },
      };

      const testWorkbook = new ExcelJS.Workbook();
      createPayOutSummarySheet(testWorkbook, testConfig);

      const worksheet = testWorkbook.getWorksheet("SUMMARY");
      expect(worksheet!.getCell("B13").value).toBe(expectedName);
    }
  });

  it("should calculate total costs and net payout correctly", () => {
    createPayOutSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("D18").value).toEqual({
      formula: "SUM(D15:D17)",
    });

    expect(worksheet!.getCell("D19").value).toEqual({
      formula: "D13+D18",
    });
  });

  it("should set correct labels and formatting", () => {
    createPayOutSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("C16").value).toBe("Transaction Fee");
    expect(worksheet!.getCell("C18").value).toBe("TOTAL COSTS");
    expect(worksheet!.getCell("C19").value).toBe(
      "Total to Net against Pay-In (CAD)"
    );

    expect(worksheet!.getCell("C12").value).toBe("# of Transactions");
    expect(worksheet!.getCell("D12").value).toBe("CAD");
  });

  it("should display adjustments when they exist", () => {
    const configWithAdjustments = {
      ...defaultConfig,
      data: {
        ...defaultConfig.data,
        adjustments: [
          {
            id: 1,
            label: "Returned/Cancelled Payments",
            amount: -100,
            displayCommentExcel: true,
            comment: "Payments returned by customer",
          },
          {
            id: 2,
            label: "Reversed Payment",
            amount: -50,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
    };

    createPayOutSummarySheet(workbook, configWithAdjustments);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // Find the adjustment rows by looking for the adjustment labels
    let returnedPaymentsFound = false;
    let reversedPaymentFound = false;

    // Check rows after the transaction fee (around row 15-25)
    for (let rowIndex = 15; rowIndex <= 30; rowIndex++) {
      const cellValue = worksheet!.getCell(`C${rowIndex}`).value;

      if (cellValue === "Returned/Cancelled Payments") {
        expect(worksheet!.getCell(`D${rowIndex}`).value).toBe(-100);
        expect(worksheet!.getCell(`F${rowIndex}`).value).toBe(
          "Payments returned by customer"
        );
        returnedPaymentsFound = true;
      }

      if (cellValue === "Reversed Payment") {
        expect(worksheet!.getCell(`D${rowIndex}`).value).toBe(-50);
        // Should not have comment since displayCommentExcel is false
        expect(worksheet!.getCell(`F${rowIndex}`).value).toBeNull();
        reversedPaymentFound = true;
      }
    }

    expect(returnedPaymentsFound).toBe(true);
    expect(reversedPaymentFound).toBe(true);
  });
});
