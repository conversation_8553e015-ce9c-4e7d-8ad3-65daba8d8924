import { type ExcelConfig } from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import { createKycSummarySheet } from "./create-kyc-summary-settlement-sheet";

// Mock KYC descriptions
const mockKycDescriptions = {
  KY1: "Know Your Customer Level 1",
  KY2: "Know Your Customer Level 2",
  KY3: "Know Your Customer Level 3",
};

const defaultConfig: ExcelConfig = {
  platformCode: "KYC",
  customerTradingName: "Test Customer",
  period: {
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
  },
  fileName: "test-kyc.xlsx",
  folderPath: "/test/path",
  data: {
    labelName: "Know Your Customer (KYC)",
    transactionCount: 100,
    totalTransactionAmount: 1000,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 1000,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    txnAmountRTO_R: 0,
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    partialReturnAmountRTO: 0,
    partialReturnCountRTO: 0,
    isAdjusted: false,
    adjustments: [],
    kycDetails: {
      KY1: {
        transactionCount: 50,
        totalTransactionAmount: 500,
      },
      KY2: {
        transactionCount: 30,
        totalTransactionAmount: 300,
      },
      KY3: {
        transactionCount: 20,
        totalTransactionAmount: 200,
      },
    },
  },
  customer: {
    entityName: "Gigadat",
    entityLogoName: "logo-gigadat.png",
  },
  kycDescriptions: mockKycDescriptions,
};

describe("createKycSummarySheet", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a SUMMARY worksheet with correct configuration", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();
    expect(worksheet!.getCell("B6").value).toBe("Test Customer");
    expect(worksheet!.getCell("B8").value).toBe("KYC Summary");
  });

  it("should set KYC type details correctly", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // Check KYC type rows
    expect(worksheet!.getCell("B15").value).toBe("KY1");
    expect(worksheet!.getCell("C15").value).toBe("Know Your Customer Level 1");
    expect(worksheet!.getCell("D15").value).toBe(50);
    expect(worksheet!.getCell("E15").value).toBe(500);

    expect(worksheet!.getCell("B16").value).toBe("KY2");
    expect(worksheet!.getCell("C16").value).toBe("Know Your Customer Level 2");
    expect(worksheet!.getCell("D16").value).toBe(30);
    expect(worksheet!.getCell("E16").value).toBe(300);

    expect(worksheet!.getCell("B17").value).toBe("KY3");
    expect(worksheet!.getCell("C17").value).toBe("Know Your Customer Level 3");
    expect(worksheet!.getCell("D17").value).toBe(20);
    expect(worksheet!.getCell("E17").value).toBe(200);
  });

  it("should calculate sub total correctly", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // Sub total should be at row 20 (15 + 3 KYC types + 2 gap rows)
    expect(worksheet!.getCell("C20").value).toBe("Sub Total");
    expect(worksheet!.getCell("D20").value).toBe(100); // 50 + 30 + 20
    expect(worksheet!.getCell("E20").value).toBe(1000); // 500 + 300 + 200
  });

  it("should create individual KYC type worksheets", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    // Check that individual KYC type worksheets are created
    expect(workbook.getWorksheet("KY1")).toBeDefined();
    expect(workbook.getWorksheet("KY2")).toBeDefined();
    expect(workbook.getWorksheet("KY3")).toBeDefined();
  });

  it("should handle adjustments correctly", async () => {
    const configWithAdjustments: ExcelConfig = {
      ...defaultConfig,
      data: {
        ...defaultConfig.data,
        adjustments: [
          {
            id: 1,
            label: "Test Adjustment",
            amount: 100,
            displayCommentExcel: true,
            comment: "Test comment",
          },
        ],
      },
    };

    await createKycSummarySheet(workbook, configWithAdjustments);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // Adjustment should be at row 18 (17 + 1 gap row)
    expect(worksheet!.getCell("C18").value).toBe("Test Adjustment");
    expect(worksheet!.getCell("E18").value).toBe(100);
    expect(worksheet!.getCell("F18").value).toBe("Test comment");
  });

  it("should calculate total payable correctly", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    // Total Payable should be at row 18 (17 + 1 gap row)
    expect(worksheet!.getCell("C18").value).toBe("Total Payable");
    expect(worksheet!.getCell("E18").value).toEqual({
      formula: "SUM(E18:E18)",
    });
  });

  it("should set correct cell alignments for KYC types", async () => {
    await createKycSummarySheet(workbook, defaultConfig);

    const worksheet = workbook.getWorksheet("SUMMARY");

    expect(worksheet).toBeDefined();

    expect(worksheet!.getCell("D15").alignment).toEqual({
      horizontal: "center",
    });
  });
});
