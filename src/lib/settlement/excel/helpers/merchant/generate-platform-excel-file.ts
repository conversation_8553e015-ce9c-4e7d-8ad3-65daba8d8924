import { platformCodes } from "@constants/settlement";
import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import {
  type SummaryInformation,
  type ExcelConfig,
} from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";

import { createKycSummarySheet } from "./create-kyc-summary-settlement-sheet";
import { createPayInSummarySheet } from "./create-payin-summary-settlement-sheet";
import { createPayOutSummarySheet } from "./create-payout-summary-settlement-sheet";
import { createSummaryExcelFile } from "./create-summary-platform-file";
import { createTransactionsSheets } from "./create-transactions-settlement-sheet";
import { type Transaction } from "../../../../../services/blusky/transactions";
import { writeExcelFile } from "../write-excel-file";

export const generatePlatformExcelFile = async (
  platformTransactions: Transaction[],
  config: ExcelConfig,
  summaryInformation: SummaryInformation
): Promise<void> => {
  const { folderPath, fileName } = config;

  const workbook = new ExcelJS.Workbook();

  if (payInGroup.includes(config.platformCode as Platform)) {
    createPayInSummarySheet(workbook, config);
    createTransactionsSheets(workbook, config, platformTransactions);
  } else if (payOutGroup.includes(config.platformCode as Platform)) {
    createPayOutSummarySheet(workbook, config);
    createTransactionsSheets(workbook, config, platformTransactions);
  } else if (config.platformCode === platformCodes.summary) {
    createSummaryExcelFile(workbook, config, summaryInformation);
  } else if (config.platformCode === platformCodes.kyc) {
    await createKycSummarySheet(workbook, config);
  }

  await writeExcelFile(workbook, folderPath, fileName);
};
