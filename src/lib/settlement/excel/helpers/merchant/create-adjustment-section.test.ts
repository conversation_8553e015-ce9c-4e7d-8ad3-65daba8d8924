import ExcelJS from "exceljs";

import { createAdjustmentsSection } from "./create-adjustment-section";

import type { AdjustmentDetails } from "@lib/settlement/repository/types";

const createMockAdjustment = (
  overrides: Partial<AdjustmentDetails> = {}
): AdjustmentDetails => ({
  id: 1,
  label: "Test Adjustment",
  amount: 100.5,
  displayCommentExcel: false,
  comment: "Test comment",
  ...overrides,
});

describe("createAdjustmentsSection", () => {
  let workbook: ExcelJS.Workbook;
  let summaryWorksheet: ExcelJS.Worksheet;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
    summaryWorksheet = workbook.addWorksheet("Summary");
  });

  it("should create adjustment section with single adjustment", () => {
    const adjustments: AdjustmentDetails[] = [
      createMockAdjustment({
        label: "Service Fee Adjustment",
        amount: -25,
        displayCommentExcel: true,
        comment: "Monthly service fee deduction",
      }),
    ];
    const startRowNumber = 10;

    createAdjustmentsSection(summaryWorksheet, adjustments, startRowNumber);

    // Check label cell (C10)
    const labelCell = summaryWorksheet.getCell("C10");
    expect(labelCell.value).toBe("Service Fee Adjustment");
    expect(labelCell.alignment).toEqual({ horizontal: "right" });
    expect(labelCell.font).toEqual({ bold: false });

    // Check amount cell (D10)
    const amountCell = summaryWorksheet.getCell("D10");
    expect(amountCell.value).toBe(-25);

    // Check comment cell (F10) - should be present since displayCommentExcel is true
    const commentCell = summaryWorksheet.getCell("F10");
    expect(commentCell.value).toBe("Monthly service fee deduction");
  });

  it("should create adjustment section with multiple adjustments", () => {
    const adjustments: AdjustmentDetails[] = [
      createMockAdjustment({
        id: 1,
        label: "First Adjustment",
        amount: 50,
        displayCommentExcel: false,
        comment: "First comment",
      }),
      createMockAdjustment({
        id: 2,
        label: "Second Adjustment",
        amount: -30,
        displayCommentExcel: true,
        comment: "Second comment",
      }),
      createMockAdjustment({
        id: 3,
        label: "Third Adjustment",
        amount: 15.5,
        displayCommentExcel: true,
        comment: "Third comment",
      }),
    ];
    const startRowNumber = 15;

    createAdjustmentsSection(summaryWorksheet, adjustments, startRowNumber);

    // Check first adjustment (row 15)
    expect(summaryWorksheet.getCell("C15").value).toBe("First Adjustment");
    expect(summaryWorksheet.getCell("D15").value).toBe(50);
    expect(summaryWorksheet.getCell("F15").value).toBeNull(); // No comment since displayCommentExcel is false

    // Check second adjustment (row 16)
    expect(summaryWorksheet.getCell("C16").value).toBe("Second Adjustment");
    expect(summaryWorksheet.getCell("D16").value).toBe(-30);
    expect(summaryWorksheet.getCell("F16").value).toBe("Second comment");

    // Check third adjustment (row 17)
    expect(summaryWorksheet.getCell("C17").value).toBe("Third Adjustment");
    expect(summaryWorksheet.getCell("D17").value).toBe(15.5);
    expect(summaryWorksheet.getCell("F17").value).toBe("Third comment");
  });

  it("should handle adjustment without comment when displayCommentExcel is false", () => {
    const adjustments: AdjustmentDetails[] = [
      createMockAdjustment({
        label: "No Comment Adjustment",
        amount: 75.25,
        displayCommentExcel: false,
        comment: "This comment should not appear",
      }),
    ];
    const startRowNumber = 20;

    createAdjustmentsSection(summaryWorksheet, adjustments, startRowNumber);

    expect(summaryWorksheet.getCell("C20").value).toBe("No Comment Adjustment");
    expect(summaryWorksheet.getCell("D20").value).toBe(75.25);
    expect(summaryWorksheet.getCell("F20").value).toBeNull();
  });
});
