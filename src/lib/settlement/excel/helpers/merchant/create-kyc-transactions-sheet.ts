import { kycTransactionColumns } from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";

import { type KycTxn } from "../../../../../services/blusky/transactions";
import { addKycTransactionRow } from "../create-kyc-transaction-excel-row";

import type ExcelJS from "exceljs";

export const createKycTransactionsSheets = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig,
  kycTransactions: KycTxn[]
): void => {
  const { kycDetails } = config.data;

  const kycNames = Object.keys(kycDetails ?? {});

  if (kycNames.length === 0) {
    return;
  }

  for (const kycName of kycNames) {
    const kycWorksheet = workbook.addWorksheet(kycName);
    kycWorksheet.columns = kycTransactionColumns;
  }

  const rowCounters: Record<string, number> = {};

  for (const kycTransaction of kycTransactions) {
    const kycName = kycTransaction.type;

    if (rowCounters[kycName]) {
      rowCounters[kycName]++;
    } else {
      rowCounters[kycName] = 2;
    }

    const kycWorksheet = workbook.getWorksheet(kycName);

    if (kycWorksheet) {
      addKycTransactionRow(kycWorksheet, kycTransaction, rowCounters[kycName]!);
    }
  }
};
