import { type ExcelConfig } from "@lib/settlement/approve/types";
import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import { createKycTransactionsSheets } from "./create-kyc-transactions-sheet";
import { type KycTxn } from "../../../../../services/blusky/transactions";

const mockKycDescriptions = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  KY1: "Know Your Customer Level 1",
  // eslint-disable-next-line @typescript-eslint/naming-convention
  KY2: "Know Your Customer Level 2",
  // eslint-disable-next-line @typescript-eslint/naming-convention
  KY3: "Know Your Customer Level 3",
};

const defaultConfig: ExcelConfig = {
  platformCode: "KYC",
  customerTradingName: "Test Customer",
  period: {
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-31"),
  },
  fileName: "test-kyc.xlsx",
  folderPath: "/test/path",
  data: {
    labelName: "Know Your Customer (KYC)",
    transactionCount: 100,
    totalTransactionAmount: 1000,
    refundCount: 0,
    totalRefundAmount: 0,
    gatewayFee: 0,
    transactionFee: 0,
    salesFee: 0,
    refundFee: 0,
    totalFailedAmount: 0,
    endBalance: 1000,
    total2FaRejectAmount: 0,
    total2FaRejectCount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnAmountRTO_R: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    txnCountETI_R1: 0,
    minimumFeeTotal: 0,
    minimumFeeCount: 0,
    totalMinimumAmount: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnAmountRTO: 0,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partialReturnCountRTO: 0,
    isAdjusted: false,
    adjustments: [],
    kycDetails: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      KY1: {
        transactionCount: 50,
        totalTransactionAmount: 500,
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      KY2: {
        transactionCount: 30,
        totalTransactionAmount: 300,
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      KY3: {
        transactionCount: 20,
        totalTransactionAmount: 200,
      },
    },
  },
  customer: {
    entityName: "Gigadat",
    entityLogoName: "logo-gigadat.png",
  },
  kycDescriptions: mockKycDescriptions,
};

const mockKycTransactions: KycTxn[] = [
  {
    serviceNumber: "SRV001",
    name: "John Doe",
    email: "<EMAIL>",
    type: "KY1",
    clientUserId: "USER001",
    status: "COMPLETED",
    dispCreated: "2024-01-15 10:30:00",
    dispUpdated: "2024-01-15 10:30:00",
  },
  {
    serviceNumber: "SRV002",
    name: "Jane Smith",
    email: "<EMAIL>",
    type: "KY2",
    clientUserId: "USER002",
    status: "PENDING",
    dispCreated: "2024-01-16 14:20:00",
    dispUpdated: "2024-01-16 14:20:00",
  },
  {
    serviceNumber: "SRV003",
    name: "Bob Johnson",
    email: "<EMAIL>",
    type: "KY1",
    clientUserId: "USER003",
    status: "COMPLETED",
    dispCreated: "2024-01-17 09:15:00",
    dispUpdated: "2024-01-17 09:15:00",
  },
  {
    serviceNumber: "SRV004",
    name: "Alice Brown",
    email: "<EMAIL>",
    type: "KY3",
    clientUserId: "USER004",
    status: "FAILED",
    dispCreated: "2024-01-18 16:45:00",
    dispUpdated: "2024-01-18 16:45:00",
  },
];

describe("createKycTransactionsSheets", () => {
  let workbook: ExcelJS.Workbook;

  beforeEach(() => {
    workbook = new ExcelJS.Workbook();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create worksheets for each KYC type", () => {
    createKycTransactionsSheets(workbook, defaultConfig, mockKycTransactions);

    expect(workbook.getWorksheet("KY1")).toBeDefined();
    expect(workbook.getWorksheet("KY2")).toBeDefined();
    expect(workbook.getWorksheet("KY3")).toBeDefined();
  });

  it("should set correct column configuration for each worksheet", () => {
    createKycTransactionsSheets(workbook, defaultConfig, mockKycTransactions);

    const ky1Worksheet = workbook.getWorksheet("KY1");
    const ky2Worksheet = workbook.getWorksheet("KY2");
    const ky3Worksheet = workbook.getWorksheet("KY3");

    expect(ky1Worksheet).toBeDefined();
    expect(ky2Worksheet).toBeDefined();
    expect(ky3Worksheet).toBeDefined();

    expect(ky1Worksheet!.columns).toBeDefined();
    expect(ky2Worksheet!.columns).toBeDefined();
    expect(ky3Worksheet!.columns).toBeDefined();
  });

  it("should not create worksheets if kycDetails is empty", () => {
    const configWithoutKycDetails: ExcelConfig = {
      ...defaultConfig,
      data: {
        ...defaultConfig.data,
        kycDetails: {},
      },
    };

    createKycTransactionsSheets(
      workbook,
      configWithoutKycDetails,
      mockKycTransactions
    );

    expect(workbook.getWorksheet("KY1")).toBeUndefined();
    expect(workbook.getWorksheet("KY2")).toBeUndefined();
    expect(workbook.getWorksheet("KY3")).toBeUndefined();
  });

  it("should handle empty transactions array gracefully", () => {
    createKycTransactionsSheets(workbook, defaultConfig, []);

    expect(workbook.getWorksheet("KY1")).toBeDefined();
    expect(workbook.getWorksheet("KY2")).toBeDefined();
    expect(workbook.getWorksheet("KY3")).toBeDefined();
  });

  it("should add transactions to correct worksheets with correct row numbers", () => {
    createKycTransactionsSheets(workbook, defaultConfig, mockKycTransactions);

    const ky1Worksheet = workbook.getWorksheet("KY1");
    const ky2Worksheet = workbook.getWorksheet("KY2");
    const ky3Worksheet = workbook.getWorksheet("KY3");

    expect(ky1Worksheet).toBeDefined();
    expect(ky2Worksheet).toBeDefined();
    expect(ky3Worksheet).toBeDefined();

    // KY1 should have 2 transactions (rows 2 and 3)
    expect(ky1Worksheet!.getCell("A2").value).toBe("SRV001");
    expect(ky1Worksheet!.getCell("A3").value).toBe("SRV003");

    // KY2 should have 1 transaction (row 2)
    expect(ky2Worksheet!.getCell("A2").value).toBe("SRV002");

    // KY3 should have 1 transaction (row 2)
    expect(ky3Worksheet!.getCell("A2").value).toBe("SRV004");
  });
});
