import ExcelJS from "exceljs";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

import { addKycTransactionRow } from "./create-kyc-transaction-excel-row";
import { type KycTxn } from "../../../../services/blusky/transactions";

describe("addKycTransactionRow", () => {
  let worksheet: ExcelJS.Worksheet;

  beforeEach(() => {
    const workbook = new ExcelJS.Workbook();
    worksheet = workbook.addWorksheet("Test");
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should add transaction data to correct cells", () => {
    const transaction: KycTxn = {
      serviceNumber: "SRV001",
      name: "<PERSON>",
      email: "<EMAIL>",
      type: "KY1",
      clientUserId: "USER001",
      status: "COMPLETED",
      dispCreated: "2024-01-15 10:30:00",
      dispUpdated: "2024-01-15 10:30:00",
    };

    addKycTransactionRow(worksheet, transaction, 2);

    expect(worksheet.getCell("A2").value).toBe("SRV001");
    expect(worksheet.getCell("B2").value).toBe("John Doe");
    expect(worksheet.getCell("C2").value).toBe("<EMAIL>");
    expect(worksheet.getCell("D2").value).toBe("KY1");
    expect(worksheet.getCell("E2").value).toBe("USER001");
    expect(worksheet.getCell("F2").value).toBe("COMPLETED");
    expect(worksheet.getCell("G2").value).toBe("2024-01-15 10:30:00");
    expect(worksheet.getCell("H2").value).toBe("2024-01-15 10:30:00");
  });

  it("should handle different row numbers correctly", () => {
    const transaction: KycTxn = {
      serviceNumber: "SRV002",
      name: "Jane Smith",
      email: "<EMAIL>",
      type: "KY2",
      clientUserId: "USER002",
      status: "PENDING",
      dispCreated: "2024-01-16 14:20:00",
      dispUpdated: "2024-01-16 14:20:00",
    };

    addKycTransactionRow(worksheet, transaction, 5);

    expect(worksheet.getCell("A5").value).toBe("SRV002");
    expect(worksheet.getCell("B5").value).toBe("Jane Smith");
    expect(worksheet.getCell("C5").value).toBe("<EMAIL>");
    expect(worksheet.getCell("D5").value).toBe("KY2");
    expect(worksheet.getCell("E5").value).toBe("USER002");
    expect(worksheet.getCell("F5").value).toBe("PENDING");
    expect(worksheet.getCell("G5").value).toBe("2024-01-16 14:20:00");
    expect(worksheet.getCell("H5").value).toBe("2024-01-16 14:20:00");
  });
});
