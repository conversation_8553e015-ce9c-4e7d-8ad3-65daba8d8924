import { readFileSync } from "node:fs";

import { entities } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";
import { settlementTypes } from "@constants/settlement";
import { payInGroup, type Platform } from "@constants/transactions/platform";
import ExcelJS from "exceljs";

import {
  type BuyRateSummary,
  generateBuyRatePlatformSheet,
  generateBuyRateSummarySheet,
} from "./non-merchant/buy-rate";
import { checkSettlementDatesMatch } from "./non-merchant/check-settlement-dates";
import { generateCombinationSummarySheet } from "./non-merchant/combination";
import {
  type CommissionRateSummary,
  generateCommissionRatePlatformSheet,
  generateCommissionRateSummarySheet,
} from "./non-merchant/commission-rate";
import { writeExcelFile } from "./write-excel-file";

import type {
  NonMerchantFeesBreakdown,
  SettlementResult,
} from "../../functions/workers/types";
import type { Customer } from "@lib/customer/repository/types";
import type { MerchantPlatformCommission } from "@lib/settlement/functions/workers/calculate-non-merchant-settlement";
import type { DateOnly } from "@utils/date-only";

export const generateNonMerchantExcel = async (
  settlementResult: SettlementResult,
  customer: Customer,
  fileName: string
): Promise<void> => {
  const workbook = new ExcelJS.Workbook();

  if (!checkSettlementDatesMatch(settlementResult)) {
    throw new Error("Settlement dates do not match across platforms");
  }

  const firstPlatformKey = Object.keys(settlementResult.settlement)[0]!;
  const firstFeesBreakdown = settlementResult.settlement[firstPlatformKey]!
    .feesBreakdown as NonMerchantFeesBreakdown;
  const { interval } = firstFeesBreakdown;

  switch (customer.nonMerchantSettlementType) {
    case settlementTypes.commissionRate: {
      _generateCommissionRateExcel(
        settlementResult,
        customer,
        interval,
        workbook
      );
      break;
    }

    case settlementTypes.buyRate: {
      _generateBuyRateExcel(settlementResult, customer, interval, workbook);
      break;
    }

    case settlementTypes.combination: {
      _generateCombinationExcel(settlementResult, customer, interval, workbook);
      break;
    }

    default: {
      throw new Error("Unknown non-merchant settlement type");
    }
  }

  const image = workbook.addImage({
    buffer: readFileSync(`${baseDirectoryName}/${customer.entityLogoName}`),
    extension: "png",
  });

  workbook.eachSheet((worksheet) => {
    if (customer.entityName === entities.gigadat) {
      worksheet.addImage(image, "B1:C4");
    } else {
      worksheet.addImage(image, "C1:C5");
    }
  });

  await writeExcelFile(workbook, customer.statementFolderLocation, fileName);
};

const _generateCommissionRateExcel = (
  settlementResult: SettlementResult,
  customer: Customer,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { customerTradingName } = customer;

  const summaries: CommissionRateSummary[] = [];

  const sortedPlatformSettlementEntries =
    _getSortedPlatformSettlementEntries(settlementResult);

  for (const [, platformSettlement] of sortedPlatformSettlementEntries) {
    const { platformDescription } = platformSettlement;

    const { transactionCount, totalTransactionAmount } =
      platformSettlement.totalTransactionSummary;

    const { totalTransactionCommission } =
      platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

    summaries.push({
      platformDescription,
      transactionCount,
      totalTransactionAmount,
      commission: totalTransactionCommission,
    });
  }

  generateCommissionRateSummarySheet(
    summaries,
    customerTradingName,
    interval,
    workbook
  );

  for (const [
    platformKey,
    platformSettlement,
  ] of sortedPlatformSettlementEntries) {
    const platform = platformKey as Platform;

    // Hard coded to use only sales fee for pay-ins and only transaction fee for pay-outs
    // Update in FA-999 to allow for more flexibility
    const feeType = payInGroup.includes(platform)
      ? "salesFee"
      : "transactionFee";

    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: {
        feeType,
      },
    });
  }
};

const _generateBuyRateExcel = (
  settlementResult: SettlementResult,
  customer: Customer,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { customerTradingName } = customer;

  const merchantPlatformData = [];

  for (const [platformKey, platformSettlement] of Object.entries(
    settlementResult.settlement
  )) {
    const platform = platformKey as Platform;

    const { platformDescription } = platformSettlement;

    const feesBreakdown =
      platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

    for (const merchantPlatformCommission of Object.values(
      feesBreakdown.merchantBreakdown
    )) {
      merchantPlatformData.push({
        platform,
        platformDescription,
        merchantPlatformCommission,
      });
    }
  }

  // Sort by ascending alphabetical order of merchant names
  merchantPlatformData.sort((a, b) =>
    a.merchantPlatformCommission.merchantName.localeCompare(
      b.merchantPlatformCommission.merchantName,
      "en"
    )
  );

  const summaries = _getBuyRateSummaries(merchantPlatformData);

  generateBuyRateSummarySheet(
    summaries,
    customerTradingName,
    interval,
    workbook
  );

  for (const {
    platform,
    platformDescription,
    merchantPlatformCommission,
  } of merchantPlatformData) {
    const data = {
      customerTradingName,
      fromDate: interval.fromDate,
      toDate: interval.toDate,
      platform,
      platformDescription,
      merchantPlatformCommission,
    };

    const options = {
      includeGatewayFee: payInGroup.includes(platform),
    };

    generateBuyRatePlatformSheet(data, workbook, options);
  }
};

const _generateCombinationExcel = (
  settlementResult: SettlementResult,
  customer: Customer,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { customerTradingName } = customer;

  const commissionRateSummaries: CommissionRateSummary[] = [];
  let buyRateSummaries: BuyRateSummary[] = [];

  const merchantPlatformData: Array<{
    platform: Platform;
    platformDescription: string;
    merchantPlatformCommission: MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: {
        fromDate: DateOnly;
        toDate: DateOnly;
      };
    };
  }> = [];

  const sortedPlatformSettlementEntries =
    _getSortedPlatformSettlementEntries(settlementResult);

  for (const [
    platformKey,
    platformSettlement,
  ] of sortedPlatformSettlementEntries) {
    const platform = platformKey as Platform;

    const { platformDescription } = platformSettlement;

    const { transactionCount, totalTransactionAmount } =
      platformSettlement.totalTransactionSummary;

    const { totalTransactionCommission, merchantBreakdown } =
      platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

    commissionRateSummaries.push({
      platformDescription,
      transactionCount,
      totalTransactionAmount,
      commission: totalTransactionCommission,
    });

    for (const merchantPlatformCommission of Object.values(merchantBreakdown)) {
      merchantPlatformData.push({
        platform,
        platformDescription,
        merchantPlatformCommission,
      });
    }
  }

  // Sort by ascending alphabetical order of merchant names
  merchantPlatformData.sort((a, b) =>
    a.merchantPlatformCommission.merchantName.localeCompare(
      b.merchantPlatformCommission.merchantName,
      "en"
    )
  );

  buyRateSummaries = _getBuyRateSummaries(merchantPlatformData);

  generateCombinationSummarySheet({
    commissionRateSummaries,
    buyRateSummaries,
    customerTradingName,
    interval,
    workbook,
  });

  // Generate commission rate platform sheets
  for (const [
    platformKey,
    platformSettlement,
  ] of sortedPlatformSettlementEntries) {
    const platform = platformKey as Platform;

    // Hard coded to use only sales fee for pay-ins and only transaction fee for pay-outs
    // Update in FA-999 to allow for more flexibility
    const feeType = payInGroup.includes(platform)
      ? "salesFee"
      : "transactionFee";

    generateCommissionRatePlatformSheet({
      customerTradingName,
      platform,
      platformSettlement,
      workbook,
      options: {
        feeType,
      },
    });
  }

  // Generate buy rate platform sheets
  for (const {
    platform,
    platformDescription,
    merchantPlatformCommission,
  } of merchantPlatformData) {
    const data = {
      customerTradingName,
      fromDate: interval.fromDate,
      toDate: interval.toDate,
      platform,
      platformDescription,
      merchantPlatformCommission,
    };

    const options = {
      includeGatewayFee: payInGroup.includes(platform),
    };

    generateBuyRatePlatformSheet(data, workbook, options);
  }
};

const _getBuyRateSummaries = (
  merchantPlatformData: Array<{
    platform: Platform;
    platformDescription: string;
    merchantPlatformCommission: MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: {
        fromDate: DateOnly;
        toDate: DateOnly;
      };
    };
  }>
): BuyRateSummary[] => {
  const groupedMerchantSummary: Record<string, BuyRateSummary> = {};

  for (const data of merchantPlatformData) {
    const { platform, platformDescription, merchantPlatformCommission } = data;
    const { merchantName, serviceNumber } = merchantPlatformCommission;

    // Initialize the merchant summary if it doesn't exist
    groupedMerchantSummary[serviceNumber] ??= {
      merchantName,
      platformCommission: {},
    };

    groupedMerchantSummary[serviceNumber]!.platformCommission[platform] = {
      platformDescription,
      commission: merchantPlatformCommission.fees.salesCommission,
    };
  }

  const buyRateSummaries: BuyRateSummary[] = Object.values(
    groupedMerchantSummary
  ).map((summary) => ({
    merchantName: summary.merchantName,
    platformCommission: summary.platformCommission,
  }));

  return buyRateSummaries;
};

const _getSortedPlatformSettlementEntries = (
  settlementResult: SettlementResult
) => {
  // Sort by platform display sequence in ascending order
  return Object.entries(settlementResult.settlement).sort(
    ([, a], [, b]) => a.platformDisplaySequence - b.platformDisplaySequence
  );
};
