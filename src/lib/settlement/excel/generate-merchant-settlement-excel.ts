import path from "node:path";

import { platformCodes } from "@constants/settlement";
import { type Platform } from "@constants/transactions/platform";
import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import { toDateOnly } from "@utils/date-only";
import { format } from "date-fns";

import { generatePlatformExcelFile } from "./helpers/merchant/generate-platform-excel-file";
import {
  getTransactions,
  transactionStatus,
} from "../../../services/blusky/transactions";
import {
  type SummaryInformation,
  type ExcelConfig,
  type SettlementExcelData,
} from "../approve/types";
import { getKycDescriptionsMap } from "../functions/workers/calculate-kyc";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";
import { type PlatformCode } from "../repository/types";

import type { PrismaClient } from "@prisma/client";

export const generateSettlementExcelFiles = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement
): Promise<void> => {
  if (!settlement.fromDate || !settlement.toDate) {
    throw new Error("Settlement fromDate and toDate are required");
  }

  const settlements = await getAllSettlementsByCustomerAndPeriod(
    settlement.customerId,
    settlement.fromDate,
    settlement.toDate,
    prisma
  );

  if (!settlement.customerCustomerType.statementFolderLocation) {
    throw new Error("Statement folder location not configured for customer");
  }

  const baseFolderLocation =
    settlement.customerCustomerType.statementFolderLocation;

  const { customerTradingName } = settlement.customer;
  const formattedFromDate = format(settlement.fromDate, "yyyy-MM-dd");
  const formattedToDate = format(settlement.toDate, "yyyy-MM-dd");

  const [transactions, kycDescriptions] = await Promise.all([
    getTransactions(
      settlement.customer.serviceNumber,
      {
        fromDate: toDateOnly(settlement.fromDate),
        toDate: toDateOnly(settlement.toDate),
      },
      {
        aggregate: {
          [transactionStatus.rejected1]: {
            aggregateForPlatforms: ["ETI", "RFM"],
            targetPlatform: "RTO",
            keepInOriginal: false,
          },
        },
      }
    ),
    getKycDescriptionsMap(prisma),
  ]);

  const summaryInformation: SummaryInformation = {};

  for (const platformSettlement of settlements) {
    const entry = Object.entries(platformSettlement)[0];

    if (!entry) {
      continue;
    }

    const [platformCode, settlementData] = entry;

    if (platformCode === platformCodes.summary) {
      continue;
    }

    summaryInformation[platformCode] = {
      labelName: settlementData.labelName,
      transactionCount: settlementData.transactionCount,
      totalTransactionAmount: settlementData.totalTransactionAmount,
      refundCount: settlementData.refundCount,
      totalRefundAmount: settlementData.totalRefundAmount,
      gatewayFee: settlementData.gatewayFee,
      transactionFee: settlementData.transactionFee,
      salesFee: settlementData.salesFee,
      refundFee: settlementData.refundFee,
      totalFailedAmount: settlementData.totalFailedAmount,
      endBalance: settlementData.endBalance,
      displaySequence: settlementData.displaySequence,
      minimumFeeTotal: settlementData.minimumFeeTotal,
      adjustments: settlementData.adjustments ?? [],
      kycDetails: settlementData.kycDetails,
    };
  }

  for (const settlementItem of settlements) {
    const platformCode = Object.keys(settlementItem)[0];

    if (!platformCode) {
      continue;
    }

    const platformData = settlementItem[platformCode as PlatformCode];
    const excelConfig: ExcelConfig = {
      platformCode,
      customerTradingName,
      period: {
        fromDate: settlement.fromDate,
        toDate: settlement.toDate,
      },
      fileName: `${customerTradingName}-${formattedFromDate}-${formattedToDate}-${platformCode}`,
      folderPath: path.join(baseFolderLocation, platformCode),
      data: platformData as SettlementExcelData,
      customer: {
        entityName: settlement.customer.entity.entityName,
        entityLogoName: settlement.customer.entity.logoName,
      },
      kycDescriptions,
    };
    // eslint-disable-next-line no-await-in-loop
    await generatePlatformExcelFile(
      transactions[platformCode as Platform] ?? [],
      excelConfig,
      summaryInformation
    );
  }
};
