import path from "node:path";

import { platformCodes } from "@constants/settlement";
import { type Platform } from "@constants/transactions/platform";
import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import { toDateOnly } from "@utils/date-only";
import { format } from "date-fns";

import { generatePlatformExcelFile } from "./helpers/merchant/generate-platform-excel-file";
import { getAuthToken } from "../../../services/blusky/authentication";
import {
  getKycTransactions,
  getTransactions,
  transactionStatus,
} from "../../../services/blusky/transactions";
import {
  type SummaryInformation,
  type ExcelConfig,
  type SettlementExcelData,
} from "../approve/types";
import { getKycDescriptionsMap } from "../repository/get-kyc-descriptions";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";
import { type PlatformCode, type SettlementDetails } from "../repository/types";

import type { PrismaClient } from "@prisma/client";

export const generateSettlementExcelFiles = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement
): Promise<void> => {
  if (!settlement.fromDate || !settlement.toDate) {
    throw new Error("Settlement fromDate and toDate are required");
  }

  const { regenerationCount, customerId, fromDate, toDate } = settlement;

  const token = await getAuthToken();

  const settlements = await getAllSettlementsByCustomerAndPeriod(
    customerId,
    fromDate,
    toDate,
    prisma
  );

  if (!settlement.customerCustomerType.statementFolderLocation) {
    throw new Error("Statement folder location not configured for customer");
  }

  const baseFolderLocation =
    settlement.customerCustomerType.statementFolderLocation;

  const { customerTradingName } = settlement.customer;
  const formattedFromDate = format(settlement.fromDate, "yyyy-MM-dd");
  const formattedToDate = format(settlement.toDate, "yyyy-MM-dd");

  const revision = regenerationCount > 0 ? ` Rev${regenerationCount}` : "";

  const [transactions, kycDescriptions, kycTransactions] = await Promise.all([
    getTransactions(
      settlement.customer.serviceNumber,
      {
        fromDate: toDateOnly(settlement.fromDate),
        toDate: toDateOnly(settlement.toDate),
      },
      {
        aggregate: {
          [transactionStatus.rejected1]: {
            aggregateForPlatforms: ["ETI", "RFM"],
            targetPlatform: "RTO",
            keepInOriginal: false,
          },
        },
      }
    ),
    getKycDescriptionsMap(prisma),
    getKycTransactions(
      settlement.customer.serviceNumber,
      toDateOnly(settlement.fromDate),
      toDateOnly(settlement.toDate),
      token
    ),
  ]);

  const summaryInformation: SummaryInformation = {};
  let summaryPlatformData: SettlementDetails | undefined;

  // Helper function to create Excel configuration
  const createExcelConfig = (
    platformCode: string,
    platformData: SettlementDetails
  ): ExcelConfig => ({
    platformCode,
    customerTradingName,
    period: {
      fromDate: settlement.fromDate,
      toDate: settlement.toDate,
    },
    fileName: `${customerTradingName}-${formattedFromDate}-${formattedToDate}-${platformCode}${revision}.xlsx`,
    folderPath: path.join(
      baseFolderLocation,
      platformCode === platformCodes.summary ? "Summary" : platformCode
    ),
    data: platformData as SettlementExcelData,
    customer: {
      entityName: settlement.customer.entity.entityName,
      entityLogoName: settlement.customer.entity.logoName,
    },
    kycDescriptions,
  });

  for (const settlementItem of settlements) {
    const platformCode = Object.keys(settlementItem)[0];

    if (!platformCode) {
      continue;
    }

    const platformData = settlementItem[platformCode as PlatformCode];

    if (!platformData) {
      continue;
    }

    if (platformCode === platformCodes.summary) {
      summaryPlatformData = platformData;
      continue;
    }

    summaryInformation[platformCode] = {
      labelName: platformData.labelName,
      transactionCount: platformData.transactionCount,
      totalTransactionAmount: platformData.totalTransactionAmount,
      refundCount: platformData.refundCount,
      totalRefundAmount: platformData.totalRefundAmount,
      gatewayFee: platformData.gatewayFee,
      transactionFee: platformData.transactionFee,
      salesFee: platformData.salesFee,
      refundFee: platformData.refundFee,
      totalFailedAmount: platformData.totalFailedAmount,
      endBalance: platformData.endBalance,
      displaySequence: platformData.displaySequence,
      minimumFeeTotal: platformData.minimumFeeTotal,
      adjustments: platformData.adjustments ?? [],
      kycDetails: platformData.kycDetails,
    };

    const excelConfig = createExcelConfig(platformCode, platformData);

    // eslint-disable-next-line no-await-in-loop
    await generatePlatformExcelFile(
      transactions[platformCode as Platform] ?? [],
      kycTransactions,
      excelConfig
    );
  }

  if (summaryPlatformData) {
    const summaryExcelConfig = createExcelConfig(
      platformCodes.summary,
      summaryPlatformData
    );

    await generatePlatformExcelFile(
      transactions[platformCodes.summary as Platform] ?? [],
      kycTransactions,
      summaryExcelConfig,
      summaryInformation
    );
  }
};
