/* eslint-disable @typescript-eslint/naming-convention */
import { existsSync, mkdirSync } from "node:fs";

import { format } from "date-fns";
import ExcelJS from "exceljs";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { generateSettlementExcelFiles } from "./generate-merchant-settlement-excel";
import { createPayInSummarySheet } from "./helpers/merchant/create-payin-summary-settlement-sheet";
import { createTransactionsSheets } from "./helpers/merchant/create-transactions-settlement-sheet";
import { getTransactions } from "../../../services/blusky/transactions";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";

import type { ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import type { PrismaClient } from "@prisma/client";

vi.mock("node:fs", () => ({
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
}));

vi.mock("@constants/filesystem", () => ({
  baseDirectoryName: "/test/base",
}));

vi.mock("node:path", () => ({
  default: {
    join: vi.fn().mockImplementation((...arguments_) => arguments_.join("/")),
  },
}));

vi.mock("exceljs");

vi.mock("./helpers/merchant/create-payin-summary-settlement-sheet", () => ({
  createPayInSummarySheet: vi.fn(),
}));

vi.mock("./helpers/merchant/create-transactions-settlement-sheet", () => ({
  createTransactionsSheets: vi.fn(),
}));

vi.mock("../../../services/blusky/transactions", () => ({
  getTransactions: vi.fn(),
  getKycTransactions: vi.fn(),
  transactionStatus: {
    success: "STATUS_SUCCESS",
    failed: "STATUS_FAILED",
    refund: "STATUS_REFUND",
    fRefund: "STATUS_F_REFUNDED",
    refunded: "STATUS_REFUNDED",
    rejected1: "STATUS_REJECTED1",
  },
}));

vi.mock("../../../services/blusky/authentication", () => ({
  getAuthToken: vi.fn(),
}));

vi.mock("../repository/settlement-frequency", () => ({
  getAllSettlementsByCustomerAndPeriod: vi.fn(),
}));

vi.mock("@lib/settlement/repository/get-kyc-descriptions", () => ({
  getKycDescriptionsMap: vi.fn().mockResolvedValue({}),
}));

vi.mock("@utils/date-only", () => ({
  toDateOnly: vi.fn().mockImplementation((date: Date) => ({
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
  })),
}));

vi.mock("date-fns", () => ({
  format: vi
    .fn()
    .mockReturnValueOnce("2024-01-15")
    .mockReturnValueOnce("2024-01-31"),
}));

describe("generateSettlementExcelFiles", () => {
  let mockPrisma: PrismaClient;
  let mockSettlement: ApprovalCustomerSettlement;

  beforeEach(() => {
    const mockWorkbook = {
      addWorksheet: vi.fn(),
      addImage: vi.fn(),
      xlsx: {
        writeFile: vi.fn(),
      },
    };

    vi.mocked(ExcelJS.Workbook).mockImplementation(
      () => mockWorkbook as unknown as ExcelJS.Workbook
    );

    mockPrisma = {} as unknown as PrismaClient;

    vi.mocked(format)
      .mockReturnValueOnce("2024-01-01")
      .mockReturnValueOnce("2024-01-31");

    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue([
      {
        ETI: {
          labelName: "Interac e-Transfer Pay-In (ETI)",
          transactionCount: 100,
          totalTransactionAmount: 10_000.5,
          refundCount: 5,
          totalRefundAmount: 500.25,
          minimumFeeCount: 2,
          minimumFeeTotal: 10,
          gatewayFee: 50,
          transactionFee: 30,
          salesFee: 20,
          refundFee: 2.5,
          totalFailedAmount: 0,
          endBalance: 800,
          total2FaRejectAmount: 0,
          total2FaRejectCount: 0,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          totalMinimumAmount: 0,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: false,
          adjustments: [],
          displaySequence: 1,
        },
        RFM: {
          labelName: "Real-Time Funds Movement (RFM)",
          transactionCount: 75,
          totalTransactionAmount: 7500,
          refundCount: 3,
          totalRefundAmount: 300,
          minimumFeeCount: 1,
          minimumFeeTotal: 5,
          gatewayFee: 40,
          transactionFee: 25,
          salesFee: 15,
          refundFee: 2,
          totalFailedAmount: 0,
          endBalance: 600,
          total2FaRejectAmount: 0,
          total2FaRejectCount: 0,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          totalMinimumAmount: 0,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: false,
          adjustments: [],
          displaySequence: 2,
        },
        IDP: {
          labelName: "Interac Direct Payment (IDP)",
          transactionCount: 50,
          totalTransactionAmount: 5000,
          refundCount: 2,
          totalRefundAmount: 200,
          minimumFeeCount: 0,
          minimumFeeTotal: 0,
          gatewayFee: 30,
          transactionFee: 20,
          salesFee: 10,
          refundFee: 1.5,
          totalFailedAmount: 0,
          endBalance: 400,
          total2FaRejectAmount: 0,
          total2FaRejectCount: 0,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          totalMinimumAmount: 0,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: false,
          adjustments: [],
          displaySequence: 3,
        },
      },
    ]);

    vi.mocked(getTransactions).mockResolvedValue({
      ETI: [
        {
          createdDate: "2024-01-15T10:30:00Z",
          updatedDate: "2024-01-15T11:45:00Z",
          serviceNumber: "**********",
          originalAmt: 100.5,
          refundAmt: 0,
          finalAmt: 100.5,
          currency: "CAD",
          country: "CA",
          billable: true,
          platform: "ETI",
          custNumber: "CUST123",
          rcode: 200,
          integratorName: "Test Integrator",
          programName: "Test Program",
          billingName: "Test Billing",
          transactionID: "TXN123456",
          receiptID: "RCP789012",
          interacRef: "INT345678",
          fIName: "Test Bank",
          status: "STATUS_SUCCESS",
        },
      ],
    });

    mockSettlement = {
      customerSettlementsId: 1,
      customerId: 123,
      fromDate: new Date("2024-01-01"),
      toDate: new Date("2024-01-31"),
      endBalance: 1000.5,
      regenerationCount: 0,
      platform: {
        platformId: 1,
        platformCode: "ETI",
      },
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading Name",
        entity: {
          entityName: "Gigadat",
          logoName: "logo-gigadat.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 5000,
          },
          beneficiary: {
            reference: "TEST-REF",
            bankAccount: {
              bankPaymentRailId: 789,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
        customerSettlementType: {},
      },
      customerCustomerType: {
        statementFolderLocation: "/test/statements",
        statementFrequency: {
          statementFrequencyId: 1,
          statementFrequencyCode: "MONTHLY",
        },
        customerType: {
          customerTypeName: "Merchant",
        },
      },
    };

    vi.mocked(existsSync).mockReturnValue(false);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should generate Excel files for platform settlement", async () => {
    await generateSettlementExcelFiles(mockPrisma, mockSettlement);

    expect(getAllSettlementsByCustomerAndPeriod).toHaveBeenCalledWith(
      123,
      new Date("2024-01-01"),
      new Date("2024-01-31"),
      mockPrisma
    );

    expect(mkdirSync).toHaveBeenCalledWith(expect.stringContaining("ETI"), {
      recursive: true,
    });

    expect(createPayInSummarySheet).toHaveBeenCalledTimes(1);

    expect(createTransactionsSheets).toHaveBeenCalledTimes(1);
  });

  it("should create folder if it doesn't exist", async () => {
    vi.mocked(existsSync).mockReturnValue(false);

    await generateSettlementExcelFiles(mockPrisma, mockSettlement);

    expect(existsSync).toHaveBeenCalled();
    expect(mkdirSync).toHaveBeenCalled();
  });

  it("should not create folder if it already exists", async () => {
    vi.mocked(existsSync).mockReturnValue(true);

    await generateSettlementExcelFiles(mockPrisma, mockSettlement);

    expect(existsSync).toHaveBeenCalled();
    expect(createPayInSummarySheet).toHaveBeenCalledTimes(1);
    expect(createTransactionsSheets).toHaveBeenCalledTimes(1);
  });

  it("should handle no platform settlements", async () => {
    vi.mocked(getAllSettlementsByCustomerAndPeriod).mockResolvedValue([]);

    await generateSettlementExcelFiles(mockPrisma, mockSettlement);

    expect(getTransactions).toHaveBeenCalled();
    expect(createPayInSummarySheet).not.toHaveBeenCalled();
    expect(createTransactionsSheets).not.toHaveBeenCalled();
  });

  it("should pass correct transactions to createTransactionsSheets", async () => {
    await generateSettlementExcelFiles(mockPrisma, mockSettlement);

    expect(createTransactionsSheets).toHaveBeenCalledWith(
      expect.any(Object), // Workbook
      expect.objectContaining({
        platformCode: "ETI",
      }),
      expect.arrayContaining([
        expect.objectContaining({
          platform: "ETI",
          status: "STATUS_SUCCESS",
        }),
      ])
    );
  });
});
