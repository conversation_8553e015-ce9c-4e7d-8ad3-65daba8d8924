/* eslint-disable @typescript-eslint/naming-convention */
import { addAdjustmentsToNonMerchantExcel } from "./add-adjustments-to-excel";
import { writeExcelFile } from "./helpers/write-excel-file";

vi.mock("exceljs", () => {
  return {
    default: {
      Workbook: vi.fn(() => mockWorkbook),
    },
  };
});
vi.mock("./helpers/write-excel-file", () => ({
  writeExcelFile: vi.fn(),
}));

const lastRowNumber = 23;

const mockWorksheet = {
  getColumn: vi.fn(() => ({ font: {} })),
  insertRow: vi.fn(),
  lastRow: {
    number: lastRowNumber,
  },
  getCell: vi.fn(),
};

const mockWorkbook = {
  xlsx: {
    readFile: vi.fn(),
  },
  getWorksheet: vi.fn(() => mockWorksheet),
};

describe("addAdjustmentsToNonMerchantExcel", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("throws if SUMMARY worksheet is not found", async () => {
    // @ts-expect-error - Mocking the return value to simulate no worksheet found
    // eslint-disable-next-line unicorn/no-useless-undefined
    mockWorkbook.getWorksheet.mockReturnValue(undefined);

    await expect(
      addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        {}
      )
    ).rejects.toThrow("SUMMARY worksheet not found in Excel file.");
  });

  it("throws if no rows found in SUMMARY worksheet", async () => {
    // @ts-expect-error - Mocking lastRow to simulate no rows
    mockWorksheet.lastRow = undefined;

    await expect(
      addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        {}
      )
    ).rejects.toThrow("No rows found in SUMMARY worksheet.");
  });

  describe("Commission Rate Settlement Type", () => {
    const mockTotalCell = {
      value: {
        formula: `SUM(E12:E${lastRowNumber})`,
      },
    };

    beforeEach(() => {
      mockWorksheet.lastRow = {
        number: lastRowNumber,
      };

      mockWorksheet.getCell.mockReturnValue(mockTotalCell);

      mockTotalCell.value.formula = `SUM(E12:E${lastRowNumber})`;
    });

    it("adds adjustments correctly for commission rate", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Summary Adjustment",
            amount: 200,
            comment: "Summary comment",
            displayCommentExcel: true,
          },
        ],
        RTO: [
          {
            id: 1,
            label: "Adjustment 1",
            amount: 100,
            comment: "Test comment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "Summary",
        "Summary Adjustment",
        "",
        "",
        -200,
        "Summary comment",
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RTO",
        "Adjustment 1",
        "",
        "",
        100,
        "Test comment",
      ]);
      expect(mockWorksheet.getCell).toHaveBeenCalledWith(
        `E${lastRowNumber + 2}`
      );
      expect(mockTotalCell.value.formula).toBe("SUM(E12:E23)");
      expect(writeExcelFile).toHaveBeenCalledWith(
        mockWorkbook,
        "folder",
        "file.xlsx"
      );
    });

    it("should insert positive adjustment amounts for payout platform codes", async () => {
      const adjustments = {
        RTO: [
          {
            id: 1,
            label: "RTO Adjustment",
            amount: 50,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
        RTX: [
          {
            id: 2,
            label: "RTX Adjustment",
            amount: 25,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "RTO",
        "RTO Adjustment",
        "",
        "",
        50,
        "Positive adjustment",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RTX",
        "RTX Adjustment",
        "",
        "",
        25,
        "Positive adjustment",
      ]);
    });

    it("should insert negative adjustment amounts for non-payout platform code", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Adjustment 2",
            amount: 50,
            comment: "Negative adjustment",
            displayCommentExcel: true,
          },
        ],
        ETI: [
          {
            id: 4,
            label: "ETI adjustment",
            amount: 25,
            comment: "Negative ETI adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "Summary",
        "Adjustment 2",
        "",
        "",
        -50,
        "Negative adjustment",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "ETI",
        "ETI adjustment",
        "",
        "",
        -25,
        "Negative ETI adjustment",
      ]);
    });

    it("should not display comment if displayCommentExcel is false", async () => {
      const adjustments = {
        ETI: [
          {
            id: 3,
            label: "Adjustment 3",
            amount: 75,
            comment: "Should display",
            displayCommentExcel: true,
          },
        ],
        RFM: [
          {
            id: 5,
            label: "Adjustment 5",
            amount: 75,
            comment: "Should not display",
            displayCommentExcel: false,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Commision Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "ETI",
        "Adjustment 3",
        "",
        "",
        -75,
        "Should display",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RFM",
        "Adjustment 5",
        "",
        "",
        -75,
        "",
      ]);
    });
  });

  describe("Buy Rate Settlement Type", () => {
    let mockCells: Record<string, { value: unknown; font?: unknown }>;

    beforeEach(() => {
      // Mock worksheet cells (assuming tests are only using 2 adjustments)
      mockCells = {
        // Mock label cells
        [`B${lastRowNumber - 1}`]: {
          value: {},
          font: {},
        },
        [`B${lastRowNumber}`]: {
          value: {},
          font: {},
        },
        // Mock last row cells
        [`G${lastRowNumber + 2}`]: {
          value: {},
        },
        [`H${lastRowNumber + 2}`]: {
          value: {},
        },
        [`I${lastRowNumber + 2}`]: {
          value: {},
        },
        [`J${lastRowNumber + 2}`]: {
          value: {},
        },
        [`K${lastRowNumber + 2}`]: {
          value: {},
        },
        [`L${lastRowNumber + 2}`]: {
          value: {},
        },
        [`M${lastRowNumber + 2}`]: {
          value: {},
        },
        [`N${lastRowNumber + 2}`]: {
          value: {},
        },
        [`O${lastRowNumber + 2}`]: {
          value: {},
        },
        [`P${lastRowNumber + 2}`]: {
          value: {},
        },
        [`D${lastRowNumber + 2}`]: {
          value: {},
        },
        [`Q${lastRowNumber + 2}`]: {
          value: {},
        },
      };

      mockWorksheet.lastRow.number = lastRowNumber;

      mockWorksheet.getCell.mockImplementation((cell: string) => {
        return mockCells[cell];
      });
    });

    it("adds adjustments correctly for buy rate", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Summary Adjustment",
            amount: 200,
            comment: "Summary comment",
            displayCommentExcel: true,
          },
        ],
        RTO: [
          {
            id: 1,
            label: "Adjustment 1",
            amount: 100,
            comment: "Test comment",
            displayCommentExcel: true,
          },
        ],
      };

      // Mock cells for the adjustment amounts
      mockCells[`Q${lastRowNumber - 1}`] = {
        value: {},
      };
      mockCells[`L${lastRowNumber}`] = {
        value: {},
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Buy Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "",
        "Summary Adjustment",
        "Summary comment",
        -200,
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "",
        "Adjustment 1",
        "Test comment",
        100,
      ]);
      expect(mockCells[`Q${lastRowNumber - 1}`]!.value).toBe(-200);
      expect(mockCells[`L${lastRowNumber}`]!.value).toBe(100);
      expect(mockCells[`D${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(D13:D${lastRowNumber})`,
      });
      expect(mockCells[`Q${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(Q13:Q${lastRowNumber})`,
      });
      expect(mockCells[`G${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(G13:G${lastRowNumber})`,
      });
      expect(mockCells[`H${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(H13:H${lastRowNumber})`,
      });
      expect(mockCells[`I${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(I13:I${lastRowNumber})`,
      });
      expect(mockCells[`J${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(J13:J${lastRowNumber})`,
      });
      expect(mockCells[`K${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(K13:K${lastRowNumber})`,
      });
      expect(mockCells[`L${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(L13:L${lastRowNumber})`,
      });
      expect(mockCells[`M${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(M13:M${lastRowNumber})`,
      });
      expect(mockCells[`N${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(N13:N${lastRowNumber})`,
      });
      expect(mockCells[`O${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(O13:O${lastRowNumber})`,
      });
      expect(mockCells[`P${lastRowNumber + 2}`]!.value).toEqual({
        formula: `SUM(P13:P${lastRowNumber})`,
      });
      expect(writeExcelFile).toHaveBeenCalledWith(
        mockWorkbook,
        "folder",
        "file.xlsx"
      );
    });

    it("should insert positive adjustment amounts for payout platform codes", async () => {
      const adjustments = {
        RTO: [
          {
            id: 1,
            label: "RTO Adjustment",
            amount: 50,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
        RTX: [
          {
            id: 2,
            label: "RTX Adjustment",
            amount: 25,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      // Mock cells for the adjustment amounts
      mockCells[`L${lastRowNumber - 1}`] = {
        value: {},
      };
      mockCells[`M${lastRowNumber}`] = {
        value: {},
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Buy Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "",
        "RTO Adjustment",
        "Positive adjustment",
        50,
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "",
        "RTX Adjustment",
        "Positive adjustment",
        25,
      ]);
      expect(mockCells[`L${lastRowNumber - 1}`]!.value).toBe(50);
      expect(mockCells[`M${lastRowNumber}`]!.value).toBe(25);
    });

    it("should insert negative adjustment amounts for non-payout platform code", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Adjustment 2",
            amount: 50,
            comment: "Negative adjustment",
            displayCommentExcel: true,
          },
        ],
        ETI: [
          {
            id: 4,
            label: "ETI adjustment",
            amount: 25,
            comment: "Negative ETI adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      // Mock cells for the adjustment amounts
      mockCells[`Q${lastRowNumber - 1}`] = {
        value: {},
      };
      mockCells[`H${lastRowNumber}`] = {
        value: {},
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Buy Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "",
        "Adjustment 2",
        "Negative adjustment",
        -50,
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "",
        "ETI adjustment",
        "Negative ETI adjustment",
        -25,
      ]);
      expect(mockCells[`Q${lastRowNumber - 1}`]!.value).toBe(-50);
      expect(mockCells[`H${lastRowNumber}`]!.value).toBe(-25);
    });

    it("should not display comment if displayCommentExcel is false", async () => {
      const adjustments = {
        ETI: [
          {
            id: 3,
            label: "Adjustment 3",
            amount: 75,
            comment: "Should display",
            displayCommentExcel: true,
          },
        ],
        RFM: [
          {
            id: 5,
            label: "Adjustment 5",
            amount: 75,
            comment: "Should not display",
            displayCommentExcel: false,
          },
        ],
      };

      // Mock cells for the adjustment amounts
      mockCells[`H${lastRowNumber - 1}`] = {
        value: {},
      };
      mockCells[`K${lastRowNumber}`] = {
        value: {},
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Buy Rate",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "",
        "Adjustment 3",
        "Should display",
        -75,
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "",
        "Adjustment 5",
        "",
        -75,
      ]);
    });
  });

  describe("Combination Settlement Type", () => {
    const mockTotalCell = {
      value: {
        formula: `SUM(C13:E${lastRowNumber})`,
      },
    };

    beforeEach(() => {
      mockWorksheet.lastRow = {
        number: lastRowNumber,
      };

      mockWorksheet.getCell.mockReturnValue(mockTotalCell);

      mockTotalCell.value.formula = `SUM(C13:E${lastRowNumber})`;
    });

    it("adds adjustments correctly for combination", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Summary Adjustment",
            amount: 200,
            comment: "Summary comment",
            displayCommentExcel: true,
          },
        ],
        RTO: [
          {
            id: 1,
            label: "Adjustment 1",
            amount: 100,
            comment: "Test comment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Combination",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "Summary",
        "Summary Adjustment",
        -200,
        "Summary comment",
      ]);
      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RTO",
        "Adjustment 1",
        100,
        "Test comment",
      ]);
      expect(mockWorksheet.getCell).toHaveBeenCalledWith(
        `C${lastRowNumber + 2}`
      );
      expect(mockTotalCell.value.formula).toBe("SUM(C13:C23)");
      expect(writeExcelFile).toHaveBeenCalledWith(
        mockWorkbook,
        "folder",
        "file.xlsx"
      );
    });

    it("should insert positive adjustment amounts for payout platform codes", async () => {
      const adjustments = {
        RTO: [
          {
            id: 1,
            label: "RTO Adjustment",
            amount: 50,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
        RTX: [
          {
            id: 2,
            label: "RTX Adjustment",
            amount: 25,
            comment: "Positive adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Combination",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "RTO",
        "RTO Adjustment",
        50,
        "Positive adjustment",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RTX",
        "RTX Adjustment",
        25,
        "Positive adjustment",
      ]);
    });

    it("should insert negative adjustment amounts for non-payout platform code", async () => {
      const adjustments = {
        SUMMARY: [
          {
            id: 2,
            label: "Adjustment 2",
            amount: 50,
            comment: "Negative adjustment",
            displayCommentExcel: true,
          },
        ],
        ETI: [
          {
            id: 4,
            label: "ETI adjustment",
            amount: 25,
            comment: "Negative ETI adjustment",
            displayCommentExcel: true,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Combination",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "Summary",
        "Adjustment 2",
        -50,
        "Negative adjustment",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "ETI",
        "ETI adjustment",
        -25,
        "Negative ETI adjustment",
      ]);
    });

    it("should not display comment if displayCommentExcel is false", async () => {
      const adjustments = {
        ETI: [
          {
            id: 3,
            label: "Adjustment 3",
            amount: 75,
            comment: "Should display",
            displayCommentExcel: true,
          },
        ],
        RFM: [
          {
            id: 5,
            label: "Adjustment 5",
            amount: 75,
            comment: "Should not display",
            displayCommentExcel: false,
          },
        ],
      };

      await addAdjustmentsToNonMerchantExcel(
        "folder",
        "file.xlsx",
        "Combination",
        adjustments
      );

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber - 1, [
        "ETI",
        "Adjustment 3",
        -75,
        "Should display",
      ]);

      expect(mockWorksheet.insertRow).toHaveBeenCalledWith(lastRowNumber, [
        "RFM",
        "Adjustment 5",
        -75,
        "",
      ]);
    });
  });
});
