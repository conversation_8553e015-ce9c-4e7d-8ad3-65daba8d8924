import { type Prisma } from "@prisma/client";

import { descending } from "../constants/default-parameters";
import { type LatestSettlements } from "../read/types";

export const getSettlementLatestEndBalanceAmount = async (
  prisma: Prisma.TransactionClient,
  settlementInfo: LatestSettlements
): Promise<string> => {
  const latestEndBalances = await prisma.customerSettlements.findFirst({
    where: {
      customerCustomerTypeId: settlementInfo.customerCustomerTypeId,
      customerId: settlementInfo.customerId,
      toDate: { lt: settlementInfo.toDate },
      platform: {
        platformName: "Summary",
      },
      deletedAt: null,
    },
    select: {
      endBalance: true,
    },
    orderBy: {
      toDate: descending,
    },
  });

  if (latestEndBalances === null) {
    return "0";
  }

  return latestEndBalances?.endBalance?.toString() ?? "0";
};
