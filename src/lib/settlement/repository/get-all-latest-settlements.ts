import { type PrismaClient } from "@prisma/client";

// Get all the latest settlements and their type.
export const getAllLatestSettlements = async (
  prisma: PrismaClient
) => prisma.$queryRaw`
WITH MaxUpdated AS (
SELECT
    customerCustomerTypeId,
    fromDate,
    toDate,
    MAX(updatedAt) AS maxUpdatedAt
FROM
    customerSettlementGenerationSchedule
GROUP BY
    customerCustomerTypeId,
    fromDate,
    toDate
),
Filtered AS (
SELECT
    sgs.*
FROM
    customerSettlementGenerationSchedule sgs
INNER JOIN MaxUpdated mu
    ON
    sgs.customerCustomerTypeId = mu.customerCustomerTypeId
    AND sgs.fromDate = mu.fromDate
    AND sgs.toDate = mu.toDate
    AND sgs.updatedAt = mu.maxUpdatedAt
),
FinalRanked AS (
SELECT
    *,
    ROW_NUMBER() OVER (
      PARTITION BY customerCustomerTypeId,
    fromDate,
    toDate
ORDER BY
    customerSettlementGenerationScheduleId DESC
    ) AS rn
FROM
    Filtered
)
SELECT
    r.customerSettlementGenerationScheduleId ,
    r.serviceNumber ,
    r.customerCustomerTypeId ,
    r.fromDate ,
    r.toDate ,
    r.updatedAt,
    r.generationType,
    r.generationstatus,
    cct.customerId,
    cct.customerTypeId,
    cct.statementFrequencyId,
    ct.customerTypeName,
    c.customerName,
    ss.stateName 
FROM
    FinalRanked r
LEFT JOIN customerCustomerType cct
  ON
    r.customerCustomerTypeId = cct.customerCustomerTypeId
LEFT JOIN customerType ct
  ON
    ct.customerTypeId = cct.customerTypeId
LEFT JOIN customer c
  ON
    c.customerId = cct.customerId
LEFT JOIN settlementState ss
  ON r.generationStatus = ss.generationStatus
  AND r.generationType = ss.generationType
WHERE r.rn = 1
ORDER BY r.updatedAt DESC;
`;
