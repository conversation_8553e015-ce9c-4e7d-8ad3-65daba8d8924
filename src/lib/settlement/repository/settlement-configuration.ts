import {
  fetchCombinedMerchantServiceNumbers,
  getAssociatedMerchantIds,
} from "@lib/customer/repository/get-customer";
import { type PrismaClient } from "@prisma/client";

import { flattenMerchantConfiguration } from "./helpers/flatten-merchant-configuration";
import { selectClause } from "./helpers/select-clause";
import {
  type MerchantSettlementConfiguration,
  type NonMerchantSettlementConfiguration,
  type NonMerchantType,
  nonMerchantTypes,
} from "./types";
import { type DateOnly, fromDateOnly } from "../../../utils/date-only";

const getMerchantsSettlementConfiguration = async (
  merchantIds: number[],
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<MerchantSettlementConfiguration[]> => {
  if (merchantIds?.length === 0) {
    return [];
  }

  const customersWithSettlementConfigs =
    await prisma.customerCustomerType.findMany({
      where: {
        customerId: { in: merchantIds },
        deletedAt: null,
        customerType: {
          customerTypeName: "Merchant",
        },
        customer: {
          enabled: true,
          deletedAt: null,
        },
      },
      ...selectClause(
        fromDateOnly(effectiveDate.fromDate),
        fromDateOnly(effectiveDate.toDate)
      ),
    });

  const flattenedConfigs = customersWithSettlementConfigs.map((config) =>
    flattenMerchantConfiguration(config)
  );

  return flattenedConfigs;
};

const getNonMerchantsSettlementConfiguration = async (
  nonMerchantId: number,
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<NonMerchantSettlementConfiguration> => {
  const customersWithSettlementConfigs =
    await prisma.customerCustomerType.findFirst({
      where: {
        customerId: nonMerchantId,
        deletedAt: null,
        customerType: {
          customerTypeName: { not: "Merchant" },
        },
        customer: {
          enabled: true,
          deletedAt: null,
        },
      },
      ...selectClause(
        fromDateOnly(effectiveDate.fromDate),
        fromDateOnly(effectiveDate.toDate)
      ),
    });

  if (!customersWithSettlementConfigs) {
    throw new Error(
      `No settlement configuration found for customer ${nonMerchantId}.`
    );
  }

  // Flatten the configuration into our internal type.
  const config = flattenMerchantConfiguration(customersWithSettlementConfigs);
  const nonMerchantTypeName = config.customerType.customerTypeName;

  if (!nonMerchantTypes.includes(nonMerchantTypeName as NonMerchantType)) {
    throw new Error(
      `Invalid non-merchant type: ${nonMerchantTypeName}. Expected one of ${nonMerchantTypes.join(", ")}.`
    );
  }

  // Retrieve the associated merchant IDs for this non-merchant.
  const associatedMerchantIds = await getAssociatedMerchantIds(
    nonMerchantId,
    nonMerchantTypeName as NonMerchantType,
    effectiveDate,
    prisma
  );

  let combinedMerchantServiceNumbers: string[] = [];

  if (config.isCombineMultipleServices) {
    const nonMerchantServiceNumbers = (
      customersWithSettlementConfigs.customerCustomerTypeServiceNumber ?? []
    ).map((c) => c.serviceNumber);

    combinedMerchantServiceNumbers = await fetchCombinedMerchantServiceNumbers(
      nonMerchantServiceNumbers as string[],
      effectiveDate,
      prisma
    );
  }

  return {
    customerCustomerTypeId: config.customerCustomerTypeId,
    customerId: config.customerId,
    customerName: config.customerName,
    serviceNumber: config.serviceNumber,
    settlementType: config?.settlementType ?? "",
    isCombineMultipleServices: config.isCombineMultipleServices,
    combinedMerchantServiceNumbers,
    nonMerchantType: config.customerType,
    associatedMerchantIds,
  };
};

export {
  getMerchantsSettlementConfiguration,
  getNonMerchantsSettlementConfiguration,
};
