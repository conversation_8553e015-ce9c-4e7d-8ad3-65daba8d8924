import { Prisma, type PrismaClient } from "@prisma/client";

import { type AdjustedCustomerSettlement } from "./types";

export const getAdjustedCustomerSettlements = async (
  prisma: PrismaClient
): Promise<AdjustedCustomerSettlement[]> => {
  const adjustments = await prisma.customerSettlementAdjustments.findMany({
    where: {
      amount: {
        not: new Prisma.Decimal(0),
      },
      deletedAt: null,
    },
    select: {
      customerSettlements: {
        select: {
          customerCustomerTypeId: true,
          customerId: true,
          fromDate: true,
          toDate: true,
        },
      },
    },
  });

  return adjustments.map((adjustment) => ({
    customerCustomerTypeId:
      adjustment.customerSettlements!.customerCustomerTypeId === null
        ? undefined
        : adjustment.customerSettlements!.customerCustomerTypeId,
    customerId: adjustment.customerSettlements!.customerId,
    fromDate: adjustment.customerSettlements!.fromDate,
    toDate: adjustment.customerSettlements!.toDate,
  }));
};
