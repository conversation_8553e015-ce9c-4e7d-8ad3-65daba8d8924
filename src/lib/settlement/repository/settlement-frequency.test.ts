/* eslint-disable @typescript-eslint/naming-convention */

import { getCustomerIdByCustomerCustomerType } from "@lib/customer-customer-type/repository";
import { PrismaClient } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import { fromDateOnly } from "@utils/date-only";

import {
  createFrequencySettlementSchedule,
  getAllSettlementsByCustomerAndPeriod,
  getFrequencyPlatformSettlement,
  getFrequencySettlementSchedule,
  getSettlementById,
  saveFrequencySettlements,
} from "./settlement-frequency";
import { type FrequencySettlementsResult } from "./types";
import { type MerchantFeesBreakdown } from "../functions/workers/types";

vi.mock("@lib/customer-customer-type/repository", () => ({
  getCustomerIdByCustomerCustomerType: vi.fn(() => ({
    customerId,
    customerTypeId,
  })),
}));

vi.mock("@prisma/client", () => {
  const mockPrismaClient = {
    $transaction: vi.fn(),
    customerSettlementGenerationSchedule: {
      update: vi.fn(),
      create: vi.fn(),
      findFirst: vi.fn(),
    },
    customerSettlements: {
      upsert: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
    },
  };

  return { PrismaClient: vi.fn(() => mockPrismaClient) };
});

vi.mock("@constants/settlement", () => ({
  kycClientPlatformId: 7,
  merchantTypeId: 3,
  statusInit: "INIT",
}));

const prisma = new PrismaClient();

const customerId = 11;
const customerTypeId = 3;
const scheduleId = 42;
const customerCustomerTypeId = 12;
const etiPlatformId = 2;
const rfmPlatformId = 3;

const fromDate = { year: 2025, month: 1, day: 16 };
const toDate = { year: 2025, month: 1, day: 31 };

const etiFees = {
  gatewayFeeTotal: 0,
  transactionFeeTotal: 3071.75,
  rejected1FeeTotal: 0,
  salesFeeTotal: 10_845.267,
  refundFeeTotal: 0,
  minimumFeeTotal: 0,
  partialReturnFeeTotal: 0,
};

const etiTransactionSummary = {
  transactionCount: 5585,
  totalTransactionAmount: 409_255.34,
  refundCount: 0,
  totalRefundAmount: 0,
  totalFailedAmount: 0,
  failedCount: 0,
  total_RAmount: 0,
  _RCount: 0,
  rejected1Count: 0,
  totalRejected1Amount: 0,
  minimumAmountCount: 0,
  totalMinimumAmount: 0,
  partialReturnCount: 0,
  totalPartialReturnAmount: 0,
};

const rfmFees = {
  gatewayFeeTotal: 0,
  transactionFeeTotal: 2156.55,
  rejected1FeeTotal: 0,
  salesFeeTotal: 7580.34,
  refundFeeTotal: 0,
  minimumFeeTotal: 0,
  partialReturnFeeTotal: 0,
};

const rfmTransactionSummary = {
  transactionCount: 3921,
  totalTransactionAmount: 286_050.57,
  refundCount: 0,
  totalRefundAmount: 0,
  totalFailedAmount: 0,
  failedCount: 0,
  total_RAmount: 0,
  _RCount: 0,
  rejected1Count: 0,
  totalRejected1Amount: 0,
  minimumAmountCount: 0,
  totalMinimumAmount: 0,
  partialReturnCount: 0,
  totalPartialReturnAmount: 0,
};

const mockSettlementsResult: FrequencySettlementsResult = {
  [customerCustomerTypeId]: {
    status: "SUCCESS" as const,
    generationType: "INITIAL",
    generationStatus: "COMPLETE",
    scheduleId,
    data: {
      result: {
        settlement: {
          ETI: {
            platformId: etiPlatformId,
            platformDescription: "Interac e-Transfer Pay-In (ETI)",
            platformDisplaySequence: 1,
            totalChargedFees: {
              ...etiFees,
            },
            totalTransactionSummary: {
              ...etiTransactionSummary,
            },
            isContractChange: false,
            feesBreakdown: [
              {
                meta: {
                  transactionSummary: {
                    ...etiTransactionSummary,
                  },
                  rates: {
                    transactionFee: 0.55,
                    reject1Fee: 0.55,
                    salesFee: 0.0265,
                    gatewayFee: 0,
                    refundFee: 6.95,
                    minimumThreshold: 0,
                    minimumCharge: 0,
                    isMinimumFeeApplicable: false,
                    transactionTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    rejectOneTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    delayMonths: {
                      isDelayApplicable: false,
                      delayInMonths: 0,
                      tierItem: undefined,
                    },
                  },
                  rateDeterminingProfile: {
                    fromDate: {
                      year: 2024,
                      month: 12,
                      day: 1,
                    },
                    toDate: {
                      year: 2024,
                      month: 12,
                      day: 31,
                    },
                    combineTotal: 1_423_666.55,
                    isCombineIdpTierSet: true,
                    isCombineAchTierSet: false,
                    isCombineRtoTierSet: true,
                    serviceNumbers: [],
                    isCombineMultipleServicesEnabled: false,
                  },
                },
                chargedFees: {
                  ...etiFees,
                },
                interval: {
                  fromDate: {
                    year: 2025,
                    month: 1,
                    day: 16,
                  },
                  toDate: {
                    year: 2025,
                    month: 1,
                    day: 31,
                  },
                },
              },
            ],
          },
          RFM: {
            platformId: rfmPlatformId,
            platformDescription: "RFM Platform",
            platformDisplaySequence: 2,
            totalChargedFees: {
              ...rfmFees,
            },
            totalTransactionSummary: {
              ...rfmTransactionSummary,
            },
            isContractChange: false,
            feesBreakdown: [
              {
                meta: {
                  transactionSummary: {
                    ...rfmTransactionSummary,
                  },
                  rates: {
                    transactionFee: 0.55,
                    reject1Fee: 0.55,
                    salesFee: 0.0265,
                    gatewayFee: 0,
                    refundFee: 6.95,
                    minimumThreshold: 0,
                    minimumCharge: 0,
                    isMinimumFeeApplicable: false,
                    transactionTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    rejectOneTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    delayMonths: {
                      isDelayApplicable: false,
                      delayInMonths: 0,
                      tierItem: undefined,
                    },
                  },
                  rateDeterminingProfile: {
                    fromDate: {
                      year: 2024,
                      month: 12,
                      day: 1,
                    },
                    toDate: {
                      year: 2024,
                      month: 12,
                      day: 31,
                    },
                    combineTotal: 1_423_666.55,
                    isCombineIdpTierSet: true,
                    isCombineAchTierSet: false,
                    isCombineRtoTierSet: true,
                    serviceNumbers: [],
                    isCombineMultipleServicesEnabled: false,
                  },
                },
                chargedFees: {
                  ...rfmFees,
                },
                interval: {
                  fromDate: {
                    year: 2025,
                    month: 1,
                    day: 16,
                  },
                  toDate: {
                    year: 2025,
                    month: 1,
                    day: 31,
                  },
                },
              },
            ],
          },
        },
      },
      timeTakenInSeconds: 12,
    },
  },
};

describe("saveFrequencySettlements", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should update frequency settlement schedule when status is success", async () => {
    vi.mocked(prisma.$transaction).mockImplementation(async (callback) =>
      callback(prisma)
    );

    await saveFrequencySettlements(
      mockSettlementsResult,
      fromDate,
      toDate,
      prisma
    );

    expect(
      prisma.customerSettlementGenerationSchedule.update
    ).toHaveBeenCalledWith({
      where: {
        customerSettlementGenerationScheduleId: scheduleId,
      },
      data: {
        completionDate: expect.any(Date) as Date,
        generationStatus: "COMPLETE",
        errorMessage: null,
      },
    });
  });

  it("should update frequency settlement schedule when status is error", async () => {
    vi.mocked(prisma.$transaction).mockImplementation(async (callback) =>
      callback(prisma)
    );

    const mockSettlementsWithError = {
      [customerCustomerTypeId]: {
        status: "ERROR" as const,
        generationType: "INITIAL" as const,
        generationStatus: "ERROR" as const,
        scheduleId,
        message: "An error occurred",
      },
    };

    await saveFrequencySettlements(
      mockSettlementsWithError,
      fromDate,
      toDate,
      prisma
    );

    expect(
      prisma.customerSettlementGenerationSchedule.update
    ).toHaveBeenCalledWith({
      where: {
        customerSettlementGenerationScheduleId: scheduleId,
      },
      data: {
        completionDate: expect.any(Date) as Date,
        generationStatus: "ERROR",
        errorMessage: "An error occurred",
      },
    });
  });

  it("should save frequency platform settlements", async () => {
    vi.mocked(prisma.$transaction).mockImplementation(async (callback) =>
      callback(prisma)
    );

    await saveFrequencySettlements(
      mockSettlementsResult,
      fromDate,
      toDate,
      prisma
    );

    expect(prisma.customerSettlements.upsert).toHaveBeenCalledWith({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId,
          platformId: etiPlatformId,
          fromDate: fromDateOnly(fromDate),
          toDate: fromDateOnly(toDate),
        },
      },
      update: {
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: etiFees.gatewayFeeTotal,
        transactionFee: etiFees.transactionFeeTotal + etiFees.rejected1FeeTotal,
        salesFee: etiFees.salesFeeTotal,
        refundFee: etiFees.refundFeeTotal,
        minimumFeeTotal: etiFees.minimumFeeTotal,
        transactionCount: etiTransactionSummary.transactionCount,
        totalTransactionAmount: etiTransactionSummary.totalTransactionAmount,
        refundCount: etiTransactionSummary.refundCount,
        totalRefundAmount: etiTransactionSummary.totalRefundAmount,
        totalFailedAmount: etiTransactionSummary.totalFailedAmount,
        total2FaRejectCount: etiTransactionSummary.rejected1Count,
        total2FaRejectAmount: etiTransactionSummary.totalRejected1Amount,
        txnCountETI_R1: etiTransactionSummary._RCount,
        txnAmountRTO_R: etiTransactionSummary.total_RAmount,
        minimumFeeCount: etiTransactionSummary.minimumAmountCount,
        totalMinimumAmount: etiTransactionSummary.totalMinimumAmount,
        partialReturnAmountRTO: etiTransactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO: etiTransactionSummary.partialReturnCount,
        meta: {
          feesBreakdown:
            mockSettlementsResult[customerCustomerTypeId]!.data!.result
              .settlement["ETI"]!.feesBreakdown,
        },
      },
      create: {
        customerId,
        platformId: etiPlatformId,
        fromDate: fromDateOnly(fromDate),
        toDate: fromDateOnly(toDate),
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: etiFees.gatewayFeeTotal,
        transactionFee: etiFees.transactionFeeTotal + etiFees.rejected1FeeTotal,
        salesFee: etiFees.salesFeeTotal,
        refundFee: etiFees.refundFeeTotal,
        minimumFeeTotal: etiFees.minimumFeeTotal,
        transactionCount: etiTransactionSummary.transactionCount,
        totalTransactionAmount: etiTransactionSummary.totalTransactionAmount,
        refundCount: etiTransactionSummary.refundCount,
        totalRefundAmount: etiTransactionSummary.totalRefundAmount,
        totalFailedAmount: etiTransactionSummary.totalFailedAmount,
        total2FaRejectCount: etiTransactionSummary.rejected1Count,
        total2FaRejectAmount: etiTransactionSummary.totalRejected1Amount,
        txnCountETI_R1: etiTransactionSummary._RCount,
        txnAmountRTO_R: etiTransactionSummary.total_RAmount,
        minimumFeeCount: etiTransactionSummary.minimumAmountCount,
        totalMinimumAmount: etiTransactionSummary.totalMinimumAmount,
        partialReturnAmountRTO: etiTransactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO: etiTransactionSummary.partialReturnCount,
        meta: {
          feesBreakdown:
            mockSettlementsResult[customerCustomerTypeId]!.data!.result
              .settlement["ETI"]!.feesBreakdown,
        },
      },
    });

    expect(prisma.customerSettlements.upsert).toHaveBeenCalledWith({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId,
          platformId: rfmPlatformId,
          fromDate: fromDateOnly(fromDate),
          toDate: fromDateOnly(toDate),
        },
      },
      update: {
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: rfmFees.gatewayFeeTotal,
        transactionFee: rfmFees.transactionFeeTotal + rfmFees.rejected1FeeTotal,
        salesFee: rfmFees.salesFeeTotal,
        refundFee: rfmFees.refundFeeTotal,
        minimumFeeTotal: rfmFees.minimumFeeTotal,
        transactionCount: rfmTransactionSummary.transactionCount,
        totalTransactionAmount: rfmTransactionSummary.totalTransactionAmount,
        refundCount: rfmTransactionSummary.refundCount,
        totalRefundAmount: rfmTransactionSummary.totalRefundAmount,
        totalFailedAmount: rfmTransactionSummary.totalFailedAmount,
        total2FaRejectCount: rfmTransactionSummary.rejected1Count,
        total2FaRejectAmount: rfmTransactionSummary.totalRejected1Amount,
        txnCountETI_R1: rfmTransactionSummary._RCount,
        txnAmountRTO_R: rfmTransactionSummary.total_RAmount,
        minimumFeeCount: rfmTransactionSummary.minimumAmountCount,
        totalMinimumAmount: rfmTransactionSummary.totalMinimumAmount,
        partialReturnAmountRTO: rfmTransactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO: rfmTransactionSummary.partialReturnCount,
        meta: {
          feesBreakdown:
            mockSettlementsResult[customerCustomerTypeId]!.data!.result
              .settlement["RFM"]!.feesBreakdown,
        },
      },
      create: {
        customerId,
        platformId: rfmPlatformId,
        fromDate: fromDateOnly(fromDate),
        toDate: fromDateOnly(toDate),
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: rfmFees.gatewayFeeTotal,
        transactionFee: rfmFees.transactionFeeTotal + rfmFees.rejected1FeeTotal,
        salesFee: rfmFees.salesFeeTotal,
        refundFee: rfmFees.refundFeeTotal,
        minimumFeeTotal: rfmFees.minimumFeeTotal,
        transactionCount: rfmTransactionSummary.transactionCount,
        totalTransactionAmount: rfmTransactionSummary.totalTransactionAmount,
        refundCount: rfmTransactionSummary.refundCount,
        totalRefundAmount: rfmTransactionSummary.totalRefundAmount,
        totalFailedAmount: rfmTransactionSummary.totalFailedAmount,
        total2FaRejectCount: rfmTransactionSummary.rejected1Count,
        total2FaRejectAmount: rfmTransactionSummary.totalRejected1Amount,
        txnCountETI_R1: rfmTransactionSummary._RCount,
        txnAmountRTO_R: rfmTransactionSummary.total_RAmount,
        minimumFeeCount: rfmTransactionSummary.minimumAmountCount,
        totalMinimumAmount: rfmTransactionSummary.totalMinimumAmount,
        partialReturnAmountRTO: rfmTransactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO: rfmTransactionSummary.partialReturnCount,
        meta: {
          feesBreakdown:
            mockSettlementsResult[customerCustomerTypeId]!.data!.result
              .settlement["RFM"]!.feesBreakdown,
        },
      },
    });

    expect(prisma.customerSettlements.upsert).toHaveBeenCalledWith({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId,
          platformId: 1,
          fromDate: fromDateOnly(fromDate),
          toDate: fromDateOnly(toDate),
        },
      },
      update: {
        customerCustomerTypeId,
        status: "INIT",
        endBalance: 0,
      },
      create: {
        customerId,
        platformId: 1,
        fromDate: fromDateOnly(fromDate),
        toDate: fromDateOnly(toDate),
        customerCustomerTypeId,
        status: "INIT",
        endBalance: 0,
      },
    });
  });

  it("should save frequency platform settlements with contract change", async () => {
    vi.mocked(prisma.$transaction).mockImplementation(async (callback) =>
      callback(prisma)
    );

    const fromDate = {
      year: 2025,
      month: 1,
      day: 1,
    };

    const toDate = {
      year: 2025,
      month: 1,
      day: 31,
    };

    const secondTransactionSummary = {
      transactionCount: 445,
      totalTransactionAmount: 32_608.53,
      refundCount: 0,
      totalRefundAmount: 0,
      totalFailedAmount: 0,
      failedCount: 0,
      total_RAmount: 0,
      _RCount: 0,
      rejected1Count: 0,
      totalRejected1Amount: 0,
      minimumAmountCount: 0,
      totalMinimumAmount: 0,
      partialReturnCount: 0,
      totalPartialReturnAmount: 0,
    };

    const secondFeesBreakdown = {
      meta: {
        transactionSummary: secondTransactionSummary,
        rates: {
          transactionFee: 0.65,
          reject1Fee: 0.65,
          salesFee: 0.0265,
          gatewayFee: 0,
          refundFee: 6.95,
          minimumThreshold: 0,
          minimumCharge: 0,
          isMinimumFeeApplicable: false,
          transactionTierItem: {
            id: 1372,
            maxAmount: 2_000_000,
            minAmount: 500_000,
            transactionFee: 0.65,
            salesFee: 0.0265,
          },
          rejectOneTierItem: {
            id: 1372,
            maxAmount: 2_000_000,
            minAmount: 500_000,
            transactionFee: 0.65,
            salesFee: 0.0265,
          },
          delayMonths: {
            isDelayApplicable: false,
            delayInMonths: 0,
            tierItem: undefined,
          },
        },
        rateDeterminingProfile: {
          fromDate: {
            year: 2024,
            month: 12,
            day: 1,
          },
          toDate: {
            year: 2024,
            month: 12,
            day: 31,
          },
          combineTotal: 1_423_666.55,
          isCombineIdpTierSet: true,
          isCombineAchTierSet: false,
          isCombineRtoTierSet: true,
          serviceNumbers: [],
          isCombineMultipleServicesEnabled: false,
        },
      },
      chargedFees: {
        gatewayFeeTotal: 0,
        transactionFeeTotal: 289.25,
        rejected1FeeTotal: 0,
        salesFeeTotal: 864.13,
        refundFeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      interval: {
        fromDate: {
          year: 2025,
          month: 1,
          day: 1,
        },
        toDate: {
          year: 2025,
          month: 1,
          day: 15,
        },
      },
    };

    const totalTransactionSummary = {
      transactionCount:
        etiTransactionSummary.transactionCount +
        secondTransactionSummary.transactionCount,
      totalTransactionAmount:
        etiTransactionSummary.totalTransactionAmount +
        secondTransactionSummary.totalTransactionAmount,
      refundCount:
        etiTransactionSummary.refundCount +
        secondTransactionSummary.refundCount,
      totalRefundAmount:
        etiTransactionSummary.totalRefundAmount +
        secondTransactionSummary.totalRefundAmount,
      totalFailedAmount:
        etiTransactionSummary.totalFailedAmount +
        secondTransactionSummary.totalFailedAmount,
      failedCount:
        etiTransactionSummary.failedCount +
        secondTransactionSummary.failedCount,
      total_RAmount:
        etiTransactionSummary.total_RAmount +
        secondTransactionSummary.total_RAmount,
      _RCount: etiTransactionSummary._RCount + secondTransactionSummary._RCount,
      rejected1Count:
        etiTransactionSummary.rejected1Count +
        secondTransactionSummary.rejected1Count,
      totalRejected1Amount:
        etiTransactionSummary.totalRejected1Amount +
        secondTransactionSummary.totalRejected1Amount,
      minimumAmountCount:
        etiTransactionSummary.minimumAmountCount +
        secondTransactionSummary.minimumAmountCount,
      totalMinimumAmount:
        etiTransactionSummary.totalMinimumAmount +
        secondTransactionSummary.totalMinimumAmount,
      partialReturnCount:
        etiTransactionSummary.partialReturnCount +
        secondTransactionSummary.partialReturnCount,
      totalPartialReturnAmount:
        etiTransactionSummary.totalPartialReturnAmount +
        secondTransactionSummary.totalPartialReturnAmount,
    };

    const mockSettlementsWithContractChange = { ...mockSettlementsResult };

    mockSettlementsWithContractChange[
      customerCustomerTypeId
    ]!.data!.result.settlement["ETI"]!.totalTransactionSummary =
      totalTransactionSummary;

    mockSettlementsWithContractChange[
      customerCustomerTypeId
    ]!.data!.result.settlement["ETI"]!.isContractChange = true;

    (
      mockSettlementsWithContractChange[customerCustomerTypeId]!.data!.result
        .settlement["ETI"]?.feesBreakdown as MerchantFeesBreakdown
    )?.push(secondFeesBreakdown);

    await saveFrequencySettlements(
      mockSettlementsWithContractChange,
      fromDate,
      toDate,
      prisma
    );

    expect(prisma.customerSettlements.upsert).toHaveBeenCalledWith({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId,
          platformId: etiPlatformId,
          fromDate: fromDateOnly(fromDate),
          toDate: fromDateOnly(toDate),
        },
      },
      update: {
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: etiFees.gatewayFeeTotal,
        transactionFee: etiFees.transactionFeeTotal + etiFees.rejected1FeeTotal,
        salesFee: etiFees.salesFeeTotal,
        refundFee: etiFees.refundFeeTotal,
        minimumFeeTotal: etiFees.minimumFeeTotal,
        transactionCount:
          etiTransactionSummary.transactionCount +
          secondFeesBreakdown.meta.transactionSummary.transactionCount,
        totalTransactionAmount:
          etiTransactionSummary.totalTransactionAmount +
          secondFeesBreakdown.meta.transactionSummary.totalTransactionAmount,
        refundCount:
          etiTransactionSummary.refundCount +
          secondFeesBreakdown.meta.transactionSummary.refundCount,
        totalRefundAmount:
          etiTransactionSummary.totalRefundAmount +
          secondFeesBreakdown.meta.transactionSummary.totalRefundAmount,
        totalFailedAmount:
          etiTransactionSummary.totalFailedAmount +
          secondFeesBreakdown.meta.transactionSummary.totalFailedAmount,
        total2FaRejectCount:
          etiTransactionSummary.rejected1Count +
          secondFeesBreakdown.meta.transactionSummary.rejected1Count,
        total2FaRejectAmount:
          etiTransactionSummary.totalRejected1Amount +
          secondFeesBreakdown.meta.transactionSummary.totalRejected1Amount,
        txnCountETI_R1:
          etiTransactionSummary._RCount +
          secondFeesBreakdown.meta.transactionSummary._RCount,
        txnAmountRTO_R:
          etiTransactionSummary.total_RAmount +
          secondFeesBreakdown.meta.transactionSummary.total_RAmount,
        minimumFeeCount:
          etiTransactionSummary.minimumAmountCount +
          secondFeesBreakdown.meta.transactionSummary.minimumAmountCount,
        totalMinimumAmount:
          etiTransactionSummary.totalMinimumAmount +
          secondFeesBreakdown.meta.transactionSummary.totalMinimumAmount,
        partialReturnAmountRTO:
          etiTransactionSummary.totalPartialReturnAmount +
          secondFeesBreakdown.meta.transactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO:
          etiTransactionSummary.partialReturnCount +
          secondFeesBreakdown.meta.transactionSummary.partialReturnCount,
        meta: {
          feesBreakdown: [
            (
              mockSettlementsResult[customerCustomerTypeId]!.data!.result
                .settlement["ETI"]!.feesBreakdown as MerchantFeesBreakdown
            )[0],
            secondFeesBreakdown,
          ],
        },
      },
      create: {
        customerId,
        platformId: etiPlatformId,
        fromDate: fromDateOnly(fromDate),
        toDate: fromDateOnly(toDate),
        customerCustomerTypeId,
        status: "INIT",
        gatewayFee: etiFees.gatewayFeeTotal,
        transactionFee: etiFees.transactionFeeTotal + etiFees.rejected1FeeTotal,
        salesFee: etiFees.salesFeeTotal,
        refundFee: etiFees.refundFeeTotal,
        minimumFeeTotal: etiFees.minimumFeeTotal,
        transactionCount:
          etiTransactionSummary.transactionCount +
          secondFeesBreakdown.meta.transactionSummary.transactionCount,
        totalTransactionAmount:
          etiTransactionSummary.totalTransactionAmount +
          secondFeesBreakdown.meta.transactionSummary.totalTransactionAmount,
        refundCount:
          etiTransactionSummary.refundCount +
          secondFeesBreakdown.meta.transactionSummary.refundCount,
        totalRefundAmount:
          etiTransactionSummary.totalRefundAmount +
          secondFeesBreakdown.meta.transactionSummary.totalRefundAmount,
        totalFailedAmount:
          etiTransactionSummary.totalFailedAmount +
          secondFeesBreakdown.meta.transactionSummary.totalFailedAmount,
        total2FaRejectCount:
          etiTransactionSummary.rejected1Count +
          secondFeesBreakdown.meta.transactionSummary.rejected1Count,
        total2FaRejectAmount:
          etiTransactionSummary.totalRejected1Amount +
          secondFeesBreakdown.meta.transactionSummary.totalRejected1Amount,
        txnCountETI_R1:
          etiTransactionSummary._RCount +
          secondFeesBreakdown.meta.transactionSummary._RCount,
        txnAmountRTO_R:
          etiTransactionSummary.total_RAmount +
          secondFeesBreakdown.meta.transactionSummary.total_RAmount,
        minimumFeeCount:
          etiTransactionSummary.minimumAmountCount +
          secondFeesBreakdown.meta.transactionSummary.minimumAmountCount,
        totalMinimumAmount:
          etiTransactionSummary.totalMinimumAmount +
          secondFeesBreakdown.meta.transactionSummary.totalMinimumAmount,
        partialReturnAmountRTO:
          etiTransactionSummary.totalPartialReturnAmount +
          secondFeesBreakdown.meta.transactionSummary.totalPartialReturnAmount,
        partialReturnCountRTO:
          etiTransactionSummary.partialReturnCount +
          secondFeesBreakdown.meta.transactionSummary.partialReturnCount,
        meta: {
          feesBreakdown: [
            (
              mockSettlementsResult[customerCustomerTypeId]!.data!.result
                .settlement["ETI"]!.feesBreakdown as MerchantFeesBreakdown
            )[0],
            secondFeesBreakdown,
          ],
        },
      },
    });
  });

  it("should throw an error if customer is not found", async () => {
    // Resetting the mock makes it return undefined
    vi.mocked(getCustomerIdByCustomerCustomerType).mockReset();

    await expect(
      saveFrequencySettlements(mockSettlementsResult, fromDate, toDate, prisma)
    ).rejects.toThrowError(
      `Customer not found for customerCustomerTypeId ${customerCustomerTypeId}`
    );
  });

  it("should throw an error if feesBreakdown is not found", async () => {
    vi.mocked(prisma.$transaction).mockImplementation(async (callback) =>
      callback(prisma)
    );

    const mockSettlementsWithoutFeesBreakdown = { ...mockSettlementsResult };

    delete mockSettlementsWithoutFeesBreakdown[customerCustomerTypeId]!.data!
      .result.settlement["ETI"]!.feesBreakdown;

    await expect(
      saveFrequencySettlements(
        mockSettlementsWithoutFeesBreakdown,
        fromDate,
        toDate,
        prisma
      )
    ).rejects.toThrowError(
      "Fees breakdown is missing for ETI platform settlement"
    );
  });
});

describe("getFrequencyPlatformSettlement", () => {
  it("should return undefined if settlement does not exist", async () => {
    const result = await getFrequencyPlatformSettlement(
      customerId,
      1,
      fromDate,
      prisma
    );
    expect(result).toBeUndefined();
  });

  it("should return the settlement if it exists", async () => {
    const settlementId = 1;
    const endBalance = 1000;

    vi.mocked(prisma.customerSettlements.findFirst).mockResolvedValue({
      customerSettlementsId: settlementId,
      customerId,
      platformId: 1,
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      customerCustomerTypeId,
      status: "INIT",
      endBalance: new Decimal(endBalance),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      approvedBy: null,
      transactionCount: null,
      totalTransactionAmount: null,
      refundCount: null,
      totalRefundAmount: null,
      gatewayFee: null,
      transactionFee: null,
      salesFee: null,
      refundFee: null,
      totalFailedAmount: null,
      tierItemId: null,
      regenerationCount: null,
      emailSentAt: null,
      merchantPlatformId: null,
      integratorFee: null,
      agentFee: null,
      subAgentFee: null,
      total2FaRejectAmount: null,
      total2FaRejectCount: null,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: null,
      minimumFeeCount: null,
      totalMinimumAmount: null,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      meta: null,
    });

    const result = await getFrequencyPlatformSettlement(
      customerId,
      1,
      fromDate,
      prisma
    );

    expect(result).toEqual({
      settlementId,
      endBalance,
    });
  });

  it("should have endBalance as 0 if endBalance is null", async () => {
    const settlementId = 1;

    vi.mocked(prisma.customerSettlements.findFirst).mockResolvedValue({
      customerSettlementsId: settlementId,
      customerId,
      platformId: 1,
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      customerCustomerTypeId,
      status: "INIT",
      endBalance: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      approvedBy: null,
      transactionCount: null,
      totalTransactionAmount: null,
      refundCount: null,
      totalRefundAmount: null,
      gatewayFee: null,
      transactionFee: null,
      salesFee: null,
      refundFee: null,
      totalFailedAmount: null,
      tierItemId: null,
      regenerationCount: null,
      emailSentAt: null,
      merchantPlatformId: null,
      integratorFee: null,
      agentFee: null,
      subAgentFee: null,
      total2FaRejectAmount: null,
      total2FaRejectCount: null,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: null,
      minimumFeeCount: null,
      totalMinimumAmount: null,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      meta: null,
    });

    const result = await getFrequencyPlatformSettlement(
      customerId,
      1,
      fromDate,
      prisma
    );

    expect(result).toEqual({
      settlementId,
      endBalance: 0,
    });
  });
});

describe("createFrequencySettlementSchedule", () => {
  it("should create a frequency settlement schedule", async () => {
    const customer = {
      customerId: 2,
      customerCustomerTypeId: 12,
      serviceNumber: "99999",
      entityName: "Gigadat",
      entityId: 1,
      entityLogoName: "gigadat-logo.png",
      customerTradingName: "Tester",
      customerName: "Tester",
      enabled: true,
      customerTypeName: "Merchant",
      customerTypeId: 3,
      statementFolderLocation: "test",
    };

    vi.mocked(
      prisma.customerSettlementGenerationSchedule.create
    ).mockResolvedValue({
      customerSettlementGenerationScheduleId: scheduleId,
      serviceNumber: customer.serviceNumber,
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      generationDate: new Date(),
      startDate: new Date(),
      generationType: "INITIAL",
      generationStatus: "STARTED",
      customerCustomerTypeId: customer.customerCustomerTypeId,
      completionDate: new Date(),
      errorMessage: null,
      parentScheduleId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      fromUI: null,
    });

    const schedule = await createFrequencySettlementSchedule(
      customer,
      fromDate,
      toDate,
      prisma
    );

    expect(schedule).toEqual({
      scheduleId,
      generationType: "INITIAL",
      generationStatus: "STARTED",
    });
  });
});

describe("getFrequencySettlementSchedule", () => {
  it("should return undefined if schedule does not exist", async () => {
    const result = await getFrequencySettlementSchedule(
      customerCustomerTypeId,
      fromDateOnly(fromDate),
      fromDateOnly(toDate),
      prisma
    );
    expect(result).toBeUndefined();
  });

  it("should return the schedule if it exists", async () => {
    vi.mocked(
      prisma.customerSettlementGenerationSchedule.findFirst
    ).mockResolvedValue({
      customerSettlementGenerationScheduleId: scheduleId,
      serviceNumber: "99999",
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      generationDate: new Date(),
      startDate: new Date(),
      generationType: "INITIAL",
      generationStatus: "STARTED",
      customerCustomerTypeId,
      completionDate: new Date(),
      errorMessage: null,
      parentScheduleId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      fromUI: null,
    });

    const result = await getFrequencySettlementSchedule(
      customerCustomerTypeId,
      fromDateOnly(fromDate),
      fromDateOnly(toDate),
      prisma
    );

    expect(result).toEqual({
      scheduleId,
      generationType: "INITIAL",
      generationStatus: "STARTED",
    });
  });

  it("should return the schedule with error message if it exists", async () => {
    vi.mocked(
      prisma.customerSettlementGenerationSchedule.findFirst
    ).mockResolvedValue({
      customerSettlementGenerationScheduleId: scheduleId,
      serviceNumber: "99999",
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      generationDate: new Date(),
      startDate: new Date(),
      generationType: "INITIAL",
      generationStatus: "ERROR",
      customerCustomerTypeId,
      completionDate: new Date(),
      errorMessage: "An error occurred",
      parentScheduleId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      fromUI: null,
    });

    const result = await getFrequencySettlementSchedule(
      customerCustomerTypeId,
      fromDateOnly(fromDate),
      fromDateOnly(toDate),
      prisma
    );

    expect(result).toEqual({
      scheduleId,
      generationType: "INITIAL",
      generationStatus: "ERROR",
      errorMessage: "An error occurred",
    });
  });
});

describe("getSettlementById", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should return the settlement if it exists", async () => {
    const fromDate = new Date();
    const toDate = new Date();

    vi.mocked(prisma.customerSettlements.findUnique).mockResolvedValue({
      // @ts-expect-error - this is a mock, type does not matter
      customer: {
        customerId: 1,
        customerName: "Test Customer",
        serviceNumber: "99999",
      },
      customerCustomerType: {
        customerType: {
          customerTypeName: "Merchant",
        },
        customerCustomerTypeId: 3,
      },
      fromDate,
      toDate,
    });

    const result = await getSettlementById(prisma, 1);

    expect(result).toEqual({
      customerId: 1,
      customerName: "Test Customer",
      serviceNumber: "99999",
      customerType: "Merchant",
      customerCustomerTypeId: 3,
      fromDate,
      toDate,
    });
  });

  it("should return undefined if settlement does not exist", async () => {
    const result = await getSettlementById(prisma, 1);

    expect(result).toBeUndefined();
  });
});

describe("getAllSettlementsByCustomerAndPeriod", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should return an empty array if no settlements exist", async () => {
    vi.mocked(prisma.customerSettlements.findMany).mockResolvedValue([]);

    const result = await getAllSettlementsByCustomerAndPeriod(
      1,
      new Date(),
      new Date(),
      prisma
    );

    expect(result).toEqual([]);
  });

  it("should return settlements with adjustments", async () => {
    vi.mocked(prisma.customerSettlements.findMany).mockResolvedValue([
      {
        // @ts-expect-error - this is a mock, type does not matter
        platform: { platformCode: "ETI" },
        customerSettlementAdjustments: [
          {
            label: "Adjustment 1",
            amount: new Decimal(100),
            displayCommentExcel: true,
            comment: "Test comment",
          },
          {
            label: null,
            amount: null,
            displayCommentExcel: null,
            comment: null,
          },
        ],
        customerSettlementKyc: [],
        transactionCount: 10,
        totalTransactionAmount: new Decimal(1000),
        refundCount: 2,
        totalRefundAmount: new Decimal(200),
        gatewayFee: new Decimal(10),
        transactionFee: new Decimal(20),
        salesFee: new Decimal(30),
        refundFee: new Decimal(40),
        totalFailedAmount: new Decimal(50),
        endBalance: new Decimal(60),
        total2FaRejectAmount: new Decimal(70),
        total2FaRejectCount: 3,
        txnAmountRTO_R: 80,
        txnCountETI_R1: 4,
        minimumFeeTotal: new Decimal(90),
        minimumFeeCount: 1,
        totalMinimumAmount: new Decimal(100),
        partialReturnAmountRTO: 110,
        partialReturnCountRTO: 2,
      },
    ]);

    const result = await getAllSettlementsByCustomerAndPeriod(
      1,
      new Date(),
      new Date(),
      prisma
    );

    expect(result).toEqual([
      {
        ETI: {
          transactionCount: 10,
          totalTransactionAmount: 1000,
          refundCount: 2,
          totalRefundAmount: 200,
          gatewayFee: 10,
          transactionFee: 20,
          salesFee: 30,
          refundFee: 40,
          totalFailedAmount: 50,
          endBalance: 60,
          total2FaRejectAmount: 70,
          total2FaRejectCount: 3,
          txnAmountRTO_R: 80,
          txnCountETI_R1: 4,
          minimumFeeTotal: 90,
          minimumFeeCount: 1,
          totalMinimumAmount: 100,
          partialReturnAmountRTO: 110,
          partialReturnCountRTO: 2,
          isAdjusted: true,
          adjustments: [
            {
              label: "Adjustment 1",
              amount: 100,
              displayCommentExcel: true,
              comment: "Test comment",
            },
            {
              label: "",
              amount: 0,
              displayCommentExcel: false,
              comment: "",
            },
          ],
        },
      },
    ]);
  });

  it("should return settlements with kycDetails", async () => {
    vi.mocked(prisma.customerSettlements.findMany).mockResolvedValue([
      {
        // @ts-expect-error - this is a mock, type does not matter
        platform: { platformCode: "KYC" },
        customerSettlementAdjustments: [
          {
            label: "Adjustment 1",
            amount: new Decimal(100),
            displayCommentExcel: true,
            comment: "Test comment",
          },
        ],
        customerSettlementKyc: [
          {
            kycType: { kycName: "KY1" },
            transactionCount: 5,
            totalTransactionAmount: new Decimal(500),
          },
          {
            kycType: { kycName: "KY2" },
            transactionCount: null,
            totalTransactionAmount: null,
          },
        ],
        transactionCount: 10,
        totalTransactionAmount: new Decimal(500),
        refundCount: 2,
        totalRefundAmount: new Decimal(200),
        gatewayFee: new Decimal(10),
        transactionFee: new Decimal(20),
        salesFee: new Decimal(30),
        refundFee: new Decimal(40),
        totalFailedAmount: new Decimal(50),
        endBalance: new Decimal(60),
        total2FaRejectAmount: new Decimal(70),
        total2FaRejectCount: 3,
        txnAmountRTO_R: 80,
        txnCountETI_R1: 4,
        minimumFeeTotal: new Decimal(90),
        minimumFeeCount: 1,
        totalMinimumAmount: new Decimal(100),
        partialReturnAmountRTO: 110,
        partialReturnCountRTO: 2,
      },
    ]);

    const result = await getAllSettlementsByCustomerAndPeriod(
      1,
      new Date(),
      new Date(),
      prisma
    );

    expect(result).toEqual([
      {
        KYC: {
          transactionCount: 10,
          totalTransactionAmount: 500,
          refundCount: 2,
          totalRefundAmount: 200,
          gatewayFee: 10,
          transactionFee: 20,
          salesFee: 30,
          refundFee: 40,
          totalFailedAmount: 50,
          endBalance: 60,
          total2FaRejectAmount: 70,
          total2FaRejectCount: 3,
          txnAmountRTO_R: 80,
          txnCountETI_R1: 4,
          minimumFeeTotal: 90,
          minimumFeeCount: 1,
          totalMinimumAmount: 100,
          partialReturnAmountRTO: 110,
          partialReturnCountRTO: 2,
          isAdjusted: true,
          adjustments: [
            {
              label: "Adjustment 1",
              amount: 100,
              displayCommentExcel: true,
              comment: "Test comment",
            },
          ],
          kycDetails: {
            KY1: {
              transactionCount: 5,
              totalTransactionAmount: 500,
            },
            KY2: {
              transactionCount: 0,
              totalTransactionAmount: 0,
            },
          },
        },
      },
    ]);
  });

  it("should return settlements with no adjustments and no kyc details and null fields", async () => {
    vi.mocked(prisma.customerSettlements.findMany).mockResolvedValue([
      {
        // @ts-expect-error - this is a mock, type does not matter
        platform: { platformCode: "RFM" },
        customerSettlementAdjustments: [],
        customerSettlementKyc: [],
        transactionCount: null,
        totalTransactionAmount: null,
        refundCount: null,
        totalRefundAmount: null,
        gatewayFee: null,
        transactionFee: null,
        salesFee: null,
        refundFee: null,
        totalFailedAmount: null,
        endBalance: null,
        total2FaRejectAmount: null,
        total2FaRejectCount: null,
        txnAmountRTO_R: 0,
        txnCountETI_R1: 0,
        minimumFeeTotal: null,
        minimumFeeCount: null,
        totalMinimumAmount: null,
        partialReturnAmountRTO: 0,
        partialReturnCountRTO: 0,
      },
    ]);

    const result = await getAllSettlementsByCustomerAndPeriod(
      1,
      new Date(),
      new Date(),
      prisma
    );

    expect(result).toEqual([
      {
        RFM: {
          transactionCount: 0,
          totalTransactionAmount: 0,
          refundCount: 0,
          totalRefundAmount: 0,
          gatewayFee: 0,
          transactionFee: 0,
          salesFee: 0,
          refundFee: 0,
          totalFailedAmount: 0,
          endBalance: 0,
          total2FaRejectAmount: 0,
          total2FaRejectCount: 0,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          minimumFeeTotal: 0,
          minimumFeeCount: 0,
          totalMinimumAmount: 0,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: false,
          adjustments: [],
        },
      },
    ]);
  });
});
