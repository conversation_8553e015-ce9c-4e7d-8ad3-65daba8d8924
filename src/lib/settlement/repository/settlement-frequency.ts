/* eslint-disable @typescript-eslint/naming-convention */

import {
  kycClientPlatformId,
  merchantTypeId,
  statusInit,
} from "@constants/settlement";
import { type Customer } from "@lib/customer/repository/types";
import { getCustomerIdByCustomerCustomerType } from "@lib/customer-customer-type/repository";
import {
  type KycDetails,
  type FrequencySettlementSchedule,
  type FrequencySettlementsResult,
  type PlatformSettlements,
} from "@lib/settlement/repository/types";
import { type DateOnly, fromDateOnly } from "@utils/date-only";

import {
  type KycRecord,
  type NonMerchantFeesBreakdown,
  type SettlementResult,
} from "../functions/workers/types";

import type { Prisma, PrismaClient } from "@prisma/client";

const saveFrequencySettlements = async (
  settlements: FrequencySettlementsResult,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<void> => {
  for (const [customerCustomerTypeId, result] of Object.entries(settlements)) {
    const merchantSettlementResult = result.data?.result;

    const fromDateString = fromDateOnly(fromDate);
    const toDateString = fromDateOnly(toDate);

    const customerCustomerType =
      // eslint-disable-next-line no-await-in-loop
      await getCustomerIdByCustomerCustomerType(
        Number(customerCustomerTypeId),
        prisma
      );

    if (!customerCustomerType?.customerId) {
      throw new Error(
        `Customer not found for customerCustomerTypeId ${customerCustomerTypeId}`
      );
    }

    // eslint-disable-next-line no-await-in-loop
    await prisma.$transaction(
      async (transactionPrisma) => {
        await _updateFrequencySettlementSchedule(
          result.scheduleId,
          result.status === "SUCCESS" ? "COMPLETE" : result.status,
          transactionPrisma,
          result?.message
        );

        if (merchantSettlementResult?.settlement) {
          await _saveFrequencyPlatformSettlements(
            {
              customerId: customerCustomerType?.customerId,
              customerTypeId: customerCustomerType?.customerTypeId,
              customerCustomerTypeId: Number(customerCustomerTypeId),
            },
            { fromDate, toDate },
            {
              merchantSettlement: merchantSettlementResult,
              endBalance: result.data?.endBalance ?? 0,
            },
            transactionPrisma
          );
        }

        if (merchantSettlementResult?.kyc) {
          const kycRecordsArray = Object.values(merchantSettlementResult.kyc);

          const aggregatedTransactionCount: number = kycRecordsArray.reduce(
            (sum: number, record) => sum + (record.transactionCount || 0),
            0
          );
          const aggregatedTransactionAmount: number = kycRecordsArray.reduce(
            (sum: number, record) => sum + (record.totalTransactionAmount || 0),
            0
          );

          const kycSettlement =
            await transactionPrisma.customerSettlements.upsert({
              where: {
                customerId_platformId_fromDate_toDate: {
                  customerId: customerCustomerType.customerId,
                  platformId: kycClientPlatformId,
                  fromDate: fromDateString,
                  toDate: toDateString,
                },
              },
              update: {
                customerCustomerTypeId: Number(customerCustomerTypeId),
                status: statusInit,
                transactionCount: aggregatedTransactionCount,
                totalTransactionAmount: aggregatedTransactionAmount,
              },
              create: {
                customerId: customerCustomerType.customerId,
                platformId: kycClientPlatformId,
                fromDate: fromDateString,
                toDate: toDateString,
                customerCustomerTypeId: Number(customerCustomerTypeId),
                status: statusInit,
                transactionCount: aggregatedTransactionCount,
                totalTransactionAmount: aggregatedTransactionAmount,
              },
            });

          await upsertSettlementKycRecords(
            transactionPrisma,
            kycSettlement.customerSettlementsId,
            kycRecordsArray
          );
        }
      },
      { timeout: 50_000, maxWait: 10_000 }
    );
  }
};

async function upsertSettlementKycRecords(
  tx: Prisma.TransactionClient,
  customerSettlementsId: number,
  kycRecords: KycRecord[]
): Promise<void> {
  // Update or create:
  await Promise.all(
    kycRecords.map(async ({ kycTypeId, ...data }) => {
      const { count } = await tx.customerSettlementKyc.updateMany({
        where: { customerSettlementsId, kycTypeId },
        data,
      });

      if (count === 0) {
        await tx.customerSettlementKyc.create({
          data: { customerSettlementsId, kycTypeId, ...data },
        });
      }
    })
  );

  // Delete didn't include types
  const incomingTypeIds = kycRecords.map((r) => r.kycTypeId);
  await tx.customerSettlementKyc.deleteMany({
    where: {
      customerSettlementsId,
      kycTypeId: { notIn: incomingTypeIds },
    },
  });
}

const getFrequencyPlatformSettlement = async (
  customerId: number,
  platformId: number,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<{ settlementId: number; endBalance: number } | undefined> => {
  const platformSettlement = await prisma.customerSettlements.findFirst({
    where: {
      customerId,
      platformId,
      toDate: fromDateOnly(toDate),
    },
  });

  if (!platformSettlement) {
    return;
  }

  return {
    settlementId: platformSettlement.customerSettlementsId,
    endBalance: platformSettlement.endBalance?.toNumber() ?? 0,
  };
};

const createFrequencySettlementSchedule = async (
  customer: Customer,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<FrequencySettlementSchedule> => {
  const currentDate = new Date();

  const schedule = await prisma.customerSettlementGenerationSchedule.create({
    data: {
      serviceNumber: customer.serviceNumber,
      fromDate: fromDateOnly(fromDate),
      toDate: fromDateOnly(toDate),
      generationDate: currentDate,
      startDate: currentDate,
      generationType: "INITIAL",
      generationStatus: "STARTED",
      customerCustomerTypeId: customer.customerCustomerTypeId,
    },
  });

  return {
    scheduleId: schedule.customerSettlementGenerationScheduleId,
    generationType:
      schedule.generationType as FrequencySettlementSchedule["generationType"],
    generationStatus:
      schedule.generationStatus as FrequencySettlementSchedule["generationStatus"],
  };
};

const getFrequencySettlementSchedule = async (
  customerCustomerTypeId: number,
  fromDate: Date,
  toDate: Date,
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<FrequencySettlementSchedule | undefined> => {
  const schedule = await prisma.customerSettlementGenerationSchedule.findFirst({
    where: {
      customerCustomerTypeId,
      fromDate,
      toDate,
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  if (!schedule) {
    return;
  }

  return {
    scheduleId: schedule.customerSettlementGenerationScheduleId,
    generationType:
      schedule.generationType as FrequencySettlementSchedule["generationType"],
    generationStatus:
      schedule.generationStatus as FrequencySettlementSchedule["generationStatus"],
    ...(schedule.errorMessage && {
      errorMessage: schedule.errorMessage,
    }),
  };
};

const _updateFrequencySettlementSchedule = async (
  scheduleId: number,
  status: "COMPLETE" | "SKIPPED" | "ERROR",
  prisma: Prisma.TransactionClient,
  errorMessage?: string
): Promise<void> => {
  await prisma.customerSettlementGenerationSchedule.update({
    where: {
      customerSettlementGenerationScheduleId: scheduleId,
    },
    data: {
      completionDate: new Date(),
      generationStatus: status,
      errorMessage: errorMessage ?? null,
    },
  });
};

const _saveFrequencyPlatformSettlements = async (
  customer: {
    customerId: number;
    customerTypeId: number;
    customerCustomerTypeId: number;
  },
  period: { fromDate: DateOnly; toDate: DateOnly },
  data: { merchantSettlement: SettlementResult; endBalance: number },
  transactionPrisma: Prisma.TransactionClient
): Promise<void> => {
  const fromDate = fromDateOnly(period.fromDate);
  const toDate = fromDateOnly(period.toDate);

  const platformSettlements = Object.entries(
    data.merchantSettlement.settlement
  ).map(async ([platformCode, platformSettlement]) => {
    const { totalChargedFees } = platformSettlement;
    const feesBreakdown =
      platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

    if (!feesBreakdown) {
      throw new Error(
        `Fees breakdown is missing for ${platformCode} platform settlement`
      );
    }

    const totalFees = {
      gatewayFee: totalChargedFees.gatewayFeeTotal,
      transactionFee:
        totalChargedFees.transactionFeeTotal +
        totalChargedFees.rejected1FeeTotal,
      salesFee: totalChargedFees.salesFeeTotal,
      refundFee: totalChargedFees.refundFeeTotal,
      minimumFeeTotal: totalChargedFees.minimumFeeTotal,
    };

    // Rename the keys to match the database column names
    const transactionSummary = {
      transactionCount:
        platformSettlement.totalTransactionSummary.transactionCount +
        platformSettlement.totalTransactionSummary.minimumAmountCount,
      totalTransactionAmount:
        customer.customerTypeId === merchantTypeId
          ? platformSettlement.totalTransactionSummary.totalTransactionAmount +
            platformSettlement.totalTransactionSummary.totalMinimumAmount
          : feesBreakdown?.totalCommission,
      refundCount: platformSettlement.totalTransactionSummary.refundCount,
      totalRefundAmount:
        platformSettlement.totalTransactionSummary.totalRefundAmount,
      totalFailedAmount:
        platformSettlement.totalTransactionSummary.totalFailedAmount,
      total2FaRejectCount:
        platformSettlement.totalTransactionSummary.rejected1Count,
      total2FaRejectAmount:
        platformSettlement.totalTransactionSummary.totalRejected1Amount,
      txnCountETI_R1: platformSettlement.totalTransactionSummary._RCount,
      txnAmountRTO_R: platformSettlement.totalTransactionSummary.total_RAmount,
      minimumFeeCount:
        platformSettlement.totalTransactionSummary.minimumAmountCount,
      totalMinimumAmount:
        platformSettlement.totalTransactionSummary.totalMinimumAmount,
      partialReturnAmountRTO:
        platformSettlement.totalTransactionSummary.totalPartialReturnAmount,
      partialReturnCountRTO:
        platformSettlement.totalTransactionSummary.partialReturnCount,
    };

    return transactionPrisma.customerSettlements.upsert({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId: customer.customerId,
          platformId: platformSettlement.platformId,
          fromDate,
          toDate,
        },
      },
      update: {
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        ...totalFees,
        ...transactionSummary,
        meta: { feesBreakdown },
      },
      create: {
        customerId: customer.customerId,
        platformId: platformSettlement.platformId,
        fromDate,
        toDate,
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        ...totalFees,
        ...transactionSummary,
        meta: { feesBreakdown },
      },
    });
  });

  // Overall summary - required for old FA to display the settlement
  platformSettlements.push(
    transactionPrisma.customerSettlements.upsert({
      where: {
        customerId_platformId_fromDate_toDate: {
          customerId: customer.customerId,
          platformId: 1,
          fromDate,
          toDate,
        },
      },
      update: {
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        endBalance: data.endBalance,
      },
      create: {
        customerId: customer.customerId,
        platformId: 1,
        fromDate,
        toDate,
        customerCustomerTypeId: customer.customerCustomerTypeId,
        status: statusInit,
        endBalance: data.endBalance,
      },
    })
  );

  await Promise.all(platformSettlements);
};

const getSettlementById = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  id: number
): Promise<
  | {
      customerId: number;
      customerName: string;
      serviceNumber: string;
      customerType: string | undefined;
      customerCustomerTypeId: number | undefined;
      fromDate: Date;
      toDate: Date;
      emailId?: number | undefined;
      deletedAt?: string | undefined;
    }
  | undefined
> => {
  const settlement = await prisma.customerSettlements.findUnique({
    where: { customerSettlementsId: id },
    select: {
      customer: {
        select: {
          customerId: true,
          customerName: true,
          serviceNumber: true,
        },
      },
      customerCustomerType: {
        select: {
          customerType: {
            select: {
              customerTypeName: true,
            },
          },
          customerCustomerTypeId: true,
        },
      },
      fromDate: true,
      toDate: true,
      wire: {
        where: {
          isCancelled: false,
        },
        select: {
          email: {
            select: {
              emailId: true,
              deletedAt: true,
            },
          },
        },
      },
    },
  });

  if (!settlement) {
    return;
  }

  return {
    customerId: settlement.customer.customerId,
    customerName: settlement.customer.customerName,
    serviceNumber: settlement.customer.serviceNumber,
    customerType:
      settlement.customerCustomerType?.customerType.customerTypeName,
    customerCustomerTypeId:
      settlement.customerCustomerType?.customerCustomerTypeId,
    fromDate: settlement.fromDate,
    toDate: settlement.toDate,
    emailId: settlement?.wire?.[0]?.email?.[0]?.emailId,
    deletedAt: settlement?.wire?.[0]?.email?.[0]?.deletedAt?.toString(),
  };
};

const getSettlementIdByCustomerIdPlatformIdAndPeriod = async (
  customerId: number,
  platformId: number,
  fromDate: Date,
  toDate: Date,
  prisma: PrismaClient
): Promise<number> => {
  const result = await prisma.customerSettlements.findFirst({
    where: {
      customerId: Number(customerId),
      platformId,
      fromDate,
      toDate,
    },
    select: {
      customerSettlementsId: true,
    },
  });

  if (!result) {
    throw new Error(
      `Customer settlement not found for platform with Id ${platformId} from ${fromDate.toISOString()} to ${toDate.toISOString()}.`
    );
  }

  return result.customerSettlementsId;
};

const getAllSettlementsByCustomerAndPeriod = async (
  customerId: number,
  fromDate: Date,
  toDate: Date,
  prisma: PrismaClient
): Promise<PlatformSettlements[]> => {
  const settlements = await prisma.customerSettlements.findMany({
    where: {
      customerId,
      fromDate,
      toDate,
    },
    select: {
      platform: {
        select: {
          platformCode: true,
          settlementDescription: true,
          displaySequence: true,
        },
      },
      customerSettlementAdjustments: {
        where: {
          deletedAt: null,
        },
        select: {
          customerSettlementAdjustmentId: true,
          label: true,
          amount: true,
          displayCommentExcel: true,
          comment: true,
        },
      },
      customerSettlementKyc: {
        select: {
          kycType: {
            select: {
              kycName: true,
            },
          },
          transactionCount: true,
          totalTransactionAmount: true,
        },
      },
      transactionCount: true,
      totalTransactionAmount: true,
      refundCount: true,
      totalRefundAmount: true,
      gatewayFee: true,
      transactionFee: true,
      salesFee: true,
      refundFee: true,
      totalFailedAmount: true,
      endBalance: true,
      total2FaRejectAmount: true,
      total2FaRejectCount: true,
      txnAmountRTO_R: true,
      txnCountETI_R1: true,
      minimumFeeTotal: true,
      minimumFeeCount: true,
      totalMinimumAmount: true,
      partialReturnAmountRTO: true,
      partialReturnCountRTO: true,
      meta: true,
    },
  });

  return settlements.map((settlement) => ({
    [settlement.platform.platformCode]: {
      labelName: settlement.platform.settlementDescription,
      displaySequence: settlement.platform.displaySequence,
      transactionCount: settlement.transactionCount ?? 0,
      totalTransactionAmount:
        settlement.totalTransactionAmount?.toNumber() ?? 0,
      refundCount: settlement.refundCount ?? 0,
      totalRefundAmount: settlement.totalRefundAmount?.toNumber() ?? 0,
      gatewayFee: settlement.gatewayFee?.toNumber() ?? 0,
      transactionFee: settlement.transactionFee?.toNumber() ?? 0,
      salesFee: settlement.salesFee?.toNumber() ?? 0,
      refundFee: settlement.refundFee?.toNumber() ?? 0,
      totalFailedAmount: settlement.totalFailedAmount?.toNumber() ?? 0,
      endBalance: settlement.endBalance?.toNumber() ?? 0,
      total2FaRejectAmount: settlement.total2FaRejectAmount?.toNumber() ?? 0,
      total2FaRejectCount: settlement.total2FaRejectCount ?? 0,
      txnAmountRTO_R: settlement.txnAmountRTO_R,
      txnCountETI_R1: settlement.txnCountETI_R1,
      minimumFeeTotal: settlement.minimumFeeTotal?.toNumber() ?? 0,
      minimumFeeCount: settlement.minimumFeeCount ?? 0,
      totalMinimumAmount: settlement.totalMinimumAmount?.toNumber() ?? 0,
      partialReturnAmountRTO: settlement.partialReturnAmountRTO,
      partialReturnCountRTO: settlement.partialReturnCountRTO,
      isAdjusted: settlement.customerSettlementAdjustments.some(
        (adjustment) => adjustment.amount?.toNumber() !== 0
      ),
      adjustments: settlement.customerSettlementAdjustments.map(
        (adjustment) => ({
          id: adjustment.customerSettlementAdjustmentId,
          label: adjustment.label ?? "",
          amount: adjustment.amount?.toNumber() ?? 0,
          displayCommentExcel: adjustment.displayCommentExcel ?? false,
          comment: adjustment.comment ?? "",
        })
      ),
      ...(settlement.customerSettlementKyc.length > 0
        ? {
            kycDetails: Object.fromEntries(
              settlement.customerSettlementKyc.map((kyc) => [
                kyc.kycType.kycName,
                {
                  transactionCount: kyc.transactionCount ?? 0,
                  totalTransactionAmount:
                    kyc.totalTransactionAmount?.toNumber() ?? 0,
                },
              ])
            ) as KycDetails,
          }
        : {}),
      ...(settlement.meta ? { meta: settlement.meta } : {}),
    },
  }));
};

export {
  saveFrequencySettlements,
  getFrequencyPlatformSettlement,
  createFrequencySettlementSchedule,
  getFrequencySettlementSchedule,
  getSettlementById,
  getAllSettlementsByCustomerAndPeriod,
  getSettlementIdByCustomerIdPlatformIdAndPeriod,
};
