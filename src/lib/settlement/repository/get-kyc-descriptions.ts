import { type PrismaClient } from "@prisma/client";

import { type KycDescriptionsMap, type KycName } from "./types";

export const getKycDescriptionsMap = async (
  prisma: PrismaClient
): Promise<KycDescriptionsMap> => {
  const kycTypesArray = await prisma.kycType.findMany({
    select: {
      kycName: true,
      kycDescription: true,
    },
    where: {
      deletedAt: null,
    },
  });

  const result: KycDescriptionsMap = {};

  for (const current of kycTypesArray) {
    if (current.kycName) {
      result[current.kycName as KycName] =
        current.kycDescription ?? `${current.kycName} Description`;
    }
  }

  return result;
};
