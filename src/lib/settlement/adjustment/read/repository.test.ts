import { getSettlementAdjustments } from "./repository";

import type { PrismaClient } from "@prisma/client";

const prisma = {
  customerSettlementAdjustments: {
    findMany: vi.fn(),
  },
} as unknown as PrismaClient;

describe("getSettlementAdjustments", () => {
  it("returns mapped adjustments with all fields present", async () => {
    vi.mocked(prisma.customerSettlementAdjustments.findMany).mockResolvedValue([
      {
        customerSettlementAdjustmentId: 1,
        label: "Adjustment 1",
        // @ts-expect-error - Mocking Prisma client
        amount: { toNumber: () => 100 },
        displayCommentExcel: true,
        comment: "Test comment",
      },
    ]);

    const result = await getSettlementAdjustments(prisma, 123);
    expect(result).toEqual([
      {
        id: 1,
        label: "Adjustment 1",
        amount: 100,
        displayCommentExcel: true,
        comment: "Test comment",
      },
    ]);
    expect(prisma.customerSettlementAdjustments.findMany).toHaveBeenCalledWith({
      where: { customerSettlementsId: 123, deletedAt: null },
      select: {
        customerSettlementAdjustmentId: true,
        label: true,
        amount: true,
        displayCommentExcel: true,
        comment: true,
      },
    });
  });

  it("provides default values for null fields", async () => {
    vi.mocked(prisma.customerSettlementAdjustments.findMany).mockResolvedValue([
      // @ts-expect-error - Mocking Prisma client
      {
        customerSettlementAdjustmentId: 2,
        label: null,
        amount: null,
        displayCommentExcel: null,
        comment: null,
      },
    ]);

    const result = await getSettlementAdjustments(prisma, 456);
    expect(result).toEqual([
      {
        id: 2,
        label: "",
        amount: 0,
        displayCommentExcel: false,
        comment: "",
      },
    ]);
  });

  it("returns an empty array if no adjustments found", async () => {
    vi.mocked(prisma.customerSettlementAdjustments.findMany).mockResolvedValue(
      []
    );

    const result = await getSettlementAdjustments(prisma, 789);
    expect(result).toEqual([]);
  });
});
