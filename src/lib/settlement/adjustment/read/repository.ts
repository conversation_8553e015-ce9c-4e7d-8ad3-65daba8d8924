import type { AdjustmentDetails } from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

export const getSettlementAdjustments = async (
  prisma: PrismaClient,
  settlementId: number
): Promise<AdjustmentDetails[]> => {
  const adjustments = await prisma.customerSettlementAdjustments.findMany({
    where: {
      customerSettlementsId: settlementId,
      deletedAt: null,
    },
    select: {
      customerSettlementAdjustmentId: true,
      label: true,
      amount: true,
      displayCommentExcel: true,
      comment: true,
    },
  });

  return adjustments.map((adjustment) => ({
    id: adjustment.customerSettlementAdjustmentId,
    label: adjustment.label ?? "",
    amount: adjustment.amount?.toNumber() ?? 0,
    displayCommentExcel: adjustment.displayCommentExcel ?? false,
    comment: adjustment.comment ?? "",
  }));
};
