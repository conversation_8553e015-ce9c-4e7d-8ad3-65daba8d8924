import { type SettlementSummary } from "@lib/settlement/repository/types";
import { type PrismaClient } from "@prisma/client";

import { type LatestSettlements } from "../../types"; // Or wherever the type is defined
import { groupSettlementData } from "../group-settlement-data";

export async function loadSettlements(
  prisma: PrismaClient,
  orderedFilteredSettlements: LatestSettlements[]
): Promise<SettlementSummary[]> {
  if (orderedFilteredSettlements.length === 0) {
    return [];
  }

  const settlementSummaryPromises = [];

  for (const settlementData of orderedFilteredSettlements) {
    settlementSummaryPromises.push(groupSettlementData(prisma, settlementData));
  }

  const customerSettlementSummaries = await Promise.all(
    settlementSummaryPromises
  );

  return customerSettlementSummaries;
}
