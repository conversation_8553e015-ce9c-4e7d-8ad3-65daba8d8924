import { Prisma } from "@prisma/client";

import { getSubQueryForSettlementsWithFilters } from "./get-sub-query-for-settlements-with-filters";

describe("getSubQueryForSettlementsWithFilters", () => {
  it("should return a valid SQL query with no conditions", () => {
    const result = getSubQueryForSettlementsWithFilters([], [], []);
    expect(result).toEqual(Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId\n          
          WHERE  s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
           `);
  });

  it("should return a valid SQL query with where conditions", () => {
    const whereConditions = [Prisma.sql`c.customerName = 'Test'`];
    const result = getSubQueryForSettlementsWithFilters(
      whereConditions,
      [],
      []
    );
    expect(result).toEqual(Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId\n          
          WHERE c.customerName = 'Test' AND s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
           `);
  });

  it("should return a valid SQL query with join conditions", () => {
    const joinConditions = [
      Prisma.sql`LEFT JOIN customerType ct ON c.customerTypeId = ct.customerTypeId`,
    ];
    const result = getSubQueryForSettlementsWithFilters([], joinConditions, []);
    expect(result).toEqual(Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId
          LEFT JOIN customerType ct ON c.customerTypeId = ct.customerTypeId
          WHERE  s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
           `);
  });

  it("should return a valid SQL query with having conditions", () => {
    const havingConditions = [Prisma.sql`SUM(s.amount) > 100`];
    const result = getSubQueryForSettlementsWithFilters(
      [],
      [],
      havingConditions
    );
    expect(result).toEqual(Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId\n          
          WHERE  s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          HAVING SUM(s.amount) > 100`);
  });

  it("should return a valid SQL query with all conditions", () => {
    const whereConditions = [Prisma.sql`c.customerName = 'Test'`];
    const joinConditions = [
      Prisma.sql`LEFT JOIN customerType ct ON c.customerTypeId = ct.customerTypeId`,
    ];
    const havingConditions = [Prisma.sql`SUM(s.amount) > 100`];

    const result = getSubQueryForSettlementsWithFilters(
      whereConditions,
      joinConditions,
      havingConditions
    );
    expect(result).toEqual(Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId
          LEFT JOIN customerType ct ON c.customerTypeId = ct.customerTypeId
          WHERE c.customerName = 'Test' AND s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          HAVING SUM(s.amount) > 100`);
  });
});
