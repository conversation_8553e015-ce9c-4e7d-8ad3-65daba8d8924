import {
  adjustmentOnly,
  approval,
  noAdjustments,
  noFilter,
  settlementApproval,
  settlementGeneration,
  showAll,
} from "@lib/settlement/constants/default-parameters";
import { type PrismaClient } from "@prisma/client";

import { setIsAdjustedOnSettlements } from "./set-settlements-adjustment";
import { type FilterOptions, type LatestSettlements } from "../../types";

export async function applyFilterOnLatestSettlements(
  prisma: PrismaClient,
  allLatestSettlements: LatestSettlements[],
  filterOptions: FilterOptions
): Promise<LatestSettlements[]> {
  const {
    nameOrServiceNumber,
    clientType,
    displayAdjusted,
    state,
    status,
    frequency,
    startDate,
    endDate,
  } = filterOptions;

  if (nameOrServiceNumber) {
    allLatestSettlements = allLatestSettlements.filter((settlement) => {
      return (
        settlement.customerName
          .toLowerCase()
          .includes(nameOrServiceNumber.toLowerCase()) ||
        settlement.serviceNumber?.toString().includes(nameOrServiceNumber)
      );
    });
  }

  if (startDate) {
    allLatestSettlements = allLatestSettlements.filter((settlement) => {
      return settlement.fromDate >= new Date(startDate);
    });
  }

  if (endDate) {
    allLatestSettlements = allLatestSettlements.filter((settlement) => {
      return settlement.toDate <= new Date(endDate);
    });
  }

  if (clientType) {
    allLatestSettlements = allLatestSettlements.filter(
      (settlement) => settlement.customerTypeName === clientType
    );
  }

  if (displayAdjusted !== showAll) {
    await setIsAdjustedOnSettlements(prisma, allLatestSettlements);

    if (displayAdjusted === adjustmentOnly) {
      allLatestSettlements = allLatestSettlements.filter(
        (settlement) => settlement.isAdjusted
      );
    } else if (displayAdjusted === noAdjustments) {
      allLatestSettlements = allLatestSettlements.filter(
        (settlement) => !settlement.isAdjusted
      );
    }
  }

  if (state) {
    allLatestSettlements = allLatestSettlements.filter(
      (settlement) =>
        settlement.stateName?.toLowerCase() === state.toLowerCase()
    );
  }

  if (status !== noFilter) {
    allLatestSettlements = allLatestSettlements.filter((settlement) =>
      status === settlementApproval
        ? settlement.stateName?.includes(approval)
        : status === settlementGeneration
          ? !settlement.stateName?.includes(approval)
          : true
    );
  }

  if (frequency) {
    allLatestSettlements = allLatestSettlements.filter((settlement) => {
      return settlement.statementFrequencyId?.toString() === frequency;
    });
  }

  return allLatestSettlements;
}
