import { getAdjustedCustomerSettlements } from "@lib/settlement/repository/get-adjusted-customer-settlements";
import { type PrismaClient } from "@prisma/client";

import { setIsAdjustedOnSettlements } from "./set-settlements-adjustment";
import { type LatestSettlements } from "../../types";

vi.mock("@lib/settlement/repository/get-adjusted-customer-settlements", () => ({
  getAdjustedCustomerSettlements: vi.fn(),
}));

const mockPrisma = {} as unknown as PrismaClient;

describe("setIsAdjustedOnSettlements", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("sets isAdjusted to false when no adjustments returned", async () => {
    const settlements: LatestSettlements[] = [
      {
        customerId: 1,
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
        customerSettlementGenerationScheduleId: 1,
        customerName: "Zara",
        customerCustomerTypeId: 1,
        customerTypeId: 1,
        serviceNumber: 1,
        updatedAt: new Date(),
        generationType: "",
        generationStatus: "",
        statementFrequencyId: 0,
        customerTypeName: "",
        status: "",
        isAdjusted: false,
        stateName: "",
      },
    ];

    vi.mocked(getAdjustedCustomerSettlements).mockResolvedValue([]);

    await setIsAdjustedOnSettlements(mockPrisma, settlements);

    expect(settlements[0]?.isAdjusted).toBe(false);
  });

  it("sets isAdjusted to true for matching adjustment", async () => {
    const settlements: LatestSettlements[] = [
      {
        customerId: 1,
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
        customerSettlementGenerationScheduleId: 1,
        customerName: "Zara",
        customerCustomerTypeId: 1,
        customerTypeId: 1,
        serviceNumber: 1,
        updatedAt: new Date(),
        generationType: "",
        generationStatus: "",
        statementFrequencyId: 0,
        customerTypeName: "",
        status: "",
        isAdjusted: false,
        stateName: "",
      },
    ];

    const adjustments = [
      {
        customerId: 1,
        customerCustomerTypeId: 1,
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
      },
    ];

    vi.mocked(getAdjustedCustomerSettlements).mockResolvedValue(adjustments);

    await setIsAdjustedOnSettlements(mockPrisma, settlements);

    expect(settlements[0]?.isAdjusted).toBe(true);
  });

  it("sets isAdjusted to false for non-matching adjustment", async () => {
    const settlements: LatestSettlements[] = [
      {
        customerId: 2,
        fromDate: new Date("2024-02-01"),
        toDate: new Date("2024-02-28"),
        customerSettlementGenerationScheduleId: 1,
        customerName: "Zara",
        customerCustomerTypeId: 1,
        customerTypeId: 1,
        serviceNumber: 1,
        updatedAt: new Date(),
        generationType: "",
        generationStatus: "",
        statementFrequencyId: 0,
        customerTypeName: "",
        status: "",
        isAdjusted: false,
        stateName: "",
      },
    ];

    const adjustments = [
      {
        customerId: 1,
        customerCustomerTypeId: 1,
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
      },
    ];

    vi.mocked(getAdjustedCustomerSettlements).mockResolvedValue(adjustments);

    await setIsAdjustedOnSettlements(mockPrisma, settlements);

    expect(settlements[0]?.isAdjusted).toBe(false);
  });
});
