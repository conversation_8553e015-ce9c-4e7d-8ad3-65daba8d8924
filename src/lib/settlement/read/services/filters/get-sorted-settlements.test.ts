import { describe, it, expect } from "vitest";

import { getSortedSettlements } from "./get-sorted-settlements";
import { SettlementsOrderEnum, type LatestSettlements } from "../../types";

const mockSettlements: LatestSettlements[] = [
  {
    customerSettlementGenerationScheduleId: 1,
    customerName: "Zara",
    customerCustomerTypeId: 1,
    customerTypeId: 1,
    customerId: 101,
    serviceNumber: 1,
    stateName: "Completed",
    fromDate: new Date("2024-01-05"),
    toDate: new Date("2024-01-10"),
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    statementFrequencyId: 0,
    customerTypeName: "",
    status: "",
    isAdjusted: false,
  },
  {
    customerSettlementGenerationScheduleId: 2,
    customerName: "Alice",
    customerCustomerTypeId: 2,
    customerTypeId: 2,
    customerId: 102,
    serviceNumber: 2,
    stateName: "Completed",
    fromDate: new Date("2024-01-01"),
    toDate: new Date("2024-01-15"),
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    statementFrequencyId: 0,
    customerTypeName: "",
    status: "",
    isAdjusted: false,
  },
  {
    customerSettlementGenerationScheduleId: 3,
    customerName: "Bob",
    customerCustomerTypeId: 3,
    customerTypeId: 3,
    customerId: 103,
    serviceNumber: 3,
    stateName: "Completed",
    fromDate: new Date("2024-01-03"),
    toDate: new Date("2024-01-12"),
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    statementFrequencyId: 0,
    customerTypeName: "",
    status: "",
    isAdjusted: false,
  },
];

describe("getSortedSettlements", () => {
  it("sorts by fromDate ascending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.FROM_DATE,
      "asc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Alice", "Bob", "Zara"]);
  });

  it("sorts by fromDate descending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.FROM_DATE,
      "desc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Zara", "Bob", "Alice"]);
  });

  it("sorts by toDate ascending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.TO_DATE,
      "asc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Zara", "Bob", "Alice"]);
  });

  it("sorts by toDate descending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.TO_DATE,
      "desc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Alice", "Bob", "Zara"]);
  });

  it("sorts by customerName ascending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.CLIENT_NAME,
      "asc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Alice", "Bob", "Zara"]);
  });

  it("sorts by customerName descending", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.CLIENT_NAME,
      "desc",
      10,
      0
    );

    expect(result.map((r) => r.customerName)).toEqual(["Zara", "Bob", "Alice"]);
  });

  it("applies pagination with offset and limit", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.FROM_DATE,
      "asc",
      1,
      1
    );

    expect(result.length).toBe(1);
    expect(result[0]?.customerName).toBe("Bob");
  });

  it("returns empty array if offset is beyond list", () => {
    const result = getSortedSettlements(
      [...mockSettlements],
      SettlementsOrderEnum.FROM_DATE,
      "asc",
      10,
      100
    );

    expect(result).toEqual([]);
  });

  it("throws error on invalid sort key", () => {
    expect(() =>
      // eslint-disable-next-line max-len
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any
      getSortedSettlements([...mockSettlements], "INVALID" as any, "asc", 10, 0)
    ).toThrow("Invalid sort key provided");
  });

  it("handles empty input", () => {
    const result = getSortedSettlements(
      [],
      SettlementsOrderEnum.TO_DATE,
      "asc",
      10,
      0
    );
    expect(result).toEqual([]);
  });
});
