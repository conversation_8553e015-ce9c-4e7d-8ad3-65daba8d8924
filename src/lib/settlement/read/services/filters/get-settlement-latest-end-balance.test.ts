import { getSettlementLatestEndBalanceAmount } from "@lib/settlement/repository/get-settlement-latest-end-balance-amount";
import { type Prisma } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import { type LatestSettlements } from "../../types";

let mockPrisma: {
  customerSettlements: {
    findFirst: ReturnType<typeof vi.fn>;
  };
};

const baseSettlementInfo: LatestSettlements = {
  customerId: 123,
  customerCustomerTypeId: 456,
  fromDate: new Date("2023-01-01"),
  toDate: new Date("2023-01-31"),
  customerSettlementGenerationScheduleId: 0,
  serviceNumber: 0,
  updatedAt: new Date(),
  generationType: "",
  generationStatus: "",
  customerTypeId: 0,
  statementFrequencyId: 0,
  customerTypeName: "",
  customerName: "",
  stateName: "",
  status: "",
  isAdjusted: false,
};

beforeEach(() => {
  mockPrisma = {
    customerSettlements: {
      findFirst: vi.fn(),
    },
  };
});

describe("getSettlementLatestEndBalance", () => {
  vi.resetAllMocks();

  it("returns endBalance as string when record is found", async () => {
    mockPrisma.customerSettlements.findFirst.mockResolvedValue({
      endBalance: 1500.75,
      updatedAt: new Date("2023-12-31"),
    });

    const result = await getSettlementLatestEndBalanceAmount(
      mockPrisma as unknown as Prisma.TransactionClient,
      baseSettlementInfo
    );

    expect(result).toBe("1500.75");
  });

  it("returns '0' when no record is found", async () => {
    mockPrisma.customerSettlements.findFirst.mockResolvedValue(null);

    const result = await getSettlementLatestEndBalanceAmount(
      mockPrisma as unknown as Prisma.TransactionClient,
      baseSettlementInfo
    );

    expect(result).toBe("0");
  });

  it("returns '0' if endBalance is null", async () => {
    mockPrisma.customerSettlements.findFirst.mockResolvedValue({
      endBalance: null,
      updatedAt: new Date(),
    });

    const result = await getSettlementLatestEndBalanceAmount(
      mockPrisma as unknown as Prisma.TransactionClient,
      baseSettlementInfo
    );

    expect(result).toBe("0");
  });

  it("throws error if Prisma client throws", async () => {
    mockPrisma.customerSettlements.findFirst.mockRejectedValue(
      new Error("DB error")
    );

    await expect(
      getSettlementLatestEndBalanceAmount(
        mockPrisma as unknown as Prisma.TransactionClient,
        baseSettlementInfo
      )
    ).rejects.toThrow("DB error");
  });

  it("filters by correct platform name", async () => {
    mockPrisma.customerSettlements.findFirst.mockResolvedValue({
      endBalance: 100,
      updatedAt: new Date(),
    });

    await getSettlementLatestEndBalanceAmount(
      mockPrisma as unknown as Prisma.TransactionClient,
      baseSettlementInfo
    );

    expect(mockPrisma.customerSettlements.findFirst).toHaveBeenCalledWith({
      where: {
        customerCustomerTypeId: baseSettlementInfo.customerCustomerTypeId,
        customerId: baseSettlementInfo.customerId,
        toDate: { lt: baseSettlementInfo.toDate },
        platform: {
          platformName: "Summary",
        },
        deletedAt: null,
      },
      select: {
        endBalance: true,
      },
      orderBy: {
        toDate: "desc",
      },
    });
  });

  it("returns correct string if endBalance is an integer", async () => {
    mockPrisma.customerSettlements.findFirst.mockResolvedValue({
      endBalance: 2000,
      updatedAt: new Date(),
    });

    const result = await getSettlementLatestEndBalanceAmount(
      mockPrisma as unknown as Prisma.TransactionClient,
      baseSettlementInfo
    );

    expect(result).toBe("2000");
  });
});
