import {
  approvalError,
  error,
  na,
  notFound,
  processing,
  skipped,
  unknown,
} from "@lib/settlement/constants/default-parameters";
import { getSettlementLatestEndBalance } from "@lib/settlement/read/services/filters/get-settlement-latest-end-balance";
import { getFrequencySettlementSchedule } from "@lib/settlement/repository/settlement-frequency";
import {
  type SettlementState,
  type SettlementSummary,
} from "@lib/settlement/repository/types";
import { type Prisma } from "@prisma/client";
import { format } from "date-fns";

import { getAuthToken } from "../../../../../services/blusky/authentication";
import { getEndBalance } from "../../../../../services/blusky/end-balance";
import { type LatestSettlements } from "../../types";

export async function getSkippedAndErrorSettlementInfo(
  prisma: Prisma.TransactionClient,
  settlementInfo: LatestSettlements
): Promise<SettlementSummary> {
  const customerType = await prisma.customerType.findUnique({
    where: {
      customerTypeId: settlementInfo.customerTypeId,
    },
    select: {
      customerTypeName: true,
    },
  });

  const schedule = await getFrequencySettlementSchedule(
    settlementInfo.customerCustomerTypeId,
    settlementInfo.fromDate,
    settlementInfo.toDate,
    prisma
  );

  let settlementEndBalance = "0";
  let settlementCurrentBalance = "0";

  if (settlementInfo.stateName.toLowerCase() === skipped.toLowerCase()) {
    settlementEndBalance = await getSettlementLatestEndBalance(
      prisma,
      settlementInfo
    );

    try {
      const bluskyToken = await getAuthToken();
      const getEndBalanceBluSky = await getEndBalance(
        [settlementInfo.serviceNumber.toString()],
        bluskyToken
      );

      settlementCurrentBalance =
        getEndBalanceBluSky[
          settlementInfo.serviceNumber
        ]?.currentBalance?.toString() ?? notFound;
    } catch {
      throw new Error("Failed to fetch end balance from BluSky.");
    }
  }

  if (
    settlementInfo.stateName.toLowerCase() === error.toLowerCase() ||
    settlementInfo.stateName.toLowerCase() === processing.toLowerCase() ||
    settlementInfo.stateName.toLowerCase() === approvalError.toLowerCase()
  ) {
    settlementEndBalance = na;
    settlementCurrentBalance = na;
  }

  const customerSettlementSummary: SettlementSummary = {
    id: settlementInfo.customerSettlementGenerationScheduleId.toString(),
    customerName: settlementInfo.customerName,
    customerCustomerTypeId: settlementInfo.customerTypeId,
    customerId: settlementInfo.customerId.toString(),
    serviceNumber: settlementInfo.serviceNumber.toString(),
    customerType: customerType?.customerTypeName ?? unknown,
    status: settlementInfo.stateName as SettlementState,
    fromDate: format(settlementInfo.fromDate, "yyyy-MM-dd"),
    toDate: format(settlementInfo.toDate, "yyyy-MM-dd"),
    endBalance: settlementEndBalance,
    currentBalance: settlementCurrentBalance,
    platformSettlements: {},
    error: schedule?.errorMessage,
    emailId: undefined,
    deletedAt: undefined,
    settlementFolderLocation: undefined,
  };

  return customerSettlementSummary;
}
