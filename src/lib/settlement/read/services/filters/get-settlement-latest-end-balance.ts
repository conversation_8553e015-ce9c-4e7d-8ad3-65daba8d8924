import { getSettlementLatestEndBalanceAmount } from "@lib/settlement/repository/get-settlement-latest-end-balance-amount";
import { type Prisma } from "@prisma/client";

import { type LatestSettlements } from "../../types";

export async function getSettlementLatestEndBalance(
  prisma: Prisma.TransactionClient,
  settlementInfo: LatestSettlements
): Promise<string> {
  return getSettlementLatestEndBalanceAmount(prisma, settlementInfo);
}
