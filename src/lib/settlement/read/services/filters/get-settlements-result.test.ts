import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi } from "vitest";

import { loadSettlements } from "./get-settlements-result";
import { type LatestSettlements } from "../../types";
import { groupSettlementData } from "../group-settlement-data";

import type { Mock } from "vitest";

// Mock groupSettlementData
vi.mock("../group-settlement-data", () => ({
  groupSettlementData: vi.fn(),
}));

const mockPrisma = {} as unknown as PrismaClient;

const settlementInput: LatestSettlements[] = [
  {
    customerId: 1,
    customerCustomerTypeId: 1,
    fromDate: new Date("2023-01-01"),
    toDate: new Date("2023-01-31"),
    customerSettlementGenerationScheduleId: 0,
    serviceNumber: 0,
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    customerTypeId: 0,
    statementFrequencyId: 0,
    customerTypeName: "",
    customerName: "",
    stateName: "",
    status: "",
    isAdjusted: false,
  },
  {
    customerId: 2,
    customerCustomerTypeId: 2,
    fromDate: new Date("2023-01-01"),
    toDate: new Date("2023-01-31"),
    customerSettlementGenerationScheduleId: 0,
    serviceNumber: 0,
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    customerTypeId: 0,
    statementFrequencyId: 0,
    customerTypeName: "",
    customerName: "",
    stateName: "",
    status: "",
    isAdjusted: false,
  },
];

describe("loadSettlements", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it("returns empty array when input is empty", async () => {
    const result = await loadSettlements(mockPrisma, []);
    expect(result).toEqual([]);
    expect(groupSettlementData).not.toHaveBeenCalled();
  });
  it("calls groupSettlementData for each settlement item", async () => {
    (groupSettlementData as Mock).mockImplementation(
      async (_prisma: PrismaClient, settlement: LatestSettlements) => {
        return {
          summaryFor: settlement.customerId,
        };
      }
    );

    const result = await loadSettlements(mockPrisma, settlementInput);

    expect(groupSettlementData).toHaveBeenCalledTimes(2);
    expect(result).toEqual([{ summaryFor: 1 }, { summaryFor: 2 }]);
  });

  it("returns all resolved results in correct order", async () => {
    (groupSettlementData as Mock).mockResolvedValueOnce({ value: 1 });
    (groupSettlementData as Mock).mockResolvedValueOnce({ value: 2 });

    const result = await loadSettlements(mockPrisma, settlementInput);

    expect(result).toEqual([{ value: 1 }, { value: 2 }]);
  });

  it("propagates error if groupSettlementData fails", async () => {
    (groupSettlementData as Mock).mockRejectedValueOnce(
      new Error("Processing failed")
    );

    await expect(loadSettlements(mockPrisma, settlementInput)).rejects.toThrow(
      "Processing failed"
    );
  });

  it("handles groupSettlementData returning unexpected shape", async () => {
    (groupSettlementData as Mock).mockResolvedValueOnce(`undefined`);
    (groupSettlementData as Mock).mockResolvedValueOnce({});

    const result = await loadSettlements(mockPrisma, settlementInput);

    expect(result).toEqual([`undefined`, {}]);
  });
});
