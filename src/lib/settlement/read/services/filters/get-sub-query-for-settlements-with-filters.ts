import { Prisma } from "@prisma/client";

export function getSubQueryForSettlementsWithFilters(
  whereConditions: Prisma.Sql[],
  joinConditions: Prisma.Sql[],
  havingConditions: Prisma.Sql[]
) {
  const includeWhere = whereConditions.length > 0;
  const includeJoin = joinConditions.length > 0;
  const includeHaving = havingConditions.length > 0;

  return Prisma.sql`
          SELECT s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          FROM customerSettlements s
          JOIN customer c ON s.customerId = c.customerId
          JOIN customerCustomerType cct ON s.customerCustomerTypeId = cct.customerCustomerTypeId
          JOIN customerType ct ON cct.customerTypeId = ct.customerTypeId
          ${includeJoin ? Prisma.join(joinConditions, `\n`) : Prisma.empty}
          WHERE ${
            includeWhere ? Prisma.join(whereConditions, ` AND `) : Prisma.empty
          }${
            includeWhere ? Prisma.sql` AND` : Prisma.empty
          } s.deletedAt IS NULL 
          GROUP BY s.customerId, s.fromDate, s.toDate, ct.customerTypeId
          ${includeHaving ? Prisma.sql`HAVING` : Prisma.empty} ${
            includeHaving
              ? Prisma.join(havingConditions, ` AND `)
              : Prisma.empty
          }`;
}
