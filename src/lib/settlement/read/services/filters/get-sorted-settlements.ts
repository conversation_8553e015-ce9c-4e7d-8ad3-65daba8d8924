import {
  ascending,
  type descending,
} from "@lib/settlement/constants/default-parameters";

import { SettlementsOrderEnum, type LatestSettlements } from "../../types";

// eslint-disable-next-line max-params
export function getSortedSettlements(
  settlements: LatestSettlements[],
  sortKey: string,
  sortOrder: typeof ascending | typeof descending,
  limit: number,
  offset: number
): LatestSettlements[] {
  if (settlements.length === 0) {
    return [];
  }

  switch (sortKey) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    case SettlementsOrderEnum.FROM_DATE: {
      const sorted = settlements.sort((a, b) =>
        sortOrder === ascending
          ? a.fromDate.getTime() - b.fromDate.getTime()
          : b.fromDate.getTime() - a.fromDate.getTime()
      );

      return sorted.slice(offset, offset + limit);
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    case SettlementsOrderEnum.TO_DATE: {
      const sorted = settlements.sort((a, b) =>
        sortOrder === ascending
          ? a.toDate.getTime() - b.toDate.getTime()
          : b.toDate.getTime() - a.toDate.getTime()
      );

      return sorted.slice(offset, offset + limit);
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    case SettlementsOrderEnum.CLIENT_NAME: {
      const sorted = settlements.sort((a, b) =>
        sortOrder === ascending
          ? a.customerName.localeCompare(b.customerName)
          : b.customerName.localeCompare(a.customerName)
      );

      return sorted.slice(offset, offset + limit);
    }

    default: {
      throw new Error("Invalid sort key provided");
    }
  }
}
