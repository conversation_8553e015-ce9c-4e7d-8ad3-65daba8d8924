import { getFrequencySettlementSchedule } from "@lib/settlement/repository/settlement-frequency";
import { type Prisma } from "@prisma/client";
import { format } from "date-fns";
import { describe, it, expect, vi, beforeEach } from "vitest";

import { getSettlementLatestEndBalance } from "./get-settlement-latest-end-balance";
import { getSkippedAndErrorSettlementInfo } from "./get-skipped-error-settlement-info";
import { getAuthToken } from "../../../../../services/blusky/authentication";
import { getEndBalance } from "../../../../../services/blusky/end-balance";
import { type LatestSettlements } from "../../types";

vi.mock("@lib/settlement/repository/settlement-frequency", () => ({
  getFrequencySettlementSchedule: vi.fn(),
}));

vi.mock("./get-settlement-latest-end-balance", () => ({
  getSettlementLatestEndBalance: vi.fn(),
}));

vi.mock("../../../../../services/blusky/authentication", () => ({
  getAuthToken: vi.fn(),
}));

vi.mock("../../../../../services/blusky/end-balance", () => ({
  getEndBalance: vi.fn(),
}));

const mockPrisma = {
  customerType: {
    findUnique: vi.fn(),
  },
} as unknown as Prisma.TransactionClient;

const baseSettlementInfo: LatestSettlements = {
  customerSettlementGenerationScheduleId: 1,
  customerName: "Test Customer",
  customerCustomerTypeId: 123,
  customerTypeId: 456,
  customerId: 100,
  serviceNumber: 200,
  stateName: "Error",
  fromDate: new Date("2023-01-01"),
  toDate: new Date("2023-01-30"),
  updatedAt: new Date(),
  generationType: "",
  generationStatus: "",
  statementFrequencyId: 0,
  customerTypeName: "",
  status: "",
  isAdjusted: false,
};
describe("getSkippedAndErrorSettlementInfo", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns full summary for non-skipped settlement", async () => {
    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      scheduleId: 1,
      generationType: "INITIAL",
      generationStatus: "STARTED",
      errorMessage: "",
    });

    vi.mocked(getSettlementLatestEndBalance).mockResolvedValue("123.45");

    vi.mocked(getAuthToken).mockResolvedValue("mock-token");
    baseSettlementInfo.stateName = "Approval Error";
    const serviceNumber200 = "200";
    vi.mocked(getEndBalance).mockResolvedValue({
      [serviceNumber200]: {
        customerName: "Test Customer",
        endBalance: 456.78,
        currentBalance: 456.78,
        totalBalance: 456.78,
      },
    });

    const result = await getSkippedAndErrorSettlementInfo(
      mockPrisma,
      baseSettlementInfo
    );

    expect(result).toEqual({
      id: "1",
      customerName: "Test Customer",
      customerCustomerTypeId: 456,
      customerId: "100",
      serviceNumber: "200",
      customerType: "Unknown",
      currentBalance: "NA",
      status: "Approval Error",
      fromDate: format(baseSettlementInfo.fromDate, "yyyy-MM-dd"),
      toDate: format(baseSettlementInfo.toDate, "yyyy-MM-dd"),
      endBalance: "NA",
      platformSettlements: {},
      error: "",
      emailId: undefined,
      deletedAt: undefined,
      settlementFolderLocation: undefined,
    });
  });

  it("returns default values when customerType is null", async () => {
    vi.mocked(mockPrisma.customerType.findUnique).mockResolvedValue(null);

    // eslint-disable-next-line unicorn/no-useless-undefined
    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue(undefined);

    vi.mocked(getSettlementLatestEndBalance).mockResolvedValue("0");

    vi.mocked(getAuthToken).mockResolvedValue("mock-token");

    const serviceNumber200 = "200";
    vi.mocked(getEndBalance).mockResolvedValue({
      [serviceNumber200]: {
        customerName: "Test Customer",
        endBalance: 50,
        currentBalance: 50,
        totalBalance: 456.78,
      },
    });

    const result = await getSkippedAndErrorSettlementInfo(
      mockPrisma,
      baseSettlementInfo
    );

    expect(result.customerType).toBe("Unknown");
    expect(result.error).toBeUndefined();
  });

  it("uses 0 for currentBalance if not found", async () => {
    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      scheduleId: 1,
      generationType: "INITIAL",
      generationStatus: "STARTED",
      errorMessage: "",
    });
    baseSettlementInfo.stateName = "Skipped";
    vi.mocked(getSettlementLatestEndBalance).mockResolvedValue("123.45");

    vi.mocked(getAuthToken).mockResolvedValue("mock-token");

    const serviceNumber200 = "200";
    vi.mocked(getEndBalance).mockResolvedValue({
      [serviceNumber200]: {
        customerName: "Test Customer",
        currentBalance: undefined,
        endBalance: 10,
        totalBalance: 10,
      },
    });

    const result = await getSkippedAndErrorSettlementInfo(
      mockPrisma,
      baseSettlementInfo
    );

    expect(result.currentBalance).toBe("Not Found");
  });

  it("skips getSettlementLatestEndBalance if state is Skipped", async () => {
    baseSettlementInfo.status = "Skipped";
    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      scheduleId: 1,
      generationType: "INITIAL",
      generationStatus: "SKIPPED",
      errorMessage: "",
    });

    vi.mocked(getSettlementLatestEndBalance).mockResolvedValue("123.45");

    vi.mocked(getAuthToken).mockResolvedValue("mock-token");

    const serviceNumber200 = "200";
    vi.mocked(getEndBalance).mockResolvedValue({
      [serviceNumber200]: {
        customerName: "Test Customer",
        endBalance: 456.78,
        currentBalance: 456.78,
        totalBalance: 456.78,
      },
    });
    baseSettlementInfo.stateName = "Error";

    await getSkippedAndErrorSettlementInfo(mockPrisma, baseSettlementInfo);

    expect(getSettlementLatestEndBalance).not.toHaveBeenCalled();
  });
});
