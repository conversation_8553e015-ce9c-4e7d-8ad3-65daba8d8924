import { Prisma } from "@prisma/client";

import { stateStatusJoin } from "./constants";

type SettlementFilters = {
  nameOrServiceNumber: string | undefined;
  clientType: string | undefined;
  displayAdjusted: string | undefined;
  state: string | undefined;
  status: string | undefined;
  frequency: string | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
};

export const getFilterConditions = ({
  nameOrServiceNumber,
  clientType,
  displayAdjusted,
  state,
  status,
  frequency,
  startDate,
  endDate,
}: SettlementFilters) => {
  const joinConditions = [];
  const havingConditions = [];
  const whereConditions = [];

  if (nameOrServiceNumber) {
    whereConditions.push(
      Prisma.sql`(c.customerName LIKE ${`%${nameOrServiceNumber}%`} OR c.serviceNumber LIKE ${`%${nameOrServiceNumber}%`})`
    );
  }

  if (clientType) {
    whereConditions.push(Prisma.sql`(ct.customerTypeName = ${clientType})`);
  }

  if (displayAdjusted === "Adjustments Only") {
    whereConditions.push(Prisma.sql`(EXISTS (
    SELECT 1
    FROM customerSettlementAdjustments csa
    WHERE s.customerSettlementsId = csa.customerSettlementsId
      AND csa.amount > 0 AND csa.deletedAt IS NULL
))`);
  } else if (displayAdjusted === "No Adjustments") {
    joinConditions.push(
      Prisma.sql`LEFT JOIN customerSettlementAdjustments csa ON s.customerSettlementsId = csa.customerSettlementsId`
    );
    havingConditions.push(
      Prisma.sql`SUM(CASE WHEN csa.amount > 0 THEN 1 ELSE 0 END) = 0`
    );
  }

  if (state) {
    whereConditions.push(Prisma.sql`(ss.stateName = ${state})`);
    joinConditions.push(stateStatusJoin);
  } else if (status === "Settlement Approval") {
    whereConditions.push(Prisma.sql`(ss.stateName = 'Approval Pending' OR ss.stateName = 'Approval Processing' OR
      ss.stateName = 'Approval Success' OR ss.stateName = 'Approval Error')`);
    joinConditions.push(stateStatusJoin);
  } else if (status === "Settlement Generation") {
    whereConditions.push(
      Prisma.sql`(ss.stateName = 'Skipped' OR ss.stateName = 'Error' OR ss.stateName = 'Processing')`
    );
    joinConditions.push(stateStatusJoin);
  }

  if (frequency) {
    whereConditions.push(
      Prisma.sql`(sf.statementFrequencyName = ${frequency})`
    );
    joinConditions.push(
      Prisma.sql`JOIN statementFrequency sf ON cct.statementFrequencyId = sf.statementFrequencyId`
    );
  }

  if (startDate && endDate) {
    whereConditions.push(
      Prisma.sql`(s.fromDate >= ${startDate} AND s.toDate <= ${endDate})`
    );
  }

  return { joinConditions, havingConditions, whereConditions };
};
