import { getAdjustedCustomerSettlements } from "@lib/settlement/repository/get-adjusted-customer-settlements";
import { type PrismaClient } from "@prisma/client";

import { type LatestSettlements } from "../../types";

export const setIsAdjustedOnSettlements = async (
  prisma: PrismaClient,
  settlements: LatestSettlements[]
): Promise<void> => {
  const adjustments = await getAdjustedCustomerSettlements(prisma);

  if (!adjustments || adjustments.length === 0) {
    // If there are no adjustments, we can skip the update
    for (const settlement of settlements) {
      settlement.isAdjusted = false;
    }

    return;
  }

  for (const adjustment of adjustments) {
    const settlement = settlements.find(
      (s) =>
        s.customerId === adjustment.customerId &&
        s.fromDate.getTime() === adjustment.fromDate.getTime() &&
        s.toDate.getTime() === adjustment.toDate.getTime() &&
        s.customerCustomerTypeId === adjustment.customerCustomerTypeId
    );

    if (settlement) {
      settlement.isAdjusted = true;
    }
  }

  for (const settlement of settlements) {
    if (settlement.isAdjusted === undefined) {
      settlement.isAdjusted = false;
    }
  }
};
