import {
  error,
  statusUnknown,
} from "@lib/settlement/constants/default-parameters";
import { getFrequencySettlementSchedule } from "@lib/settlement/repository/settlement-frequency";
import { getSettlementState } from "@lib/settlement/repository/settlement-state";
import { type SettlementState } from "@lib/settlement/repository/types";

import type { PrismaClient, Prisma } from "@prisma/client";

export const getSettlementStateAndMessage = async (
  settlement: {
    customerCustomerTypeId: number | undefined;
    fromDate: Date;
    toDate: Date;
  },
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<{ state: SettlementState; message?: string }> => {
  // There should be a customerCustomerTypeId for each settlement
  if (!settlement.customerCustomerTypeId) {
    return {
      state: error,
      message: "customerCustomerTypeId is missing from settlement",
    };
  }

  const schedule = await getFrequencySettlementSchedule(
    settlement.customerCustomerTypeId,
    settlement.fromDate,
    settlement.toDate,
    prisma
  );

  // There should be a corresponding schedule for each settlement
  if (!schedule) {
    return {
      state: statusUnknown,
      message: "No schedule found for settlement",
    };
  }

  let state = await getSettlementState(
    schedule.generationType,
    schedule.generationStatus,
    prisma
  );

  // If no corresponding state found
  state ||= statusUnknown;

  return {
    state,
    ...(schedule.errorMessage && {
      message: schedule.errorMessage,
    }),
  };
};
