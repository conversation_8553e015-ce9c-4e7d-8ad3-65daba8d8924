import { getGroupedCustomerSettlementsBySort } from "./get-grouped-customer-settlements-by-sort";

import type { Prisma } from "@prisma/client";

describe("getGroupedCustomerSettlementsBySort", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return grouped customer settlements sorted by fromDate in ascending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };

    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "fromDate",
      sortOrder: "asc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY s.fromDate ASC"
    );
  });

  it("should return grouped customer settlements sorted by fromDate in descending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };
    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "fromDate",
      sortOrder: "desc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY s.fromDate DESC"
    );
  });

  it("should return grouped customer settlements sorted by toDate in ascending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };
    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "toDate",
      sortOrder: "asc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY s.toDate ASC"
    );
  });

  it("should return grouped customer settlements sorted by toDate in descending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };

    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "toDate",
      sortOrder: "desc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY s.toDate DESC"
    );
  });

  it("should return grouped customer settlements sorted by client name in ascending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };
    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "clientName",
      sortOrder: "asc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY c.customerName ASC"
    );
  });

  it("should return grouped customer settlements sorted by client name in descending order", async () => {
    const mockTx = {
      $queryRaw: vi.fn(),
    };
    await getGroupedCustomerSettlementsBySort({
      // @ts-expect-error not testing types in mocks
      tx: mockTx,
      joinConditions: [],
      whereConditions: [],
      havingConditions: [],
      offset: 0,
      limit: 10,
      sortKey: "clientName",
      sortOrder: "desc",
    });

    expect(mockTx.$queryRaw).toHaveBeenCalledTimes(1);
    expect(mockTx.$queryRaw.mock.calls[0]?.[0].sql).toContain(
      "ORDER BY c.customerName DESC"
    );
  });

  it("should throw an error for invalid sort option", async () => {
    const mockTx = {
      customerSettlements: { groupBy: vi.fn().mockResolvedValue([]) },
    } as unknown as Prisma.TransactionClient;
    await expect(
      getGroupedCustomerSettlementsBySort({
        tx: mockTx,
        joinConditions: [],
        whereConditions: [],
        havingConditions: [],
        offset: 0,
        limit: 10,
        sortKey: "invalid",
        sortOrder: "desc",
      })
    ).rejects.toThrow("Invalid sort value provided");
  });
});
