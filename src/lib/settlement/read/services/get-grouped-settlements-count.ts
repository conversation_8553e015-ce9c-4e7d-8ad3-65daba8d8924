import { Prisma } from "@prisma/client";

import { getSubQueryForSettlementsWithFilters } from "./filters/get-sub-query-for-settlements-with-filters";

type TotalCountType = Array<{
  totalCount: string;
}>;

export async function getGroupedSettlementsTotalCount(
  tx: Prisma.TransactionClient,
  whereConditions: Prisma.Sql[],
  joinConditions: Prisma.Sql[],
  havingConditions: Prisma.Sql[]
): Promise<number> {
  // We are doing a raw query here as we need a count of the
  // total results returned by the prisma groupedby query.
  // To count all the settlements grouped would be storing a lot of data on one
  // variable and therefore we are using a raw query to do the total count
  // once to be efficient.

  const totalCountResult: TotalCountType =
    await tx.$queryRaw<TotalCountType>(Prisma.sql`
    SELECT COUNT(*) as totalCount
    FROM(
      ${getSubQueryForSettlementsWithFilters(
        whereConditions,
        joinConditions,
        havingConditions
      )}
    ) as grouped
    `);

  let totalCount = -1;

  if (totalCountResult.length > 0) {
    totalCount = Number(totalCountResult[0]?.totalCount);
  } else {
    throw new Error("No total count of records found");
  }

  if (Number.isNaN(totalCount)) {
    throw new TypeError("Total count of records is NaN");
  }

  return totalCount;
}
