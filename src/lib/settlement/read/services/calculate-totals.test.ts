/* eslint-disable @typescript-eslint/naming-convention */

import {
  calculatePayouts,
  calculateTotalCosts,
  calculateTotalPayable,
  calculateTotalAdjustments,
} from "./calculate-totals";

import type {
  PlatformCode,
  SettlementDetails,
} from "@lib/settlement/repository/types";

describe("calculatePayouts", () => {
  it("should calculate payouts correctly for merchant", async () => {
    const settlements = [
      {
        platformCode: "RFM" as PlatformCode,
        totalTransactionAmount: 1000,
        totalFailedAmount: 0,
        transactionFee: 25,
        salesFee: 15,
        minimumFeeTotal: 5,
        gatewayFee: 0,
        refundFee: 6,
        totalRefundAmount: 100,
        adjustments: [
          {
            id: 1,
            label: "adj1",
            amount: 33,
            displayCommentExcel: false,
            comment: "",
          },
          {
            id: 2,
            label: "adj2",
            amount: 12,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
      {
        platformCode: "RTO" as PlatformCode,
        totalTransactionAmount: 500,
        totalFailedAmount: 50,
        transactionFee: 5,
        salesFee: 10,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 3,
            label: "adj3",
            amount: 44.55,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
      {
        platformCode: "KYC" as PlatformCode,
        totalTransactionAmount: 400,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [],
      },
      {
        platformCode: "SUMMARY" as PlatformCode,
        totalTransactionAmount: 0,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 31,
            label: "summary",
            amount: -1000,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
    ];

    const result = await calculatePayouts("Merchant", settlements);

    expect(result.totalPayout).toBe(-105.55);
    expect(result.netPayout).toBe(894.45);
    expect(result.platformTotals.RFM).toEqual({
      totalPayable: 804,
      totalCosts: 151,
      totalAdjustments: 45,
    });
    expect(result.platformTotals.RTO).toEqual({
      totalPayable: 509.55,
      totalCosts: 15,
      totalAdjustments: 44.55,
    });
    expect(result.platformTotals.KYC).toEqual({
      totalPayable: 400,
      totalCosts: 0,
      totalAdjustments: 0,
    });
    expect(result.platformTotals.SUMMARY).toEqual({
      totalPayable: 0,
      totalCosts: 0,
      totalAdjustments: -1000,
    });
  });

  it("should calculate payouts correctly for non-merchant", async () => {
    const settlements = [
      {
        platformCode: "RFM" as PlatformCode,
        totalTransactionAmount: 1234,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 1,
            label: "adj1",
            amount: 10,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
      {
        platformCode: "RTO" as PlatformCode,
        totalTransactionAmount: 567,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 2,
            label: "adj2",
            amount: 20,
            displayCommentExcel: false,
            comment: "",
          },
          {
            id: 3,
            label: "adj3",
            amount: 30,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
      {
        platformCode: "SUMMARY" as PlatformCode,
        totalTransactionAmount: 0,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 31,
            label: "summary adj",
            amount: 50,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
    ];

    const result = await calculatePayouts("Integrator", settlements);

    expect(result.totalPayout).toBe(1841);
    expect(result.netPayout).toBe(1791);
    expect(result.platformTotals.RFM).toEqual({
      totalPayable: 1224,
      totalCosts: 0,
      totalAdjustments: 10,
    });
    expect(result.platformTotals.RTO).toEqual({
      totalPayable: 617,
      totalCosts: 0,
      totalAdjustments: 50,
    });
    expect(result.platformTotals.SUMMARY).toEqual({
      totalPayable: 0,
      totalCosts: 0,
      totalAdjustments: 50,
    });
  });

  it("should handle empty settlements array", async () => {
    const result = await calculatePayouts("Merchant", []);

    expect(result.totalPayout).toBe(0);
    expect(result.netPayout).toBe(0);
    expect(result.platformTotals).toEqual({});
  });

  it("should handle settlements with only SUMMARY", async () => {
    const settlements = [
      {
        platformCode: "SUMMARY" as PlatformCode,
        totalTransactionAmount: 0,
        totalFailedAmount: 0,
        transactionFee: 0,
        salesFee: 0,
        minimumFeeTotal: 0,
        gatewayFee: 0,
        refundFee: 0,
        totalRefundAmount: 0,
        adjustments: [
          {
            id: 4,
            label: "summary",
            amount: 7,
            displayCommentExcel: false,
            comment: "",
          },
        ],
      },
    ];

    const result = await calculatePayouts("Merchant", settlements);

    expect(result.totalPayout).toBe(0);
    expect(result.netPayout).toBe(-7);
    expect(result.platformTotals.SUMMARY).toEqual({
      totalPayable: 0,
      totalCosts: 0,
      totalAdjustments: 7,
    });
  });
});

describe("calculateTotalCosts", () => {
  it("should sum all cost fields correctly if they exist", () => {
    const details: SettlementDetails = {
      transactionCount: 2118,
      totalTransactionAmount: 1_018_254.93,
      refundCount: 1,
      totalRefundAmount: 1000,
      gatewayFee: 1000,
      transactionFee: 3388.8,
      salesFee: 1000,
      refundFee: 1000,
      totalFailedAmount: 400.22,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 1000,
      minimumFeeCount: 1,
      totalMinimumAmount: 1000,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 0,
      isAdjusted: false,
      totalAdjustments: 0,
      totalCosts: 0,
      adjustments: [],
    };

    expect(calculateTotalCosts(details)).toBe(8388.8);
  });

  it("should return 0 if no cost fields exist", () => {
    const details: SettlementDetails = {};

    expect(calculateTotalCosts(details)).toBe(0);
  });
});

describe("calculateTotalPayable", () => {
  it("should calculate payable correctly for pay-in platform", () => {
    const details: SettlementDetails = {
      transactionCount: 2118,
      totalTransactionAmount: 1_018_254.93,
      refundCount: 1,
      totalRefundAmount: 1000,
      gatewayFee: 1000,
      transactionFee: 3388.8,
      salesFee: 1000,
      refundFee: 1000,
      totalFailedAmount: 400.22,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 1000,
      minimumFeeCount: 1,
      totalMinimumAmount: 1000,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 0,
      isAdjusted: false,
      totalAdjustments: 0,
      totalCosts: 0,
      adjustments: [
        {
          id: 1,
          label: "Customer Service Fee",
          amount: 10,
          displayCommentExcel: false,
          comment: "",
        },
      ],
    };

    expect(calculateTotalPayable("RFM", details, 8388.8, 10)).toBe(
      1_009_856.13
    );
  });

  it("should calculate payable correctly for pay-out platform", () => {
    const details: SettlementDetails = {
      transactionCount: 2118,
      totalTransactionAmount: 1_018_254.93,
      refundCount: 1,
      totalRefundAmount: 1000,
      gatewayFee: 1000,
      transactionFee: 3388.8,
      salesFee: 1000,
      refundFee: 1000,
      totalFailedAmount: 400.22,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 1000,
      minimumFeeCount: 1,
      totalMinimumAmount: 1000,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 0,
      isAdjusted: false,
      totalAdjustments: 2000,
      totalCosts: 0,
      adjustments: [
        {
          id: 2,
          label: "Returned/Cancelled Payments",
          amount: 2000,
          displayCommentExcel: false,
          comment: "",
        },
      ],
    };

    expect(calculateTotalPayable("RTO", details, 8388.8, 2000)).toBe(
      1_028_243.51
    );
  });

  it("should calculate payable correctly for KYC platform", () => {
    const details: SettlementDetails = {
      transactionCount: 719,
      totalTransactionAmount: 1408.8,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: 0,
      salesFee: 0,
      refundFee: 0,
      totalFailedAmount: 0,
      endBalance: 0,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 0,
      isAdjusted: false,
      totalAdjustments: 100,
      totalCosts: 0,
      adjustments: [],
      kycDetails: {
        KY1: {
          transactionCount: 11,
          totalTransactionAmount: 231.22,
        },
        KY2: {
          transactionCount: 5,
          totalTransactionAmount: 12.54,
        },
        KY3: {
          transactionCount: 7,
          totalTransactionAmount: 4.76,
        },
        KY4: {
          transactionCount: 211,
          totalTransactionAmount: 335.49,
        },
        KY5: {
          transactionCount: 29,
          totalTransactionAmount: 34.98,
        },
        KY6: {
          transactionCount: 456,
          totalTransactionAmount: 789.81,
        },
      },
    };
    expect(calculateTotalPayable("KYC", details, 0, 100)).toBe(1508.8);
  });

  it("should return 0 for summary platform", () => {
    const details: SettlementDetails = {
      transactionCount: 0,
      totalTransactionAmount: 0,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 0,
      transactionFee: 0,
      salesFee: 0,
      refundFee: 0,
      totalFailedAmount: 0,
      endBalance: -593.07,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 0,
      isAdjusted: false,
      totalPayout: 0,
      totalAdjustments: 0,
      netPayout: 0,
      totalCosts: 0,
      adjustments: [],
    };
    expect(calculateTotalPayable("SUMMARY", details, 0, 1000)).toBe(0);
  });

  it("should handle empty details", () => {
    const details: SettlementDetails = {};
    expect(calculateTotalPayable("RFM", details, 0, 0)).toBe(0);
  });
});

describe("calculateTotalAdjustments", () => {
  it("should sum all adjustment amounts", () => {
    const adjustments = [
      {
        id: 1,
        label: "adjusting",
        amount: 10,
        displayCommentExcel: true,
        comment: "Test comment",
      },
      {
        id: 2,
        label: "another adjustment",
        amount: -5,
        displayCommentExcel: false,
        comment: "Another comment",
      },
      {
        id: 3,
        label: "final adjustment",
        amount: 3,
        displayCommentExcel: true,
        comment: "Yet another comment",
      },
    ];
    expect(calculateTotalAdjustments(adjustments)).toBe(8);
  });

  it("should return 0 for empty adjustments", () => {
    expect(calculateTotalAdjustments([])).toBe(0);
  });

  it("should handle single adjustment", () => {
    const adjustments = [
      {
        id: 1,
        label: "adjusting",
        amount: 10,
        displayCommentExcel: true,
        comment: "Test comment",
      },
    ];
    expect(calculateTotalAdjustments(adjustments)).toBe(10);
  });
});
