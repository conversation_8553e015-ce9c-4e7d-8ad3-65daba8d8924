import { getGroupedSettlementsTotalCount } from "./get-grouped-settlements-count";

describe("getGroupedSettlementsCount", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return the count of grouped settlements", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([{ totalCount: "5" }]),
    };
    // @ts-expect-error not testing types in mocks
    const result = await getGroupedSettlementsTotalCount(mockTx, [], [], []);

    expect(mockTx.$queryRaw).toHaveBeenCalledWith(expect.anything());
    expect(result).toEqual(5);
  });

  it("should throw an error if no total count is found", async () => {
    const mockTx = {
      $queryRaw: vi.fn().mockResolvedValue([]),
    };
    await expect(
      // @ts-expect-error not testing types in mocks
      getGroupedSettlementsTotalCount(mockTx, [], [], [])
    ).rejects.toThrow("No total count of records found");
  });

  it("should throw an error if total count is NaN", async () => {
    const mockTx = {
      $queryRaw: vi
        .fn()
        .mockResolvedValue([{ totalCount: "Not an applicable number" }]),
    };

    await expect(
      // @ts-expect-error not testing types in mocks
      getGroupedSettlementsTotalCount(mockTx, [], [], [])
    ).rejects.toThrow("Total count of records is NaN");
  });
});
