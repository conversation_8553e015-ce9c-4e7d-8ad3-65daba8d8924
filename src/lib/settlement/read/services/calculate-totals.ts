import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";

import type {
  AdjustmentDetails,
  PlatformCode,
  SettlementDetails,
} from "../../repository/types";

const calculatePayouts = async (
  customerType: string,
  settlements: Array<{
    platformCode: PlatformCode;
    totalTransactionAmount: number;
    totalFailedAmount: number;
    transactionFee: number;
    salesFee: number;
    minimumFeeTotal: number;
    gatewayFee: number;
    refundFee: number;
    totalRefundAmount: number;
    adjustments: AdjustmentDetails[];
  }>
): Promise<{
  totalPayout: number;
  netPayout: number;
  platformTotals: Partial<
    Record<
      PlatformCode,
      {
        totalPayable: number;
        totalCosts: number;
        totalAdjustments: number;
      }
    >
  >;
}> => {
  let totalPayout = 0;
  let totalSummaryAdjustments = 0;

  const platformTotals: Partial<
    Record<
      PlatformCode,
      {
        totalPayable: number;
        totalCosts: number;
        totalAdjustments: number;
      }
    >
  > = {};

  const promises = settlements.map(async (settlementDetails) => {
    const {
      platformCode,
      totalTransactionAmount,
      totalFailedAmount,
      transactionFee,
      salesFee,
      minimumFeeTotal,
      gatewayFee,
      refundFee,
      totalRefundAmount,
      adjustments,
    } = settlementDetails;

    const totalAdjustments = calculateTotalAdjustments(adjustments);

    if (platformCode === "SUMMARY") {
      totalSummaryAdjustments = totalAdjustments;

      platformTotals[platformCode] = {
        totalPayable: 0,
        totalCosts: 0,
        totalAdjustments,
      };

      return;
    }

    const totalCosts = calculateTotalCosts({
      transactionFee,
      salesFee,
      minimumFeeTotal,
      gatewayFee,
      refundFee,
      totalRefundAmount,
    });

    const totalPayable = calculateTotalPayable(
      platformCode,
      { totalTransactionAmount, totalFailedAmount },
      totalCosts,
      totalAdjustments
    );

    if (customerType === "Merchant") {
      // For merchants, we add the total payable of pay-ins and subtract the
      // total payable of pay-outs and KYC.
      if (payInGroup.includes(platformCode as Platform)) {
        totalPayout += totalPayable;
      } else if (
        payOutGroup.includes(platformCode as Platform) ||
        platformCode === "KYC"
      ) {
        totalPayout -= totalPayable;
      }
    } else {
      // For non-merchants, we sum up all total payables.
      totalPayout += totalPayable;
    }

    platformTotals[platformCode] = {
      totalPayable,
      totalCosts,
      totalAdjustments,
    };
  });

  await Promise.all(promises);

  return {
    totalPayout: Number(totalPayout.toFixed(2)),
    // Treat adjustments as costs for the customer.
    netPayout: Number((totalPayout - totalSummaryAdjustments).toFixed(2)),
    platformTotals,
  };
};

const calculateTotalCosts = (details: SettlementDetails): number => {
  const transactionFee = details.transactionFee ?? 0;
  const salesFee = details.salesFee ?? 0;
  const minimumFeeTotal = details.minimumFeeTotal ?? 0;
  const gatewayFee = details.gatewayFee ?? 0;
  const refundFee = details.refundFee ?? 0;
  const totalRefundAmount = details.totalRefundAmount ?? 0;

  const totalCosts =
    transactionFee +
    salesFee +
    minimumFeeTotal +
    gatewayFee +
    refundFee +
    totalRefundAmount;

  return Number(totalCosts.toFixed(2));
};

const calculateTotalPayable = (
  platformCode: PlatformCode,
  details: SettlementDetails,
  totalCosts: number,
  totalAdjustments: number
): number => {
  let totalPayable = 0;

  if (platformCode === "SUMMARY") {
    return totalPayable;
  }

  const totalTransactionAmount = details.totalTransactionAmount ?? 0;
  const totalFailedAmount = details.totalFailedAmount ?? 0;

  // For pay-in platforms, the total payable is the total transaction amount
  // subtract total costs and adjustments.
  // For pay-out platforms and KYC, the total payable is the total transaction amount
  // plus total costs and adjustments, and subtract failed amount.
  // We treat adjustments as costs for the customer.
  totalPayable = payInGroup.includes(platformCode as Platform)
    ? totalTransactionAmount - totalCosts - totalAdjustments
    : totalTransactionAmount +
      totalCosts +
      totalAdjustments -
      totalFailedAmount;

  return Number(totalPayable.toFixed(2));
};

const calculateTotalAdjustments = (
  adjustments: AdjustmentDetails[]
): number => {
  let totalAdjustments = 0;

  for (const adjustment of adjustments) {
    totalAdjustments += adjustment.amount;
  }

  return Number(totalAdjustments.toFixed(2));
};

export {
  calculatePayouts,
  calculateTotalCosts,
  calculateTotalPayable,
  calculateTotalAdjustments,
};
