import {
  gatewayFee,
  minimumFeeCount,
  minimumFeeTotal,
  notFound,
  partialReturnAmountRTO,
  partialReturnCountRTO,
  refundCount,
  refundFee,
  salesFee,
  total2FaRejectAmount,
  total2FaRejectCount,
  totalFailedAmount,
  totalMinimumAmount,
  totalRefundAmount,
  totalTransactionAmount,
  transactionCount,
  transactionFee,
  txnAmountRTO_R,
  txnCountETI_R1,
  unknown,
} from "@lib/settlement/constants/default-parameters";
import {
  type AdjustmentDetails,
  type KycDetails,
  type PlatformCode,
  type SettlementSummary,
} from "@lib/settlement/repository/types";
import { type Prisma } from "@prisma/client";
import { objectValuesAreNotNullOrZeroOrUndefined } from "@utils/object-values-are-not-null-or-zero-or-undefined";
import { format } from "date-fns";

import { calculatePayouts } from "./calculate-totals";
import { getSettlementFolderAndIfExists } from "./get-settlement-folder-and-if-exists";
import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";
import { getAuthToken } from "../../../../services/blusky/authentication";
import { getEndBalance } from "../../../../services/blusky/end-balance";
import {
  PlatformCodeEnum,
  SettlementStateEnum,
  type SettlementDetails,
} from "../types";

export async function getCustomerSettlementData(
  prisma: Prisma.TransactionClient,
  settlement: SettlementDetails
): Promise<SettlementSummary> {
  // Ensure settlement properties are properly typed
  const { customerId, fromDate, toDate } = settlement;

  const settlements = await prisma.customerSettlements.findMany({
    where: {
      customerId,
      fromDate,
      toDate,
    },
    select: {
      fromDate: true,
      toDate: true,
      customerSettlementsId: true,
      customerId: true,
      transactionCount: true,
      totalTransactionAmount: true,
      refundCount: true,
      totalRefundAmount: true,
      gatewayFee: true,
      transactionFee: true,
      salesFee: true,
      refundFee: true,
      totalFailedAmount: true,
      endBalance: true,
      total2FaRejectAmount: true,
      total2FaRejectCount: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnAmountRTO_R: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      txnCountETI_R1: true,
      minimumFeeTotal: true,
      minimumFeeCount: true,
      totalMinimumAmount: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnAmountRTO: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partialReturnCountRTO: true,
      platform: {
        select: {
          platformCode: true,
          settlementDescription: true,
        },
      },
      customerSettlementAdjustments: {
        select: {
          customerSettlementAdjustmentId: true,
          label: true,
          amount: true,
          displayCommentExcel: true,
          comment: true,
        },
        where: {
          deletedAt: null,
        },
      },
      customerSettlementKyc: {
        select: {
          kycType: {
            select: {
              kycName: true,
            },
          },
          transactionCount: true,
          totalTransactionAmount: true,
        },
      },
      customer: {
        select: {
          customerName: true,
          serviceNumber: true,
          enabled: true,
        },
      },
      customerCustomerType: {
        select: {
          customerCustomerTypeId: true,
          customerType: {
            select: {
              customerTypeName: true,
            },
          },
        },
      },
    },
  });

  const summary: SettlementSummary = {
    id: "",
    customerName: "",
    customerCustomerTypeId: 1,
    serviceNumber: "",
    customerType: "",
    status: SettlementStateEnum.SKIPPED,
    endBalance: "0",
    currentBalance: "0",
    fromDate: ``,
    toDate: ``,
    platformSettlements: {},
    settlementFolderLocation: undefined,
  };

  const bluSkyAuthToken = await getAuthToken();

  const settlementsServiceNumbers = settlements.map(
    (settlement) => settlement.customer.serviceNumber
  );

  const endBalanceData = await getEndBalance(
    settlementsServiceNumbers,
    bluSkyAuthToken
  );

  const processedSettlements = await Promise.all(
    // eslint-disable-next-line complexity
    settlements.map(async (settlement) => {
      const { platformCode } = settlement.platform;
      const customerTypeName =
        settlement.customerCustomerType?.customerType?.customerTypeName ??
        "Unknown";

      // Determine if adjustments or meaningful values exist
      const platformIsNotZero = objectValuesAreNotNullOrZeroOrUndefined(
        settlement,
        [
          transactionCount,
          totalTransactionAmount,
          refundCount,
          totalRefundAmount,
          gatewayFee,
          transactionFee,
          salesFee,
          refundFee,
          totalFailedAmount,
          total2FaRejectAmount,
          total2FaRejectCount,
          txnAmountRTO_R,
          txnCountETI_R1,
          minimumFeeTotal,
          minimumFeeCount,
          totalMinimumAmount,
          partialReturnAmountRTO,
          partialReturnCountRTO,
        ]
      );

      const adjustments = settlement.customerSettlementAdjustments.map(
        (
          adjustment: Prisma.customerSettlementAdjustmentsGetPayload<{
            select: {
              customerSettlementAdjustmentId: true;
              label: true;
              amount: true;
              displayCommentExcel: true;
              comment: true;
            };
          }>
        ) => mapAdjustment(adjustment)
      );

      if (platformCode === "SUMMARY") {
        const { fromDate, toDate } = settlement;
        const { state, message } = await getSettlementStateAndMessage(
          {
            customerCustomerTypeId:
              settlement.customerCustomerType?.customerCustomerTypeId,
            fromDate: settlement.fromDate,
            toDate: settlement.toDate,
          },
          prisma
        );

        const { settlementFolderLocation, settlementFolderExists } =
          await getSettlementFolderAndIfExists(
            settlement.customerCustomerType?.customerCustomerTypeId,
            prisma
          );

        let settlementCurrentBalance = "0";

        try {
          const bluskyToken = await getAuthToken();
          const getEndBalanceBluSky = await getEndBalance(
            [summary.serviceNumber.toString()],
            bluskyToken
          );

          settlementCurrentBalance =
            getEndBalanceBluSky[
              summary.serviceNumber
            ]?.currentBalance?.toString() ?? notFound;
        } catch {
          throw new Error("Failed to fetch BluSky balance:");
        }

        // Update summary fields
        summary.id = String(settlement.customerSettlementsId);
        summary.customerName = settlement.customer.customerName;
        summary.customerCustomerTypeId =
          settlement.customerCustomerType?.customerCustomerTypeId ?? 0;
        summary.serviceNumber = settlement.customer.serviceNumber;
        summary.customerType = customerTypeName;
        summary.endBalance = settlement.endBalance?.toString() ?? "0";
        summary.currentBalance = settlementCurrentBalance;
        summary.status = state;
        summary.fromDate = format(fromDate, "yyyy-MM-dd");
        summary.toDate = format(toDate, "yyyy-MM-dd");
        summary.settlementFolderLocation = settlementFolderExists
          ? settlementFolderLocation
          : undefined;

        if (message) {
          summary.error = message;
        }
      }

      summary.platformSettlements[platformCode as PlatformCode] = {
        isNonZero: platformIsNotZero,
        labelName: settlement.platform.settlementDescription ?? "",
        transactionCount: settlement.transactionCount ?? 0,
        totalTransactionAmount:
          settlement.totalTransactionAmount?.toNumber() ?? 0,
        refundCount: settlement.refundCount ?? 0,
        totalRefundAmount: settlement.totalRefundAmount?.toNumber() ?? 0,
        gatewayFee: settlement.gatewayFee?.toNumber() ?? 0,
        transactionFee: settlement.transactionFee?.toNumber() ?? 0,
        salesFee: settlement.salesFee?.toNumber() ?? 0,
        refundFee: settlement.refundFee?.toNumber() ?? 0,
        totalFailedAmount: settlement.totalFailedAmount?.toNumber() ?? 0,
        endBalance: Number(settlement.endBalance ?? 0),
        total2FaRejectAmount: settlement.total2FaRejectAmount?.toNumber() ?? 0,
        total2FaRejectCount: settlement.total2FaRejectCount ?? 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        txnAmountRTO_R: settlement.txnAmountRTO_R,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        txnCountETI_R1: settlement.txnCountETI_R1,
        minimumFeeTotal: settlement.minimumFeeTotal?.toNumber() ?? 0,
        minimumFeeCount: settlement.minimumFeeCount ?? 0,
        totalMinimumAmount: settlement.totalMinimumAmount?.toNumber() ?? 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        partialReturnAmountRTO: settlement.partialReturnAmountRTO,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        partialReturnCountRTO: settlement.partialReturnCountRTO,
        isAdjusted: settlement.customerSettlementAdjustments.some(
          (adjustment) => adjustment.amount?.toNumber() !== 0
        ),
        adjustments,
        ...(settlement.customerSettlementKyc.length > 0
          ? {
              kycDetails: Object.fromEntries(
                settlement.customerSettlementKyc.map((kyc) => [
                  kyc.kycType.kycName,
                  {
                    transactionCount: kyc.transactionCount ?? 0,
                    totalTransactionAmount:
                      kyc.totalTransactionAmount?.toNumber() ?? 0,
                  },
                ])
              ) as KycDetails,
            }
          : {}),
      };

      return {
        platformCode: platformCode as PlatformCode,
        totalTransactionAmount: Number(settlement.totalTransactionAmount),
        totalFailedAmount: Number(settlement.totalFailedAmount),
        transactionFee: Number(settlement.transactionFee),
        salesFee: Number(settlement.salesFee),
        minimumFeeTotal: Number(settlement.minimumFeeTotal),
        gatewayFee: Number(settlement.gatewayFee),
        refundFee: Number(settlement.refundFee),
        totalRefundAmount: Number(settlement.totalRefundAmount),
        adjustments: adjustments as AdjustmentDetails[],
        customerTypeName,
        isSummary: platformCode === PlatformCodeEnum.SUMMARY.toString(),
      };
    })
  );

  // Extract customerTypeName from SUMMARY settlement
  const summarySettlement = processedSettlements.find((s) => s.isSummary);
  const customerTypeName = summarySettlement?.customerTypeName ?? unknown;

  // Check if any of the non-summary settlements have non-zero values
  summary.platformSettlements.SUMMARY = {
    ...summary.platformSettlements.SUMMARY,
    isNonZero: Object.values(summary.platformSettlements).some(
      (platformValue) => platformValue.isNonZero
    ),
  };

  const { totalPayout, netPayout, platformTotals } = await calculatePayouts(
    customerTypeName,
    processedSettlements
  );

  for (const platformCode of Object.keys(
    summary.platformSettlements
  ) as PlatformCode[]) {
    const totals = platformTotals[platformCode];

    summary.platformSettlements[platformCode] = {
      ...summary.platformSettlements[platformCode],
      ...totals,
    };
  }

  summary.platformSettlements.SUMMARY.totalPayout = totalPayout;
  summary.platformSettlements.SUMMARY.netPayout = netPayout;
  summary.customerId = settlement.customerId.toString();

  summary.platformSettlements.SUMMARY.currentBalance =
    endBalanceData[summary.serviceNumber]?.currentBalance === null
      ? "Not Found"
      : endBalanceData[summary.serviceNumber]?.currentBalance?.toString();

  return summary;
}

const mapAdjustment = (
  adjustment: Prisma.customerSettlementAdjustmentsGetPayload<{
    select: {
      customerSettlementAdjustmentId: true;
      label: true;
      amount: true;
      displayCommentExcel: true;
      comment: true;
    };
  }>
) => ({
  id: adjustment.customerSettlementAdjustmentId,
  label: adjustment.label ?? "",
  amount: adjustment.amount ? adjustment.amount.toNumber() : 0,
  displayCommentExcel: adjustment.displayCommentExcel ?? false,
  comment: adjustment.comment ?? "",
});
