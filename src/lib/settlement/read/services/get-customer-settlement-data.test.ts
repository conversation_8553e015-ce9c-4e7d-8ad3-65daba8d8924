/* eslint-disable @typescript-eslint/naming-convention */
import { Prisma } from "@prisma/client";
import { vi } from "vitest";

import { getCustomerSettlementData } from "./get-customer-settlement-data";

vi.mock("./get-settlement-state-and-message", () => ({
  getSettlementStateAndMessage: vi.fn().mockReturnValue({
    state: "COMPLETED",
    message: "Settlement completed successfully",
  }),
}));
vi.mock("../../../../services/blusky/authentication", () => ({
  getAuthToken: vi.fn().mockReturnValue({
    string: "auth",
  }),
}));
vi.mock("./get-settlement-folder-and-if-exists", () => ({
  getSettlementFolderAndIfExists: vi.fn().mockResolvedValue({
    settlementFolderLocation: "/settlements/2023/01",
    settlementFolderExists: true,
  }),
}));

vi.mock("@lib/settlement/read/helpers/calculate-totals", () => ({
  calculatePayouts: vi.fn().mockReturnValue({
    totalPayout: 600,
    netPayout: 500,
    platformTotals: {
      PLATFORM_X: {
        totalPayable: 700,
      },
    },
  }),
}));
vi.mock("../../../../services/blusky/end-balance", () => ({
  getEndBalance: vi.fn().mockReturnValue({
    "1234567890": {
      customerName: "Test Customer",
      endBalance: 1000,
      currentBalance: 321,
      totalBalance: 1000,
    },
  }),
}));

describe("getCustomerSettlementData", () => {
  it("should correctly grab all settlement data", async () => {
    vi.resetModules();

    vi.doMock("../../../../services/blusky/end-balance", () => ({
      getEndBalance: vi.fn().mockReturnValue({
        "1234567890": {
          customerName: "Test Customer",
          endBalance: 1000,
          currentBalance: 321,
          totalBalance: 1000,
        },
      }),
    }));

    const mockPrisma = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([
          {
            customer: {
              customerName: "Test Customer",
              serviceNumber: "1234567890",
              enabled: true,
            },
            fromDate: new Date("2023-01-01"),
            toDate: new Date("2023-01-31"),
            customerSettlementsId: 1,
            customerId: 123,
            transactionCount: 10,
            currentBalance: 0,
            totalTransactionAmount: new Prisma.Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Prisma.Decimal(1000),
            gatewayFee: new Prisma.Decimal(1000),
            transactionFee: new Prisma.Decimal(1000),
            salesFee: new Prisma.Decimal(1000),
            refundFee: new Prisma.Decimal(1000),
            totalFailedAmount: new Prisma.Decimal(1000),
            total2FaRejectAmount: new Prisma.Decimal(1000),
            totalAdjustments: 100,
            totalCosts: 6000,
            total2FaRejectCount: 1,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Prisma.Decimal(1000),
            minimumFeeCount: 1,
            totalMinimumAmount: new Prisma.Decimal(1000),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "PLATFORM_X",
              settlementDescription: "Monthly Settlement",
            },
            customerSettlementAdjustments: [
              {
                customerSettlementAdjustmentId: 1,
                label: "Adjustment A",
                amount: new Prisma.Decimal(100),
                displayCommentExcel: true,
                comment: "Adjustment for January",
              },
            ],
            customerSettlementKyc: [
              {
                kycType: {
                  kycName: "KYC Type A",
                },
                transactionCount: 5,
                totalTransactionAmount: new Prisma.Decimal(500),
              },
            ],
          },
          {
            customer: {
              customerName: "Test Customer",
              serviceNumber: "1234567890",
              enabled: true,
            },
            fromDate: new Date("2023-01-01"),
            toDate: new Date("2023-01-31"),
            customerSettlementsId: 1,
            customerId: 123,
            transactionCount: 10,
            totalTransactionAmount: new Prisma.Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Prisma.Decimal(1000),
            gatewayFee: new Prisma.Decimal(1000),
            transactionFee: new Prisma.Decimal(1000),
            salesFee: new Prisma.Decimal(1000),
            refundFee: new Prisma.Decimal(1000),
            totalFailedAmount: new Prisma.Decimal(1000),
            endBalance: new Prisma.Decimal(1000),
            total2FaRejectAmount: new Prisma.Decimal(1000),
            total2FaRejectCount: 1,
            totalAdjustments: 100,
            totalCosts: 6000,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Prisma.Decimal(1000),
            minimumFeeCount: 1,
            totalMinimumAmount: new Prisma.Decimal(1000),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
              settlementDescription: "Monthly Settlement",
            },
            customerSettlementAdjustments: [
              {
                customerSettlementAdjustmentId: 1,
                label: "Adjustment A",
                amount: new Prisma.Decimal(100),
                displayCommentExcel: true,
                comment: "Adjustment for January",
              },
            ],
            customerSettlementKyc: [
              {
                kycType: {
                  kycName: "KYC Type A",
                },
                transactionCount: 5,
                totalTransactionAmount: new Prisma.Decimal(500),
              },
            ],
          },
        ]),
      },
    };

    const mockSettlementInfo = {
      customerId: 123,
      fromDate: new Date("2023-01-01"),
      toDate: new Date("2023-01-31"),
    };

    const result = await getCustomerSettlementData(
      mockPrisma as unknown as Prisma.TransactionClient,
      mockSettlementInfo
    );

    expect(result).toEqual({
      id: "1",
      customerId: "123",
      customerName: "Test Customer",
      customerCustomerTypeId: 0,
      serviceNumber: "1234567890",
      customerType: "Unknown",
      status: "COMPLETED",
      currentBalance: "Not Found",
      endBalance: "1000",
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      settlementFolderLocation: "/settlements/2023/01",
      error: "Settlement completed successfully",
      platformSettlements: {
        PLATFORM_X: {
          isNonZero: true,
          labelName: "Monthly Settlement",
          transactionCount: 10,
          totalTransactionAmount: 1000,
          refundCount: 2,
          totalRefundAmount: 1000,
          gatewayFee: 1000,
          transactionFee: 1000,
          salesFee: 1000,
          refundFee: 1000,
          totalFailedAmount: 1000,
          endBalance: 0,
          total2FaRejectAmount: 1000,
          total2FaRejectCount: 1,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          minimumFeeTotal: 1000,
          totalAdjustments: 100,
          totalCosts: 6000,
          minimumFeeCount: 1,
          totalMinimumAmount: 1000,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: true,
          adjustments: [
            {
              id: 1,
              label: "Adjustment A",
              amount: 100,
              displayCommentExcel: true,
              comment: "Adjustment for January",
            },
          ],
          kycDetails: {
            "KYC Type A": {
              transactionCount: 5,
              totalTransactionAmount: 500,
            },
          },
          totalPayable: 6100,
        },
        SUMMARY: {
          isNonZero: true,
          labelName: "Monthly Settlement",
          transactionCount: 10,
          totalTransactionAmount: 1000,
          refundCount: 2,
          totalRefundAmount: 1000,
          gatewayFee: 1000,
          transactionFee: 1000,
          salesFee: 1000,
          refundFee: 1000,
          totalFailedAmount: 1000,
          endBalance: 1000,
          currentBalance: "Not Found",
          total2FaRejectAmount: 1000,
          total2FaRejectCount: 1,
          totalAdjustments: 100,
          totalCosts: 0,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          minimumFeeTotal: 1000,
          minimumFeeCount: 1,
          totalMinimumAmount: 1000,
          partialReturnAmountRTO: 0,
          totalPayable: 0,
          partialReturnCountRTO: 0,
          isAdjusted: true,
          adjustments: [
            {
              id: 1,
              label: "Adjustment A",
              amount: 100,
              displayCommentExcel: true,
              comment: "Adjustment for January",
            },
          ],
          kycDetails: {
            "KYC Type A": {
              transactionCount: 5,
              totalTransactionAmount: 500,
            },
          },
          totalPayout: 6100,
          netPayout: 6000,
        },
      },
    });
  });

  it("should set currentBalance to 'Not Found' if endBalance is null", async () => {
    vi.mock("../../../../services/blusky/end-balance", () => ({
      getEndBalance: vi.fn().mockReturnValue({
        "1234567890": {
          customerName: "Test Customer",
          endBalance: null,
          currentBalance: null,
          totalBalance: null,
        },
      }),
    }));
    const mockPrisma = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([
          {
            customer: {
              customerName: "Test Customer",
              serviceNumber: "1234567890",
              enabled: true,
            },
            fromDate: new Date("2023-01-01"),
            toDate: new Date("2023-01-31"),
            customerSettlementsId: 1,
            customerId: 123,
            transactionCount: 10,
            totalTransactionAmount: new Prisma.Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Prisma.Decimal(1000),
            gatewayFee: new Prisma.Decimal(1000),
            transactionFee: new Prisma.Decimal(1000),
            salesFee: new Prisma.Decimal(1000),
            refundFee: new Prisma.Decimal(1000),
            totalFailedAmount: new Prisma.Decimal(1000),
            endBalance: null,
            total2FaRejectAmount: new Prisma.Decimal(1000),
            total2FaRejectCount: 1,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Prisma.Decimal(1000),
            minimumFeeCount: 1,
            totalMinimumAmount: new Prisma.Decimal(1000),
            totalPayable: 6100,
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
              settlementDescription: "Monthly Settlement",
            },
            customerSettlementAdjustments: [
              {
                customerSettlementAdjustmentId: 1,
                label: "Adjustment A",
                amount: new Prisma.Decimal(100),
                displayCommentExcel: true,
                comment: "Adjustment for January",
              },
            ],
            customerSettlementKyc: [
              {
                kycType: {
                  kycName: "KYC Type A",
                },
                transactionCount: 5,
                totalTransactionAmount: new Prisma.Decimal(500),
              },
            ],
          },
        ]),
      },
    };
    const mockSettlementInfo = {
      customerId: 123,
      fromDate: new Date("2023-01-01"),
      toDate: new Date("2023-01-31"),
    };

    const result = await getCustomerSettlementData(
      mockPrisma as unknown as Prisma.TransactionClient,
      mockSettlementInfo
    );

    expect(result.currentBalance).toBe("Not Found");
  });
});
