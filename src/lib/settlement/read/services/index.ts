import {
  type ascending,
  type descending,
} from "@lib/settlement/constants/default-parameters";
import { getAllLatestSettlements } from "@lib/settlement/repository/get-all-latest-settlements";
import { type SettlementSummary } from "@lib/settlement/repository/types";
import { type PrismaClient } from "@prisma/client";

import { applyFilterOnLatestSettlements } from "./filters/get-filtered-settlements";
import { loadSettlements } from "./filters/get-settlements-result";
import { getSortedSettlements } from "./filters/get-sorted-settlements";
import { type LatestSettlements } from "../types";

export async function showAllSettlements(
  prisma: PrismaClient,
  parameters: {
    offset: number;
    limit: number;
    sortKey: string;
    sortOrder: typeof ascending | typeof descending;
    nameOrServiceNumber: string | undefined;
    clientType: string | undefined;
    displayAdjusted: string | undefined;
    state: string | undefined;
    status: string | undefined;
    frequencyName: string;
    startDate: string | undefined;
    endDate: string | undefined;
  }
): Promise<{ totalCount: number; settlements: SettlementSummary[] }> {
  const allLatestSettlementsResult = await getAllLatestSettlements(prisma);

  const allLatestSettlements: LatestSettlements[] = Array.isArray(
    allLatestSettlementsResult
  )
    ? (allLatestSettlementsResult as LatestSettlements[])
    : [];

  const {
    offset,
    limit,
    sortKey,
    sortOrder,
    nameOrServiceNumber,
    clientType,
    displayAdjusted,
    state,
    status,
    frequencyName,
    startDate,
    endDate,
  } = parameters;

  const allFilteredSettlements = await applyFilterOnLatestSettlements(
    prisma,
    allLatestSettlements,
    {
      nameOrServiceNumber,
      clientType,
      displayAdjusted,
      state,
      status,
      frequencyName,
      startDate,
      endDate,
    }
  );

  const totalCount = allFilteredSettlements.length;

  // Sorting and paginating the filtered settlements.
  const orderedFilteredSettlements = getSortedSettlements(
    allFilteredSettlements,
    sortKey,
    sortOrder,
    limit,
    offset
  );

  const settlements = await loadSettlements(prisma, orderedFilteredSettlements);

  return {
    totalCount,
    settlements,
  };
}
