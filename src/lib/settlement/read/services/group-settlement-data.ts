import { type SettlementSummary } from "@lib/settlement/repository/types";
import { type Prisma } from "@prisma/client";

import { getSkippedAndErrorSettlementInfo } from "./filters/get-skipped-error-settlement-info";
import { getCustomerSettlementData } from "./get-customer-settlement-data";
import { type LatestSettlements } from "../types";

export async function groupSettlementData(
  prisma: Prisma.TransactionClient,
  settlementInfo: LatestSettlements
): Promise<SettlementSummary> {
  const exists = await prisma.customerSettlements.findFirst({
    where: {
      customerId: settlementInfo.customerId,
      customerCustomerTypeId: settlementInfo.customerCustomerTypeId,
      fromDate: settlementInfo.fromDate,
      toDate: settlementInfo.toDate,
    },
  });

  if (!exists) {
    const settlementSummary = await getSkippedAndErrorSettlementInfo(
      prisma,
      settlementInfo
    );

    return settlementSummary;
  }

  const settlementObject = {
    customerId: settlementInfo.customerId,
    fromDate: settlementInfo.fromDate,
    toDate: settlementInfo.toDate,
  };

  return getCustomerSettlementData(prisma, settlementObject);
}
