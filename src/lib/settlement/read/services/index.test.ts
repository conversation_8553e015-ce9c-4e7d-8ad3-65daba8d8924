import { getAllLatestSettlements } from "@lib/settlement/repository/get-all-latest-settlements";
import { describe, it, expect, vi } from "vitest";

import { applyFilterOnLatestSettlements } from "./filters/get-filtered-settlements";
import { loadSettlements } from "./filters/get-settlements-result";
import { getSortedSettlements } from "./filters/get-sorted-settlements";
import { SettlementStateEnum, type LatestSettlements } from "../types";

import type { SettlementSummary } from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

import { showAllSettlements } from ".";

// Mock dependencies
vi.mock("../../repository/get-all-latest-settlements", () => ({
  getAllLatestSettlements: vi.fn(),
}));

vi.mock("./filters/get-filtered-settlements", () => ({
  applyFilterOnLatestSettlements: vi.fn(),
}));

vi.mock("./filters/get-sorted-settlements", () => ({
  getSortedSettlements: vi.fn(),
}));

vi.mock("./filters/get-settlements-result", () => ({
  loadSettlements: vi.fn(),
}));

describe("showAllSettlements", () => {
  const mockPrisma = {
    customerSettlements: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
    },
    $transaction: vi.fn(),
  } as unknown as PrismaClient;

  const parameters = {
    offset: 0,
    limit: 10,
    sortKey: "fromDate",
    sortOrder: "asc" as const,
    nameOrServiceNumber: "",
    clientType: "",
    displayAdjusted: "",
    state: "",
    status: "",
    frequency: "",
    startDate: "",
    endDate: "",
  };

  const mockLatestSettlements: LatestSettlements[] = [
    {
      customerId: 1,
      customerCustomerTypeId: 2,
      fromDate: new Date("2023-01-01"),
      toDate: new Date("2023-01-31"),
      customerSettlementGenerationScheduleId: 0,
      serviceNumber: 0,
      updatedAt: new Date(),
      generationType: "",
      generationStatus: "",
      customerTypeId: 0,
      statementFrequencyId: 0,
      customerTypeName: "",
      customerName: "",
      isAdjusted: false,
      stateName: "",
      status: SettlementStateEnum.APPROVAL_SUCCESS,
    },
  ];

  const mockSettlements: SettlementSummary[] = [
    {
      id: "abc123",
      customerName: "Test Merchant",
      serviceNumber: "999",
      customerType: "Merchant",
      status: SettlementStateEnum.APPROVAL_SUCCESS,
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      currentBalance: "1000",
      endBalance: "800",
      platformSettlements: {},
      customerCustomerTypeId: 2,
      settlementFolderLocation: undefined,
    },
  ];

  it("should return filtered, sorted, and paginated settlement summaries", async () => {
    vi.mocked(getAllLatestSettlements).mockResolvedValue(mockLatestSettlements);
    vi.mocked(applyFilterOnLatestSettlements).mockResolvedValue(
      mockLatestSettlements
    );
    vi.mocked(getSortedSettlements).mockReturnValue(mockLatestSettlements);
    vi.mocked(loadSettlements).mockResolvedValue(mockSettlements);

    const result = await showAllSettlements(mockPrisma, parameters);

    expect(result.totalCount).toBe(1);
    expect(result.settlements).toEqual(mockSettlements);
  });

  it("should handle non-array result from getAllLatestSettlements gracefully", async () => {
    vi.mocked(getAllLatestSettlements).mockResolvedValue(null);
    vi.mocked(applyFilterOnLatestSettlements).mockResolvedValue([]);
    vi.mocked(getSortedSettlements).mockReturnValue([]);
    vi.mocked(loadSettlements).mockResolvedValue([]);

    const result = await showAllSettlements(mockPrisma, parameters);

    expect(result.totalCount).toBe(0);
    expect(result.settlements).toEqual([]);
  });

  it("should return zero results if filters eliminate all entries", async () => {
    vi.mocked(getAllLatestSettlements).mockResolvedValue(mockLatestSettlements);
    vi.mocked(applyFilterOnLatestSettlements).mockResolvedValue([]);
    vi.mocked(getSortedSettlements).mockReturnValue([]);
    vi.mocked(loadSettlements).mockResolvedValue([]);

    const result = await showAllSettlements(mockPrisma, parameters);

    expect(result.totalCount).toBe(0);
    expect(result.settlements).toEqual([]);
  });

  it("should return empty settlements if loadSettlements returns nothing", async () => {
    vi.mocked(getAllLatestSettlements).mockResolvedValue(mockLatestSettlements);
    vi.mocked(applyFilterOnLatestSettlements).mockResolvedValue([]);
    vi.mocked(getSortedSettlements).mockReturnValue([]);
    vi.mocked(loadSettlements).mockResolvedValue([]);

    const result = await showAllSettlements(mockPrisma, parameters);

    expect(result.totalCount).toBe(0);
    expect(result.settlements).toEqual([]);
  });
});
