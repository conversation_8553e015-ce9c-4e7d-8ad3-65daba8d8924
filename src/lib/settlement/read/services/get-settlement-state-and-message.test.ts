import { getFrequencySettlementSchedule } from "@lib/settlement/repository/settlement-frequency";
import { getSettlementState } from "@lib/settlement/repository/settlement-state";
import { type Prisma } from "@prisma/client";

import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";

vi.mock("@lib/settlement/repository/settlement-frequency", () => {
  return {
    getFrequencySettlementSchedule: vi.fn(),
  };
});
vi.mock("@lib/settlement/repository/settlement-state", () => {
  return {
    getSettlementState: vi.fn(),
  };
});

describe("getSettlementStateAndMessage", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  const tx = {} as unknown as Prisma.TransactionClient;

  it("should return an error state if customerCustomerTypeId is missing", async () => {
    const settlement = {
      customerCustomerTypeId: undefined,
      fromDate: new Date(),
      toDate: new Date(),
    };

    const result = await getSettlementStateAndMessage(settlement, tx);

    expect(result).toEqual({
      state: "Error",
      message: "customerCustomerTypeId is missing from settlement",
    });
  });

  it("should return an error state if no schedule is found", async () => {
    const settlement = {
      customerCustomerTypeId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    };

    const result = await getSettlementStateAndMessage(settlement, tx);

    expect(result).toEqual({
      state: "Status Unknown",
      message: "No schedule found for settlement",
    });
  });

  it("should return the correct state if schedule and state are found", async () => {
    const generationType = "INITIAL";
    const generationStatus = "COMPLETE";
    const state = "Approval Pending";

    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      scheduleId: 1,
      generationType,
      generationStatus,
    });

    vi.mocked(getSettlementState).mockResolvedValue(state);

    const settlement = {
      customerCustomerTypeId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    };

    const result = await getSettlementStateAndMessage(settlement, tx);

    expect(result).toEqual({
      state,
    });
  });

  it("should return the state and message if both are found", async () => {
    const generationType = "FINAL";
    const generationStatus = "ERROR";
    const errorMessage = "Some error occurred";
    const state = "Approval Error";

    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      scheduleId: 1,
      generationType,
      generationStatus,
      errorMessage,
    });

    vi.mocked(getSettlementState).mockResolvedValue(state);

    const settlement = {
      customerCustomerTypeId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    };

    const result = await getSettlementStateAndMessage(settlement, tx);

    expect(result).toEqual({
      state,
      message: errorMessage,
    });
  });

  it("should return 'Status Unknown' if no state is found", async () => {
    const generationType = "FINAL";
    const generationStatus = "FAILED";
    const errorMessage = "Some error occurred";

    vi.mocked(getFrequencySettlementSchedule).mockResolvedValue({
      generationType,
      // @ts-expect-error - testing unknown status type
      generationStatus,
      errorMessage,
    });

    const settlement = {
      customerCustomerTypeId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    };

    const result = await getSettlementStateAndMessage(settlement, tx);

    expect(result).toEqual({
      state: "Status Unknown",
      message: errorMessage,
    });
  });
});
