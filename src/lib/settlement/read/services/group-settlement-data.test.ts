import { getSkippedAndErrorSettlementInfo } from "./filters/get-skipped-error-settlement-info";
import { getCustomerSettlementData } from "./get-customer-settlement-data";
import { groupSettlementData } from "./group-settlement-data";
import { SettlementStateEnum, type LatestSettlements } from "../types";

import type { SettlementSummary } from "@lib/settlement/repository/types";
import type { Prisma } from "@prisma/client";

vi.mock("./filters/get-skipped-error-settlement-info", () => ({
  getSkippedAndErrorSettlementInfo: vi.fn(),
}));

vi.mock("./get-customer-settlement-data", () => ({
  getCustomerSettlementData: vi.fn(),
}));

describe("groupSettlementData", () => {
  const mockPrisma = {
    customerSettlements: {
      findFirst: vi.fn(),
    },
  } as unknown as Prisma.TransactionClient;

  const mockSettlementInfo: LatestSettlements = {
    customerId: 1,
    customerCustomerTypeId: 2,
    fromDate: new Date("2023-01-01"),
    toDate: new Date("2023-01-31"),
    customerSettlementGenerationScheduleId: 0,
    serviceNumber: 0,
    updatedAt: new Date(),
    generationType: "",
    generationStatus: "",
    customerTypeId: 0,
    statementFrequencyId: 0,
    customerTypeName: "",
    customerName: "",
    isAdjusted: false,
    stateName: "",
    status: "",
  };

  it("should return skipped/error settlement if no existing settlement is found in customerSettlement table", async () => {
    mockPrisma.customerSettlements.findFirst = vi.fn().mockResolvedValue(null);

    const mockSkippedSummary: SettlementSummary = {
      id: "1",
      customerName: "Skipped Customer",
      serviceNumber: "9999",
      customerType: "Unknown",
      status: SettlementStateEnum.SKIPPED,
      fromDate: "2023-01-01T00:00:00.000Z",
      toDate: "2023-01-31T00:00:00.000Z",
      endBalance: "0",
      currentBalance: "0",
      customerCustomerTypeId: 2,
      platformSettlements: {},
      error: "error",
      emailId: "email",
      deletedAt: "",
      settlementFolderLocation: undefined,
    };

    vi.mocked(getSkippedAndErrorSettlementInfo).mockResolvedValue(
      mockSkippedSummary
    );

    const result = await groupSettlementData(mockPrisma, mockSettlementInfo);

    expect(mockPrisma.customerSettlements.findFirst).toHaveBeenCalled();
    expect(getSkippedAndErrorSettlementInfo).toHaveBeenCalledWith(
      mockPrisma,
      mockSettlementInfo
    );
    expect(result).toEqual(mockSkippedSummary);
  });

  it("should return customer settlement data if existing settlement is found", async () => {
    mockPrisma.customerSettlements.findFirst = vi
      .fn()
      .mockResolvedValue({ id: 42 });

    const mockCustomerData: SettlementSummary = {
      id: "2",
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Merchant",
      status: SettlementStateEnum.APPROVAL_SUCCESS,
      fromDate: "2023-01-01T00:00:00.000Z",
      toDate: "2023-01-31T00:00:00.000Z",
      endBalance: "0",
      currentBalance: "0",
      customerCustomerTypeId: 2,
      platformSettlements: {},
      error: "error",
      emailId: "email",
      deletedAt: "",
      settlementFolderLocation: undefined,
    };

    vi.mocked(getCustomerSettlementData).mockResolvedValue(mockCustomerData);

    const result = await groupSettlementData(mockPrisma, mockSettlementInfo);

    expect(getCustomerSettlementData).toHaveBeenCalledWith(mockPrisma, {
      customerId: mockSettlementInfo.customerId,
      fromDate: mockSettlementInfo.fromDate,
      toDate: mockSettlementInfo.toDate,
    });
    expect(result).toEqual(mockCustomerData);
  });
});
