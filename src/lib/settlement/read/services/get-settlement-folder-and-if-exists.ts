import { constants } from "node:fs";
import { access } from "node:fs/promises";

import { getSettlementFolderLocation } from "@lib/settlement/repository/get-settlement-folder-location";
import { type Prisma, type PrismaClient } from "@prisma/client";
import { resolveFilePath } from "@utils/file-system";

async function getSettlementFolderAndIfExists(
  customerCustomerTypeId: number | undefined,
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<{
  settlementFolderLocation: string | undefined;
  settlementFolderExists: boolean;
}> {
  let settlementFolderLocation;
  let settlementFolderExists = false;

  try {
    if (customerCustomerTypeId) {
      settlementFolderLocation = await getSettlementFolderLocation(
        customerCustomerTypeId,
        prisma
      );

      if (settlementFolderLocation) {
        const resolvedFilePath = resolveFilePath(settlementFolderLocation);

        // Check if the folder already exists
        try {
          await access(resolvedFilePath, constants.F_OK);
          settlementFolderExists = true;
        } catch {
          settlementFolderExists = false;
        }
      }
    }
  } catch (error) {
    settlementFolderExists = false;
    throw new Error(
      `Error accessing settlement folder: ${(error as Error).message}`
    );
  }

  return {
    settlementFolderLocation,
    settlementFolderExists,
  };
}

export { getSettlementFolderAndIfExists };
