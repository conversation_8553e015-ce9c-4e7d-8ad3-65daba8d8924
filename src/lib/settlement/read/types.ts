import {
  type ascending,
  type descending,
} from "../constants/default-parameters";

export type SettlementInformation = {
  customerSettlementGenerationId: number;
  customerTypeId: number;
  fromDate: Date;
  toDate: Date;
};

export type RequestBody = {
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: typeof ascending | typeof descending;
  nameOrServiceNumber?: string;
  clientType?: string;
  displayAdjusted?: string;
  state?: string;
  status?: string;
  frequency: SettlementFrequency | string;
  startDate?: string;
  endDate?: string;
};

export type SettlementDetails = {
  customerId: number;
  fromDate: Date;
  toDate: Date;
};

export type SettlementFilters = {
  filters: {
    textInputValue: string;
    clientType: string;
    displayAdjusted: string;
    state: string;
    status: string;
    frequency: string;
    startDate: string | undefined;
    endDate: string | undefined;
  };
  sortKey: string;
  sortOrder: "asc" | "desc";
  pageNumber: number;
  recordsPerPage: number;
};

export type LatestSettlements = {
  customerSettlementGenerationScheduleId: number;
  serviceNumber: number;
  customerCustomerTypeId: number;
  fromDate: Date;
  toDate: Date;
  updatedAt: Date;
  generationType: string;
  generationStatus: string;
  customerId: number;
  customerTypeId: number;
  customerTypeName: string;
  customerName: string;
  stateName: string;
  status: string;
  isAdjusted: boolean;
  statementFrequencyId: number;
};

export enum SettlementsOrderEnum {
  FROM_DATE = "fromDate",
  TO_DATE = "toDate",
  CLIENT_NAME = "clientName",
}

export type FilterOptions = {
  nameOrServiceNumber?: string | undefined;
  clientType?: string | undefined;
  displayAdjusted?: string | undefined;
  state?: string | undefined;
  status?: string | undefined;
  frequencyName?: SettlementFrequency | string;
  startDate?: string | undefined;
  endDate?: string | undefined;
};

export enum SettlementStateEnum {
  PROCESSING = "Processing",
  SKIPPED = "Skipped",
  ERROR = "Error",
  APPROVAL_PENDING = "Approval Pending",
  APPROVAL_PROCESSING = "Approval Processing",
  APPROVAL_ERROR = "Approval Error",
  APPROVAL_SUCCESS = "Approval Success",
  STATUS_UNKNOWN = "Status Unknown",
}

export enum PlatformCodeEnum {
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
  "SUMMARY",
}

export enum SettlementFrequency {
  MONTHLY = "Monthly",
  SEMI_MONTHLY = "Semi-Monthly",
  TWICE_PER_WEEK = "Twice Per Week",
  WEEKLY_MONDAY = "Weekly-Monday",
  WEEKLY_FRIDAY = "Weekly-Friday",
}
