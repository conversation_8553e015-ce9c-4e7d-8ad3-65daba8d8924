import {
  type ascending,
  type descending,
} from "../constants/default-parameters";

export type SettlementInformation = {
  customerSettlementGenerationId: number;
  customerTypeId: number;
  fromDate: Date;
  toDate: Date;
};

export type RequestBody = {
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: typeof ascending | typeof descending;
  nameOrServiceNumber?: string;
  clientType?: string;
  displayAdjusted?: string;
  state?: string;
  status?: string;
  frequency?: string;
  startDate?: string;
  endDate?: string;
};

export type SettlementDetails = {
  customerId: number;
  fromDate: Date;
  toDate: Date;
};

export type SettlementFilters = {
  filters: {
    textInputValue: string;
    clientType: string;
    displayAdjusted: string;
    state: string;
    status: string;
    frequency: string;
    startDate: string | undefined;
    endDate: string | undefined;
  };
  sortKey: string;
  sortOrder: "asc" | "desc";
  pageNumber: number;
  recordsPerPage: number;
};

export type LatestSettlements = {
  customerSettlementGenerationScheduleId: number;
  serviceNumber: number;
  customerCustomerTypeId: number;
  fromDate: Date;
  toDate: Date;
  updatedAt: Date;
  generationType: string;
  generationStatus: string;
  customerId: number;
  customerTypeId: number;
  statementFrequencyId: number;
  customerTypeName: string;
  customerName: string;
  stateName: string;
  status: string;
  isAdjusted: boolean;
};

export enum SettlementsOrderEnum {
  FROM_DATE = "fromDate",
  TO_DATE = "toDate",
  CLIENT_NAME = "clientName",
}

export type FilterOptions = {
  nameOrServiceNumber?: string | undefined;
  clientType?: string | undefined;
  displayAdjusted?: string | undefined;
  state?: string | undefined;
  status?: string | undefined;
  frequency?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
};

export enum SettlementStateEnum {
  PROCESSING = "Processing",
  SKIPPED = "Skipped",
  ERROR = "Error",
  APPROVAL_PENDING = "Approval Pending",
  APPROVAL_PROCESSING = "Approval Processing",
  APPROVAL_ERROR = "Approval Error",
  APPROVAL_SUCCESS = "Approval Success",
  STATUS_UNKNOWN = "Status Unknown",
}

export enum PlatformCodeEnum {
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
  "SUMMARY",
}
