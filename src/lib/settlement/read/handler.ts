import { showAllSettlements } from "./services";
import { type RequestBody } from "./types";
import { internalServerError } from "../constants/default-parameters";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  const {
    offset,
    limit,
    sortKey,
    sortOrder,
    nameOrServiceNumber,
    clientType,
    displayAdjusted,
    state,
    status,
    frequency: frequencyName,
    startDate,
    endDate,
  } = request.body as RequestBody;

  try {
    const allLatestSettlementsResult = await showAllSettlements(
      request.server.prisma,
      {
        offset,
        limit,
        sortKey,
        sortOrder,
        nameOrServiceNumber,
        clientType,
        displayAdjusted,
        state,
        status,
        frequencyName,
        startDate,
        endDate,
      }
    );

    return await reply.send(allLatestSettlementsResult);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message || internalServerError,
    });
  }
};
