import { type FastifyReply, type FastifyRequest } from "fastify";

import { handler } from "./handler";
import * as MainService from "./services";
import { internalServerError } from "../constants/default-parameters";

describe("handler", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlements on success", async () => {
    const mockResult = { totalCount: 1, settlements: [] };
    const spyShowAllSettlements = vi
      .spyOn(MainService, "showAllSettlements")
      .mockResolvedValue(mockResult);

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sortKey: "fromDate",
        sortOrder: "asc",
      },
      server: { prisma: {} },
      log: { error: vi.fn() },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyShowAllSettlements).toHaveBeenCalledWith(
      mockRequest.server.prisma,
      mockRequest.body
    );
    expect(mockReply.send).toHaveBeenCalledWith(mockResult);
  });

  it("should handle errors thrown by showAllSettlements", async () => {
    const testError = new Error("Test error: Something went wrong");
    vi.spyOn(MainService, "showAllSettlements").mockRejectedValue(testError);

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sortKey: "fromDate",
        sortOrder: "asc",
      },
      server: { prisma: {} },
      log: { error: vi.fn() },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalled();
    const errorArgument: Error =
      ((mockRequest.log.error as ReturnType<typeof vi.fn>).mock
        .calls?.[0]?.[0] as Error) ?? new Error("Unknown error");

    expect(errorArgument).toBeInstanceOf(Error);
    expect(errorArgument.message).toBe("Test error: Something went wrong");
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Test error: Something went wrong",
    });
  });

  it("should use internalServerError message if error has no message", async () => {
    vi.spyOn(MainService, "showAllSettlements").mockRejectedValue({});

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sortKey: "fromDate",
        sortOrder: "asc",
      },
      server: { prisma: {} },
      log: { error: vi.fn() },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: internalServerError,
    });
  });

  it("should handle missing optional fields in request body", async () => {
    const mockResult = { totalCount: 0, settlements: [] };
    vi.spyOn(MainService, "showAllSettlements").mockResolvedValue(mockResult);

    const mockRequest = {
      body: {
        offset: 0,
        limit: 10,
        sortKey: "fromDate",
        sortOrder: "asc",
      },
      server: { prisma: {} },
      log: { error: vi.fn() },
    } as unknown as FastifyRequest;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(mockReply.send).toHaveBeenCalledWith(mockResult);
  });
});
