import { getAllSettlementIdsByCustomerAndPeriod } from "@lib/settlement/regenerate/repository";
import { getSettlementWires } from "@lib/settlement/repository/delete-settlements";
import { getSettlementById } from "@lib/settlement/repository/settlement-frequency";
import { type PrismaClient } from "@prisma/client";
import { dateToString } from "@utils/date-only";

import { type WiresForSettlementResponse } from "./types";

export const getWiresForSettlements = async (
  prisma: PrismaClient,
  settlementId: string
): Promise<WiresForSettlementResponse> => {
  try {
    const settlement = await getSettlementById(prisma, Number(settlementId));

    if (!settlement) {
      throw new Error(`Settlement with id ${settlementId} not found`);
    }

    const settlementInfo: WiresForSettlementResponse = {
      settlementId: Number(settlementId),
      fromDate: dateToString(settlement.fromDate),
      toDate: dateToString(settlement.toDate),
      customerName: settlement.customerName,
      wires: [],
    };

    const allSettlementIdsForCustomer =
      await getAllSettlementIdsByCustomerAndPeriod(
        prisma,
        settlement.customerId,
        settlement.fromDate,
        settlement.toDate
      );

    const wires = await getSettlementWires(allSettlementIdsForCustomer, prisma);

    settlementInfo.wires = wires.map((wire) => ({
      wireId: wire.wireId,
      wireApprovedById: wire.wireApprovedById ?? undefined,
      isCancelled: wire.isCancelled ?? false,
    }));

    return settlementInfo;
  } catch (error) {
    throw new Error(
      `Failed to get wires for settlements: ${(error as Error).message}`
    );
  }
};
