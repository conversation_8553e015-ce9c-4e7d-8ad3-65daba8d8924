import { type PrismaClient } from "@prisma/client";
import { type FastifyReply, type FastifyRequest } from "fastify";
import { describe, expect, it, vi } from "vitest";

import { handler } from "./handler";
import { getWiresForSettlements } from "./service";
import { type WiresForSettlementResponse } from "./types";

vi.mock("./service");

const mockGetWiresForSettlements = vi.mocked(getWiresForSettlements);

describe("get-wires-for-settlement handler", () => {
  const mockPrisma = {} as unknown as PrismaClient;
  const mockRequest = {
    query: { settlementId: "123" },
    server: { prisma: mockPrisma },
    log: { error: vi.fn() },
  } as unknown as FastifyRequest<{ Querystring: { settlementId: string } }>;

  const mockReply = {
    send: vi.fn(),
    status: vi.fn().mockReturnThis(),
  } as unknown as FastifyReply;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return wires for settlements on success", async () => {
    const mockWiresForSettlements: WiresForSettlementResponse = {
      settlementId: 123,
      fromDate: "2024-01-01",
      toDate: "2024-01-31",
      customerName: "Test Customer",
      wires: [
        {
          wireId: 1,
          wireApprovedById: 456,
          isCancelled: false,
        },
      ],
    };

    mockGetWiresForSettlements.mockResolvedValue(mockWiresForSettlements);

    await handler(mockRequest, mockReply);

    expect(mockGetWiresForSettlements).toHaveBeenCalledWith(mockPrisma, "123");
    expect(mockReply.send).toHaveBeenCalledWith(mockWiresForSettlements);
  });

  it("should handle settlement not found error", async () => {
    const error = new Error("Settlement with id 123 not found");
    mockGetWiresForSettlements.mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(
      error,
      "Error fetching wires for settlements"
    );
    expect(mockReply.status).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: "Settlement not found",
      message: "Settlement with id 123 not found",
    });
  });

  it("should handle generic errors", async () => {
    const error = new Error("Database connection failed");
    mockGetWiresForSettlements.mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(
      error,
      "Error fetching wires for settlements"
    );
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: "Internal server error",
      message: "Failed to fetch wires for settlements",
    });
  });

  it("should handle empty settlementIds array", async () => {
    const mockRequestWithEmptyIds = {
      ...mockRequest,
      query: { settlementId: "" },
    } as unknown as FastifyRequest<{
      Querystring: { settlementId: string };
    }>;

    const mockWiresForSettlements: WiresForSettlementResponse = {
      settlementId: 0,
      fromDate: "",
      toDate: "",
      customerName: "",
      wires: [],
    };

    mockGetWiresForSettlements.mockResolvedValue(mockWiresForSettlements);

    await handler(mockRequestWithEmptyIds, mockReply);

    expect(mockGetWiresForSettlements).toHaveBeenCalledWith(mockPrisma, "");
    expect(mockReply.send).toHaveBeenCalledWith(mockWiresForSettlements);
  });
});
