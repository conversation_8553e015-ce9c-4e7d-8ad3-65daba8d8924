import { type FastifyReply, type FastifyRequest } from "fastify";

import { getWiresForSettlements } from "./service";
import { type WiresForSettlementResponse } from "./types";

export const handler = async (
  request: FastifyRequest<{ Querystring: { settlementId: string } }>,
  reply: FastifyReply
): Promise<WiresForSettlementResponse> => {
  const { settlementId } = request.query;

  try {
    const result = await getWiresForSettlements(
      request.server.prisma,
      settlementId
    );

    return await reply.send(result);
  } catch (error) {
    request.log.error(error, "Error fetching wires for settlements");

    const errorMessage = (error as Error).message;

    if (errorMessage.includes("not found")) {
      return reply.status(404).send({
        error: "Settlement not found",
        message: errorMessage,
      });
    }

    return reply.status(500).send({
      error: "Internal server error",
      message: "Failed to fetch wires for settlements",
    });
  }
};
