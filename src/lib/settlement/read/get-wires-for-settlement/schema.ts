import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Get wires for the settlement",
  description:
    "Get wires for specific settlements with customer and platform grouping",
  tags: [tags.settlement],
  querystring: {
    type: "object",
    properties: {
      settlementId: {
        type: "string",
        description: "The ID of the customer settlement to get wires for",
      },
    },
    required: ["settlementId"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        settlementId: {
          type: "number",
          description: "ID of the settlement",
        },
        fromDate: {
          type: "string",
          format: "date",
          description: "Start date of the settlement period",
        },
        toDate: {
          type: "string",
          format: "date",
          description: "End date of the settlement period",
        },
        customerName: {
          type: "string",
          description: "Name of the customer",
        },
        wires: {
          type: "array",
          items: {
            type: "object",
            properties: {
              wireId: { type: "number" },
              wireApprovedById: {
                oneOf: [{ type: "number" }, { type: "null" }],
              },
              isCancelled: { type: "boolean" },
            },
          },
          description:
            "Array of wires for this settlement and related settlements for the same customer and period",
        },
      },
      required: ["settlementId", "fromDate", "toDate", "customerName", "wires"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Bad Request - Invalid input data",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement not found",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal Server Error",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
  },
};
