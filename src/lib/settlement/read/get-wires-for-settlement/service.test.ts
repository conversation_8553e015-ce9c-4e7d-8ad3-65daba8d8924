import { getAllSettlementIdsByCustomerAndPeriod } from "@lib/settlement/regenerate/repository";
import { getSettlementWires } from "@lib/settlement/repository/delete-settlements";
import { getSettlementById } from "@lib/settlement/repository/settlement-frequency";
import { type PrismaClient } from "@prisma/client";
import { dateToString } from "@utils/date-only";
import { describe, expect, it, vi } from "vitest";

import { getWiresForSettlements } from "./service";

vi.mock("@lib/settlement/regenerate/repository");
vi.mock("@lib/settlement/repository/delete-settlements");
vi.mock("@lib/settlement/repository/settlement-frequency");
vi.mock("@utils/date-only");

const mockGetAllSettlementIdsByCustomerAndPeriod = vi.mocked(
  getAllSettlementIdsByCustomerAndPeriod
);
const mockGetSettlementWires = vi.mocked(getSettlementWires);
const mockGetSettlementById = vi.mocked(getSettlementById);
const mockDateToString = vi.mocked(dateToString);

describe("getWiresForSettlements", () => {
  const mockPrisma = {} as unknown as PrismaClient;

  beforeEach(() => {
    vi.clearAllMocks();
    mockDateToString.mockImplementation(
      (date) => date.toISOString().split("T")[0] ?? ""
    );
  });

  it("should return wires for settlements successfully", async () => {
    const settlementId = "123";
    const fromDate = new Date("2024-01-01");
    const toDate = new Date("2024-01-31");

    const mockSettlement = {
      customerId: 1,
      customerName: "Customer 1",
      serviceNumber: "12345",
      customerType: "Merchant",
      customerCustomerTypeId: 1,
      fromDate,
      toDate,
    };

    const mockWires = [
      {
        wireId: 1,
        wireApprovedById: 100,
        isCancelled: false,
      },
      {
        wireId: 2,
        wireApprovedById: null,
        isCancelled: true,
      },
    ];

    mockGetSettlementById.mockResolvedValue(mockSettlement);

    mockGetAllSettlementIdsByCustomerAndPeriod.mockResolvedValue([123, 124]);

    mockGetSettlementWires.mockResolvedValue(mockWires);

    const result = await getWiresForSettlements(mockPrisma, settlementId);

    expect(mockGetSettlementById).toHaveBeenCalledTimes(1);
    expect(mockGetSettlementById).toHaveBeenCalledWith(mockPrisma, 123);

    expect(mockGetAllSettlementIdsByCustomerAndPeriod).toHaveBeenCalledTimes(1);
    expect(mockGetAllSettlementIdsByCustomerAndPeriod).toHaveBeenCalledWith(
      mockPrisma,
      1,
      fromDate,
      toDate
    );

    expect(mockGetSettlementWires).toHaveBeenCalledTimes(1);
    expect(mockGetSettlementWires).toHaveBeenCalledWith([123, 124], mockPrisma);

    expect(result).toEqual({
      settlementId: 123,
      fromDate: "2024-01-01",
      toDate: "2024-01-31",
      customerName: "Customer 1",
      wires: [
        {
          wireId: 1,
          wireApprovedById: 100,
          isCancelled: false,
        },
        {
          wireId: 2,
          wireApprovedById: undefined,
          isCancelled: true,
        },
      ],
    });
  });

  it("should handle settlement not found error", async () => {
    const settlementId = "999";
    // @ts-expect-error - mockGetSettlementById is mocked to return undefined
    mockGetSettlementById.mockResolvedValue();

    await expect(
      getWiresForSettlements(mockPrisma, settlementId)
    ).rejects.toThrow("Settlement with id 999 not found");

    expect(mockGetSettlementById).toHaveBeenCalledWith(mockPrisma, 999);
  });

  it("should handle empty settlementIds array", async () => {
    const settlementId = "";

    await expect(
      getWiresForSettlements(mockPrisma, settlementId)
    ).rejects.toThrow("Settlement with id  not found");

    expect(mockGetSettlementById).toHaveBeenCalledWith(mockPrisma, 0);
  });

  it("should handle database errors", async () => {
    const settlementId = "123";
    const databaseError = new Error("Database connection failed");
    mockGetSettlementById.mockRejectedValue(databaseError);

    await expect(
      getWiresForSettlements(mockPrisma, settlementId)
    ).rejects.toThrow(
      "Failed to get wires for settlements: Database connection failed"
    );
  });

  it("should handle wires with null values correctly", async () => {
    const settlementId = "123";
    const fromDate = new Date("2024-01-01");
    const toDate = new Date("2024-01-31");

    const mockSettlement = {
      customerId: 1,
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Merchant",
      customerCustomerTypeId: 1,
      fromDate,
      toDate,
    };

    const mockWires = [
      {
        wireId: 1,
        wireApprovedById: null,
        isCancelled: null,
      },
    ];

    mockGetSettlementById.mockResolvedValue(mockSettlement);
    mockGetAllSettlementIdsByCustomerAndPeriod.mockResolvedValue([123]);
    mockGetSettlementWires.mockResolvedValue(mockWires);

    const result = await getWiresForSettlements(mockPrisma, settlementId);

    expect(result.wires[0]).toEqual({
      wireId: 1,
      wireApprovedById: undefined,
      isCancelled: false,
    });
  });

  it("should handle multiple settlements for same customer and period", async () => {
    const settlementId = "123";
    const fromDate = new Date("2024-01-01");
    const toDate = new Date("2024-01-31");

    const mockSettlement = {
      customerId: 1,
      customerName: "Customer 1",
      serviceNumber: "12345",
      customerType: "Merchant",
      customerCustomerTypeId: 1,
      fromDate,
      toDate,
    };

    const mockWires = [
      {
        wireId: 1,
        wireApprovedById: 100,
        isCancelled: false,
      },
    ];

    mockGetSettlementById.mockResolvedValue(mockSettlement);
    mockGetAllSettlementIdsByCustomerAndPeriod.mockResolvedValue([123, 124]);
    mockGetSettlementWires.mockResolvedValue(mockWires);

    const result = await getWiresForSettlements(mockPrisma, settlementId);

    expect(result.settlementId).toBe(123);
    expect(result.customerName).toBe("Customer 1");
    expect(result.wires).toHaveLength(1);
    expect(result.wires[0]?.wireId).toBe(1);
  });
});
