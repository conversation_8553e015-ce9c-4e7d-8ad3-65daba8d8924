import { addMonths, isAfter } from "date-fns";

import { type DateOnly, fromDateOnly } from "../../../../utils/date-only";
import {
  type TierSet,
  type TierItem,
  type MerchantPlatformMember,
  type NonMerchantPlatformConfiguration,
} from "../../repository/types";

type Rates = {
  transactionFee: number;
  reject1Fee: number;
  salesFee: number;
  gatewayFee: number;
  refundFee: number;
  minimumThreshold: number;
  minimumCharge: number;
};

type RatesWithMeta = {
  rates: Rates;
  meta: RatesMeta;
};

type RatesMeta = {
  isMinimumFeeApplicable: boolean;
  transactionTierItem: TierItem;
  rejectOneTierItem: TierItem;
  delayMonths: {
    isDelayApplicable: boolean;
    delayInMonths: number;
    tierItem: TierItem | undefined;
  };
};

const determineMerchantFeeRates = (
  effectiveStartDate: DateOnly,
  totalAmount: number,
  configuration: MerchantPlatformMember,
  firstTransactionEver?: DateOnly
): RatesWithMeta => {
  const { delayInMonths, delayTierItem } = configuration;
  const transactionTierSet = configuration.tierSet;
  let transactionTierItem = _findTierItemForAmount(
    transactionTierSet,
    totalAmount
  );

  if (!transactionTierItem) {
    throw new Error(
      `Cannot find tier item for platform ${configuration.platformCode} for merchant platform id ${configuration.id}`
    );
  }

  const rejectOneTierSet = configuration.rejected1TierSet ?? transactionTierSet;
  const rejectOneTierItem = _findTierItemForAmount(
    rejectOneTierSet,
    totalAmount
  );

  let isDelayApplicable = false;

  if (
    delayTierItem &&
    delayInMonths &&
    delayInMonths > 0 &&
    firstTransactionEver
  ) {
    isDelayApplicable = _isDelayTierApplicable(
      fromDateOnly(effectiveStartDate),
      fromDateOnly(firstTransactionEver),
      delayInMonths
    );

    if (
      isDelayApplicable &&
      transactionTierItem.maxAmount < delayTierItem.maxAmount
    ) {
      transactionTierItem = delayTierItem;
    }
  }

  return {
    rates: {
      transactionFee: transactionTierItem.transactionFee,
      reject1Fee: rejectOneTierItem?.transactionFee ?? 0,
      salesFee: transactionTierItem.salesFee,
      gatewayFee: configuration.gatewayFee,
      refundFee: configuration.refundFee,
      minimumThreshold: configuration.minimumThreshold,
      minimumCharge: configuration.minimumCharge,
    },

    meta: {
      isMinimumFeeApplicable: configuration.hasMinimum,
      transactionTierItem,
      rejectOneTierItem: rejectOneTierItem!,
      delayMonths: {
        isDelayApplicable,
        delayInMonths,
        tierItem: delayTierItem,
      },
    },
  } satisfies RatesWithMeta;
};

const determineBuyRate = (
  totalAmount: number,
  configuration: Omit<NonMerchantPlatformConfiguration, "nonMerchantId">
): Rates & { tierItem: TierItem } => {
  const tierItem = _findTierItemForAmount(
    configuration.saleTierSet,
    totalAmount ?? 0
  );

  if (!tierItem) {
    throw new Error(
      `Tier item not found in tier set "${configuration.saleTierSet.name}".
       Amount: ${totalAmount}.`
    );
  }

  return {
    transactionFee: tierItem.transactionFee,
    reject1Fee: 0,
    salesFee: tierItem.salesFee,
    gatewayFee: configuration.gatewayFeePercentage,
    refundFee: 0,
    minimumThreshold: 0,
    minimumCharge: 0,
    tierItem,
  };
};

const determineCommissionRate = (
  totalAmount: number,
  configuration: Omit<NonMerchantPlatformConfiguration, "nonMerchantId">
): Rates & { tierItem: TierItem } => {
  const tierItem = _findTierItemForAmount(
    configuration.tierSet,
    totalAmount ?? 0
  );

  if (!tierItem) {
    throw new Error(
      `Tier item not found in tier set "${configuration.tierSet.name}".
       Amount: ${totalAmount}.`
    );
  }

  return {
    transactionFee: tierItem.transactionFee,
    reject1Fee: 0,
    salesFee: tierItem.salesFee,
    gatewayFee: 0,
    refundFee: 0,
    minimumThreshold: 0,
    minimumCharge: 0,
    tierItem,
  };
};

function _isDelayTierApplicable(
  fromDate: Date,
  firstTxnDate: Date,
  delayInMonth: number
) {
  let isDelayDateInRange = false;

  if (delayInMonth && delayInMonth > 0 && firstTxnDate) {
    const delayedDate = addMonths(new Date(firstTxnDate), delayInMonth);
    isDelayDateInRange = isAfter(delayedDate, new Date(fromDate));
  }

  return isDelayDateInRange;
}

function _findTierItemForAmount(
  tierSet: TierSet,
  amount: number
): TierItem | undefined {
  for (const tierItem of tierSet.tierItem) {
    if (tierItem.minAmount <= amount && amount < tierItem.maxAmount) {
      return tierItem;
    }
  }

  return undefined;
}

export {
  determineMerchantFeeRates,
  determineBuyRate,
  determineCommissionRate,
  type Rates,
  type RatesMeta,
  type RatesWithMeta,
};
