/* eslint-disable @typescript-eslint/naming-convention */

import { type Platform } from "@constants/transactions/platform";
import {
  endOfPreviousMonthOnly,
  startOfPreviousMonthOnly,
} from "@utils/date-only";

import { calculateMerchantSettlement } from "./calculate-merchant-settlement";
import { calculateNonMerchantSettlement } from "./calculate-non-merchant-settlement";
import { getTransactionsTotalAmount } from "../../../../services/blusky/transactions";
import { SettlementError } from "../../../../utils/errors/settlement-error";

import type { NonMerchantFeesBreakdown, SettlementResult } from "./types";
import type {
  MerchantSettlementConfiguration,
  NonMerchantSettlementConfiguration,
} from "@lib/settlement/repository/types";

vi.mock("./calculate-merchant-settlement", () => ({
  calculateMerchantSettlement: vi.fn(),
}));
vi.mock("../../../../services/blusky/transactions", () => ({
  getTransactionsTotalAmount: vi.fn(),
}));

const mockNonMerchantSettlementConfig: NonMerchantSettlementConfiguration = {
  customerCustomerTypeId: 1,
  customerId: 1,
  customerName: "Agent 1",
  serviceNumber: "9990000001",
  settlementType: "Commision Rate",
  isCombineMultipleServices: false,
  combinedMerchantServiceNumbers: [],
  nonMerchantType: {
    id: 1,
    isCombineIdpTierSet: false,
    isCombineAchTierSet: false,
    isCombineRtoTierSet: false,
    statementFolderLocation: "/agent1",
    statementFrequencyId: 3,
    statementFrequencyName: "Semi-Monthly",
    statementFrequencyCode: "SM",
    serviceNumbers: [],
    customerTypeId: 1,
    customerTypeName: "Agent",
    volumeCombination: [
      {
        fromDate: new Date("2024-09-01"),
        paymentType: { paymentTypeName: "Pay-Out" },
        volumeCombinationPlatform: [
          {
            enabled: true,
            platform: {
              platformCode: "ETO",
              platformName: "E-Transfer-Out",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "ACH",
              platformName: "Automated Clearing House",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "RTO",
              platformName: "Real-Time E-Transfer-Out",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "RTX",
              platformName: "Real-Time Out  (RTX)",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "ANR",
              platformName: "Real Time Automated Clearing House",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "ANX",
              platformName: "Real Time eCashout (ANX)",
            },
          },
        ],
      },
      {
        fromDate: new Date("2024-09-01"),
        paymentType: { paymentTypeName: "Pay-In" },
        volumeCombinationPlatform: [
          {
            enabled: true,
            platform: {
              platformCode: "ETI",
              platformName: "E-Transfer In",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "RFM",
              platformName: "Request For Money",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "IDP",
              platformName: "Interac Online Purchase",
            },
          },
          {
            enabled: true,
            platform: {
              platformCode: "ETF",
              platformName: "e-Transfer In - FastPlay",
            },
          },
        ],
      },
    ],
  },
  associatedMerchantIds: [7, 8],
};

const mockMerchantsSettlementConfig: MerchantSettlementConfiguration[] = [
  {
    customerCustomerTypeId: 7,
    customerId: 7,
    serviceNumber: "9990000007",
    customerName: "Merchant 1",
    customerTradingName: "Merchant 1",
    platformConfigurations: {
      ETI: [
        {
          fromDate: { year: 2021, month: 1, day: 1 },
          agent: {
            nonMerchantId: 1,
            isCumulative: false,
            gatewayFeePercentage: 0.01,
            tierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 2_000_000,
                  salesFee: 0.003,
                  transactionFee: 0,
                },
                {
                  minAmount: 2_000_000,
                  maxAmount: 4_000_000,
                  salesFee: 0.0035,
                  transactionFee: 0,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0.0045,
                  transactionFee: 0,
                },
              ],
            },
            saleTierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 500_000,
                  salesFee: 0.0395,
                  transactionFee: 0.65,
                },
                {
                  minAmount: 500_000,
                  maxAmount: 4_000_000,
                  salesFee: 0.0295,
                  transactionFee: 0.65,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 8_000_000,
                  salesFee: 0.0195,
                  transactionFee: 0.65,
                },
                {
                  minAmount: 8_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0.015,
                  transactionFee: 0.65,
                },
              ],
            },
          },
        },
      ],
      RTO: [
        {
          fromDate: { year: 2021, month: 1, day: 1 },
          agent: {
            nonMerchantId: 1,
            isCumulative: false,
            gatewayFeePercentage: 0,
            tierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 4_000_000,
                  salesFee: 0,
                  transactionFee: 0.08,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0,
                  transactionFee: 0.1,
                },
              ],
            },
            saleTierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 750_000,
                  salesFee: 0,
                  transactionFee: 2.85,
                },
                {
                  minAmount: 750_000,
                  maxAmount: 1_500_000,
                  salesFee: 0,
                  transactionFee: 2.65,
                },
                {
                  minAmount: 1_500_000,
                  maxAmount: 2_000_000,
                  salesFee: 0,
                  transactionFee: 2.45,
                },
                {
                  minAmount: 2_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0,
                  transactionFee: 2.25,
                },
              ],
            },
          },
        },
      ],
    },
  },
  {
    customerCustomerTypeId: 8,
    customerId: 8,
    serviceNumber: "9990000008",
    customerName: "Merchant 2",
    customerTradingName: "Merchant 2",
    platformConfigurations: {
      ETI: [
        {
          fromDate: { year: 2021, month: 1, day: 1 },
          agent: {
            nonMerchantId: 1,
            isCumulative: false,
            gatewayFeePercentage: 0.005,
            tierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 2_000_000,
                  salesFee: 0.002,
                  transactionFee: 0,
                },
                {
                  minAmount: 2_000_000,
                  maxAmount: 4_000_000,
                  salesFee: 0.0025,
                  transactionFee: 0,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0.0035,
                  transactionFee: 0,
                },
              ],
            },
            saleTierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 500_000,
                  salesFee: 0.035,
                  transactionFee: 0.55,
                },
                {
                  minAmount: 500_000,
                  maxAmount: 4_000_000,
                  salesFee: 0.025,
                  transactionFee: 0.65,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 8_000_000,
                  salesFee: 0.015,
                  transactionFee: 0.65,
                },
                {
                  minAmount: 8_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0.01,
                  transactionFee: 0.65,
                },
              ],
            },
          },
        },
      ],
      RTO: [
        {
          fromDate: { year: 2021, month: 1, day: 1 },
          agent: {
            nonMerchantId: 1,
            isCumulative: false,
            gatewayFeePercentage: 0,
            tierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 4_000_000,
                  salesFee: 0,
                  transactionFee: 0.1,
                },
                {
                  minAmount: 4_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0,
                  transactionFee: 0.2,
                },
              ],
            },
            saleTierSet: {
              tierItem: [
                {
                  minAmount: 0,
                  maxAmount: 750_000,
                  salesFee: 0,
                  transactionFee: 2.5,
                },
                {
                  minAmount: 750_000,
                  maxAmount: 1_500_000,
                  salesFee: 0,
                  transactionFee: 2,
                },
                {
                  minAmount: 1_500_000,
                  maxAmount: 2_000_000,
                  salesFee: 0,
                  transactionFee: 1.5,
                },
                {
                  minAmount: 2_000_000,
                  maxAmount: 100_000_000,
                  salesFee: 0,
                  transactionFee: 1,
                },
              ],
            },
          },
        },
      ],
    },
  },
] as unknown as MerchantSettlementConfiguration[];

const mockInterval = {
  fromDate: {
    year: 2025,
    month: 1,
    day: 1,
  },
  toDate: {
    year: 2025,
    month: 1,
    day: 31,
  },
};

const mockMerchantSettlement1 = {
  settlement: {
    ETI: {
      platformId: 2,
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      platformDisplaySequence: 2,
      totalChargedFees: {
        gatewayFeeTotal: 0,
        salesFeeTotal: 72_160.24,
        transactionFeeTotal: 2496.2,
        refundFeeTotal: 0,
        rejected1FeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      totalTransactionSummary: {
        transactionCount: 3566,
        totalTransactionAmount: 2_405_341.42,
        minimumAmountCount: 0,
        totalMinimumAmount: 0,
        partialReturnCount: 0,
        totalPartialReturnAmount: 0,
        rejected1Count: 0,
        totalRejected1Amount: 0,
        refundCount: 0,
        totalRefundAmount: 0,
      },
    },
    RTO: {
      platformId: 13,
      platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
      platformDisplaySequence: 6,
      totalChargedFees: {
        gatewayFeeTotal: 0,
        salesFeeTotal: 0,
        transactionFeeTotal: 7107,
        refundFeeTotal: 0,
        rejected1FeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      totalTransactionSummary: {
        transactionCount: 2369,
        totalTransactionAmount: 1_789_121.11,
        minimumAmountCount: 0,
        totalMinimumAmount: 0,
        partialReturnCount: 0,
        totalPartialReturnAmount: 0,
        rejected1Count: 0,
        totalRejected1Amount: 0,
        refundCount: 0,
        totalRefundAmount: 0,
      },
    },
  },
} as unknown as SettlementResult;

const mockMerchantSettlement2 = {
  settlement: {
    ETI: {
      platformId: 2,
      platformDescription: "Interac e-Transfer Pay-In (ETI)",
      platformDisplaySequence: 2,
      totalChargedFees: {
        gatewayFeeTotal: 0,
        salesFeeTotal: 65_122.48,
        transactionFeeTotal: 4110.3,
        refundFeeTotal: 0,
        rejected1FeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      totalTransactionSummary: {
        transactionCount: 4567,
        totalTransactionAmount: 3_256_123.97,
        minimumAmountCount: 0,
        totalMinimumAmount: 0,
        partialReturnCount: 0,
        totalPartialReturnAmount: 0,
        rejected1Count: 0,
        totalRejected1Amount: 0,
        refundCount: 0,
        totalRefundAmount: 0,
      },
    },
    RTO: {
      platformId: 13,
      platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
      platformDisplaySequence: 6,
      totalChargedFees: {
        gatewayFeeTotal: 0,
        salesFeeTotal: 0,
        transactionFeeTotal: 11_664,
        refundFeeTotal: 0,
        rejected1FeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      totalTransactionSummary: {
        transactionCount: 3888,
        totalTransactionAmount: 2_897_237.12,
        minimumAmountCount: 0,
        totalMinimumAmount: 0,
        partialReturnCount: 0,
        totalPartialReturnAmount: 0,
        rejected1Count: 0,
        totalRejected1Amount: 0,
        refundCount: 0,
        totalRefundAmount: 0,
      },
    },
  },
} as unknown as SettlementResult;

const mockTransactionsTotals = {
  ETI: 4_568_238.39,
  RTO: 2_556_234.12,
} as unknown as Record<Platform, number>;

const mockToken = "token";

describe("calculateNonMerchantSettlement", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it("throws error for invalid settlementType", async () => {
    const badConfig = {
      ...mockNonMerchantSettlementConfig,
      settlementType: "invalidType",
    };

    await expect(
      calculateNonMerchantSettlement(
        {
          nonMerchantSettlementConfig: badConfig,
          merchantsSettlementConfig: mockMerchantsSettlementConfig,
        },
        mockInterval,
        "token"
      )
    ).rejects.toThrow("Invalid settlementType");
  });

  it("calculates non-merchant settlement correctly for commission rate", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: mockNonMerchantSettlementConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken
    );

    expect(calculateMerchantSettlement).toHaveBeenCalledTimes(2);
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[0],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[1],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(2);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[0]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[1]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    const expectedResult = {
      settlement: {
        ETI: {
          platformId: 2,
          platformDescription: "Interac e-Transfer Pay-In (ETI)",
          platformDisplaySequence: 2,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 22_220.470_29,
            transactionFeeTotal: 0,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 8133,
            totalTransactionAmount: 5_661_465.39,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 22_220.470_29,
            totalTransactionCommission: 22_220.470_29,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 74_656.44,
                  meta: {
                    transactionCount: 3566,
                    totalTransactionAmount: 2_405_341.42,
                  },
                },
                commission: 10_824.036_39,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 10_824.036_39,
                    transactionFeeTotal: 0,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 10_824.036_39,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.01,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0045,
                      transactionFee: 0,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 69_232.78,
                  meta: {
                    transactionCount: 4567,
                    totalTransactionAmount: 3_256_123.97,
                  },
                },
                commission: 11_396.4339,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 11_396.4339,
                    transactionFeeTotal: 0,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 11_396.4339,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.005,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0035,
                      transactionFee: 0,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
        RTO: {
          platformId: 13,
          platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
          platformDisplaySequence: 6,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 0,
            transactionFeeTotal: 578.32,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 6257,
            totalTransactionAmount: 4_686_358.23,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 578.32,
            totalTransactionCommission: 578.32,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 7107,
                  meta: {
                    transactionCount: 2369,
                    totalTransactionAmount: 1_789_121.11,
                  },
                },
                commission: 189.52,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 189.52,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 189.52,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.08,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 11_664,
                  meta: {
                    transactionCount: 3888,
                    totalTransactionAmount: 2_897_237.12,
                  },
                },
                commission: 388.8,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 388.8,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 388.8,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.1,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
      },
    };

    expect(result).toEqual(expectedResult);
  });

  it("calculates non-merchant settlement correctly for buy rate", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const buyRateNonMerchantConfig = {
      ...mockNonMerchantSettlementConfig,
      settlementType: "Buy Rate",
    };

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: buyRateNonMerchantConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken
    );

    expect(calculateMerchantSettlement).toHaveBeenCalledTimes(2);
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[0],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[1],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(2);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[0]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[1]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    const expectedResult = {
      settlement: {
        ETI: {
          platformId: 2,
          platformDescription: "Interac e-Transfer Pay-In (ETI)",
          platformDisplaySequence: 2,
          totalChargedFees: {
            gatewayFeeTotal: 40_334.034_05,
            salesFeeTotal: 95_746.017_24,
            transactionFeeTotal: 5286.45,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 8133,
            totalTransactionAmount: 5_661_465.39,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 2522.718_71,
            totalTransactionCommission: 0,
            totalSalesCommission: 2522.718_71,
            settlementType: "Buy Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 74_656.44,
                  meta: {
                    transactionCount: 3566,
                    totalTransactionAmount: 2_405_341.42,
                  },
                },
                commission: 1380.968_11,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 24_053.4142,
                    salesFeeTotal: 46_904.157_69,
                    transactionFeeTotal: 2317.9,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 0,
                  salesCommission: 1380.968_11,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.01,
                    salesTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 8_000_000,
                      salesFee: 0.0195,
                      transactionFee: 0.65,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 69_232.78,
                  meta: {
                    transactionCount: 4567,
                    totalTransactionAmount: 3_256_123.97,
                  },
                },
                commission: 1141.7506,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 16_280.619_85,
                    salesFeeTotal: 48_841.859_55,
                    transactionFeeTotal: 2968.55,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 0,
                  salesCommission: 1141.7506,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.005,
                    salesTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 8_000_000,
                      salesFee: 0.015,
                      transactionFee: 0.65,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
        RTO: {
          platformId: 13,
          platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
          platformDisplaySequence: 6,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 0,
            transactionFeeTotal: 9218.25,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 6257,
            totalTransactionAmount: 4_686_358.23,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 9552.75,
            totalTransactionCommission: 0,
            totalSalesCommission: 9552.75,
            settlementType: "Buy Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 7107,
                  meta: {
                    transactionCount: 2369,
                    totalTransactionAmount: 1_789_121.11,
                  },
                },
                commission: 1776.75,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 5330.25,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 0,
                  salesCommission: 1776.75,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    salesTierItem: {
                      minAmount: 2_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0,
                      transactionFee: 2.25,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 11_664,
                  meta: {
                    transactionCount: 3888,
                    totalTransactionAmount: 2_897_237.12,
                  },
                },
                commission: 7776,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 3888,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 0,
                  salesCommission: 7776,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    salesTierItem: {
                      minAmount: 2_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0,
                      transactionFee: 1,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
      },
    };

    expect(result).toEqual(expectedResult);
  });

  it("calculates non-merchant settlement correctly for combination", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const combinationNonMerchantConfig = {
      ...mockNonMerchantSettlementConfig,
      settlementType: "Combination",
    };

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: combinationNonMerchantConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken
    );

    expect(calculateMerchantSettlement).toHaveBeenCalledTimes(2);
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[0],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[1],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(2);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[0]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[1]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    const expectedResult = {
      settlement: {
        ETI: {
          platformId: 2,
          platformDescription: "Interac e-Transfer Pay-In (ETI)",
          platformDisplaySequence: 2,
          totalChargedFees: {
            gatewayFeeTotal: 40_334.034_05,
            salesFeeTotal: 95_746.017_24,
            transactionFeeTotal: 5286.45,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 8133,
            totalTransactionAmount: 5_661_465.39,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 24_743.189,
            totalTransactionCommission: 22_220.470_29,
            totalSalesCommission: 2522.718_71,
            settlementType: "Combination",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 74_656.44,
                  meta: {
                    transactionCount: 3566,
                    totalTransactionAmount: 2_405_341.42,
                  },
                },
                commission: 12_205.0045,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 24_053.4142,
                    salesFeeTotal: 46_904.157_69,
                    transactionFeeTotal: 2317.9,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 10_824.036_39,
                  salesCommission: 1380.968_11,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.01,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0045,
                      transactionFee: 0,
                    },
                    salesTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 8_000_000,
                      salesFee: 0.0195,
                      transactionFee: 0.65,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 69_232.78,
                  meta: {
                    transactionCount: 4567,
                    totalTransactionAmount: 3_256_123.97,
                  },
                },
                commission: 12_538.1845,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 16_280.619_85,
                    salesFeeTotal: 48_841.859_55,
                    transactionFeeTotal: 2968.55,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 11_396.4339,
                  salesCommission: 1141.7506,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.005,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0035,
                      transactionFee: 0,
                    },
                    salesTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 8_000_000,
                      salesFee: 0.015,
                      transactionFee: 0.65,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
        RTO: {
          platformId: 13,
          platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
          platformDisplaySequence: 6,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 0,
            transactionFeeTotal: 9218.25,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 6257,
            totalTransactionAmount: 4_686_358.23,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 10_131.07,
            totalTransactionCommission: 578.32,
            totalSalesCommission: 9552.75,
            settlementType: "Combination",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 7107,
                  meta: {
                    transactionCount: 2369,
                    totalTransactionAmount: 1_789_121.11,
                  },
                },
                commission: 1966.27,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 5330.25,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 189.52,
                  salesCommission: 1776.75,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.08,
                    },
                    salesTierItem: {
                      minAmount: 2_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0,
                      transactionFee: 2.25,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 11_664,
                  meta: {
                    transactionCount: 3888,
                    totalTransactionAmount: 2_897_237.12,
                  },
                },
                commission: 8164.8,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 3888,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 388.8,
                  salesCommission: 7776,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.1,
                    },
                    salesTierItem: {
                      minAmount: 2_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0,
                      transactionFee: 1,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
      },
    };

    expect(result).toEqual(expectedResult);
  });

  it("skips merchant if calculateMerchantSettlement throws SettlementError with isSkipped", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockRejectedValueOnce(new SettlementError("test skip", true))
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: mockNonMerchantSettlementConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken
    );

    expect(calculateMerchantSettlement).toHaveBeenCalledTimes(2);
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[0],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });
    expect(calculateMerchantSettlement).toHaveBeenCalledWith({
      merchantConfig: mockMerchantsSettlementConfig[1],
      interval: mockInterval,
      token: mockToken,
      options: {},
    });

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(1);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[1]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    const expectedResult = {
      settlement: {
        ETI: {
          platformId: 2,
          platformDescription: "Interac e-Transfer Pay-In (ETI)",
          platformDisplaySequence: 2,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 11_396.4339,
            transactionFeeTotal: 0,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 4567,
            totalTransactionAmount: 3_256_123.97,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 11_396.4339,
            totalTransactionCommission: 11_396.4339,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 69_232.78,
                  meta: {
                    transactionCount: 4567,
                    totalTransactionAmount: 3_256_123.97,
                  },
                },
                commission: 11_396.4339,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 11_396.4339,
                    transactionFeeTotal: 0,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 11_396.4339,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.005,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0035,
                      transactionFee: 0,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
        RTO: {
          platformId: 13,
          platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
          platformDisplaySequence: 6,
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 0,
            transactionFeeTotal: 388.8,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 3888,
            totalTransactionAmount: 2_897_237.12,
          },
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 388.8,
            totalTransactionCommission: 388.8,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 11_664,
                  meta: {
                    transactionCount: 3888,
                    totalTransactionAmount: 2_897_237.12,
                  },
                },
                commission: 388.8,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 388.8,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 388.8,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.1,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
        },
      },
    };

    expect(result).toEqual(expectedResult);
  });

  it("throws error if isCombineMultipleServices but a merchant platform is configured as split", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const combineConfig = {
      ...mockNonMerchantSettlementConfig,
      isCombineMultipleServices: true,
    };

    const merchantsSettlementConfigCumulative = structuredClone(
      mockMerchantsSettlementConfig
    );

    merchantsSettlementConfigCumulative[0]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = true;

    merchantsSettlementConfigCumulative[1]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = false;

    await expect(
      calculateNonMerchantSettlement(
        {
          nonMerchantSettlementConfig: combineConfig,
          merchantsSettlementConfig: merchantsSettlementConfigCumulative,
        },
        mockInterval,
        mockToken
      )
    ).rejects.toThrow("merchants are configured as split");
  });

  it("calculates true up if included in options", async () => {
    const mockMerchantSettlementWithTrueUp1 = {
      settlement: {
        ETI: {
          ...mockMerchantSettlement1.settlement["ETI"],
          trueUp: {
            ...mockMerchantSettlement1.settlement["ETI"],
          },
        },
        RTO: {
          ...mockMerchantSettlement1.settlement["RTO"],
          trueUp: {
            ...mockMerchantSettlement1.settlement["RTO"],
          },
        },
      },
    } as unknown as SettlementResult;

    const mockMerchantSettlementWithTrueUp2 = {
      settlement: {
        ETI: {
          ...mockMerchantSettlement2.settlement["ETI"],
          trueUp: {
            ...mockMerchantSettlement2.settlement["ETI"],
          },
        },
        RTO: {
          ...mockMerchantSettlement2.settlement["RTO"],
          trueUp: {
            ...mockMerchantSettlement2.settlement["RTO"],
          },
        },
      },
    } as unknown as SettlementResult;

    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlementWithTrueUp1)
      .mockResolvedValueOnce(mockMerchantSettlementWithTrueUp2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const options = {
      trueUp: {
        rateDeterminingInterval: {
          fromDate: {
            year: 2025,
            month: 1,
            day: 1,
          },
          toDate: {
            year: 2025,
            month: 1,
            day: 31,
          },
        },
      },
    };

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: mockNonMerchantSettlementConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken,
      options
    );

    const expectedResult = {
      settlement: {
        ETI: {
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 22_220.470_29,
            transactionFeeTotal: 0,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 8133,
            totalTransactionAmount: 5_661_465.39,
          },
          platformId: 2,
          platformDescription: "Interac e-Transfer Pay-In (ETI)",
          platformDisplaySequence: 2,
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 22_220.470_29,
            totalTransactionCommission: 22_220.470_29,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 74_656.44,
                  meta: {
                    transactionCount: 3566,
                    totalTransactionAmount: 2_405_341.42,
                  },
                },
                commission: 10_824.036_39,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 10_824.036_39,
                    transactionFeeTotal: 0,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 10_824.036_39,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.01,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0045,
                      transactionFee: 0,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 69_232.78,
                  meta: {
                    transactionCount: 4567,
                    totalTransactionAmount: 3_256_123.97,
                  },
                },
                commission: 11_396.4339,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 11_396.4339,
                    transactionFeeTotal: 0,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 11_396.4339,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 4_568_238.39,
                    isCumulative: false,
                    gatewayFee: 0.005,
                    transactionTierItem: {
                      minAmount: 4_000_000,
                      maxAmount: 100_000_000,
                      salesFee: 0.0035,
                      transactionFee: 0,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
          trueUp: {
            totalChargedFees: {
              gatewayFeeTotal: 0,
              salesFeeTotal: 22_220.470_29,
              transactionFeeTotal: 0,
              refundFeeTotal: 0,
              rejected1FeeTotal: 0,
              minimumFeeTotal: 0,
              partialReturnFeeTotal: 0,
            },
            totalTransactionSummary: {
              transactionCount: 8133,
              totalTransactionAmount: 5_661_465.39,
            },
            platformId: 2,
            platformDescription: "Interac e-Transfer Pay-In (ETI)",
            platformDisplaySequence: 2,
            isContractChange: false,
            feesBreakdown: {
              totalCommission: 22_220.470_29,
              totalTransactionCommission: 22_220.470_29,
              totalSalesCommission: 0,
              settlementType: "Commision Rate",
              interval: {
                fromDate: { year: 2025, month: 1, day: 1 },
                toDate: { year: 2025, month: 1, day: 31 },
              },
              merchantBreakdown: {
                "7": {
                  feeCollectedFromMerchant: {
                    totalCollectedFee: 74_656.44,
                    meta: {
                      transactionCount: 3566,
                      totalTransactionAmount: 2_405_341.42,
                    },
                  },
                  commission: 10_824.036_39,
                  fees: {
                    chargedFees: {
                      gatewayFeeTotal: 0,
                      salesFeeTotal: 10_824.036_39,
                      transactionFeeTotal: 0,
                      refundFeeTotal: 0,
                      rejected1FeeTotal: 0,
                      minimumFeeTotal: 0,
                      partialReturnFeeTotal: 0,
                    },
                    transactionCommission: 10_824.036_39,
                    salesCommission: 0,
                    rates: {
                      combineTotal: 4_568_238.39,
                      isCumulative: false,
                      gatewayFee: 0.01,
                      transactionTierItem: {
                        minAmount: 4_000_000,
                        maxAmount: 100_000_000,
                        salesFee: 0.0045,
                        transactionFee: 0,
                      },
                    },
                  },
                  merchantName: "Merchant 1",
                  serviceNumber: "9990000007",
                  rateDeterminingInterval: {
                    fromDate: { year: 2025, month: 1, day: 1 },
                    toDate: { year: 2025, month: 1, day: 31 },
                  },
                },
                "8": {
                  feeCollectedFromMerchant: {
                    totalCollectedFee: 69_232.78,
                    meta: {
                      transactionCount: 4567,
                      totalTransactionAmount: 3_256_123.97,
                    },
                  },
                  commission: 11_396.4339,
                  fees: {
                    chargedFees: {
                      gatewayFeeTotal: 0,
                      salesFeeTotal: 11_396.4339,
                      transactionFeeTotal: 0,
                      refundFeeTotal: 0,
                      rejected1FeeTotal: 0,
                      minimumFeeTotal: 0,
                      partialReturnFeeTotal: 0,
                    },
                    transactionCommission: 11_396.4339,
                    salesCommission: 0,
                    rates: {
                      combineTotal: 4_568_238.39,
                      isCumulative: false,
                      gatewayFee: 0.005,
                      transactionTierItem: {
                        minAmount: 4_000_000,
                        maxAmount: 100_000_000,
                        salesFee: 0.0035,
                        transactionFee: 0,
                      },
                    },
                  },
                  merchantName: "Merchant 2",
                  serviceNumber: "9990000008",
                  rateDeterminingInterval: {
                    fromDate: { year: 2025, month: 1, day: 1 },
                    toDate: { year: 2025, month: 1, day: 31 },
                  },
                },
              },
            },
          },
        },
        RTO: {
          totalChargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 0,
            transactionFeeTotal: 578.32,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          totalTransactionSummary: {
            transactionCount: 6257,
            totalTransactionAmount: 4_686_358.23,
          },
          platformId: 13,
          platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
          platformDisplaySequence: 6,
          isContractChange: false,
          feesBreakdown: {
            totalCommission: 578.32,
            totalTransactionCommission: 578.32,
            totalSalesCommission: 0,
            settlementType: "Commision Rate",
            interval: {
              fromDate: { year: 2025, month: 1, day: 1 },
              toDate: { year: 2025, month: 1, day: 31 },
            },
            merchantBreakdown: {
              "7": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 7107,
                  meta: {
                    transactionCount: 2369,
                    totalTransactionAmount: 1_789_121.11,
                  },
                },
                commission: 189.52,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 189.52,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 189.52,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.08,
                    },
                  },
                },
                merchantName: "Merchant 1",
                serviceNumber: "9990000007",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
              "8": {
                feeCollectedFromMerchant: {
                  totalCollectedFee: 11_664,
                  meta: {
                    transactionCount: 3888,
                    totalTransactionAmount: 2_897_237.12,
                  },
                },
                commission: 388.8,
                fees: {
                  chargedFees: {
                    gatewayFeeTotal: 0,
                    salesFeeTotal: 0,
                    transactionFeeTotal: 388.8,
                    refundFeeTotal: 0,
                    rejected1FeeTotal: 0,
                    minimumFeeTotal: 0,
                    partialReturnFeeTotal: 0,
                  },
                  transactionCommission: 388.8,
                  salesCommission: 0,
                  rates: {
                    combineTotal: 2_556_234.12,
                    isCumulative: false,
                    gatewayFee: 0,
                    transactionTierItem: {
                      minAmount: 0,
                      maxAmount: 4_000_000,
                      salesFee: 0,
                      transactionFee: 0.1,
                    },
                  },
                },
                merchantName: "Merchant 2",
                serviceNumber: "9990000008",
                rateDeterminingInterval: {
                  fromDate: { year: 2024, month: 12, day: 1 },
                  toDate: { year: 2024, month: 12, day: 31 },
                },
              },
            },
          },
          trueUp: {
            totalChargedFees: {
              gatewayFeeTotal: 0,
              salesFeeTotal: 0,
              transactionFeeTotal: 578.32,
              refundFeeTotal: 0,
              rejected1FeeTotal: 0,
              minimumFeeTotal: 0,
              partialReturnFeeTotal: 0,
            },
            totalTransactionSummary: {
              transactionCount: 6257,
              totalTransactionAmount: 4_686_358.23,
            },
            platformId: 13,
            platformDescription: "Real-time Interac e-Transfer Pay-Out (RTO)",
            platformDisplaySequence: 6,
            isContractChange: false,
            feesBreakdown: {
              totalCommission: 578.32,
              totalTransactionCommission: 578.32,
              totalSalesCommission: 0,
              settlementType: "Commision Rate",
              interval: {
                fromDate: { year: 2025, month: 1, day: 1 },
                toDate: { year: 2025, month: 1, day: 31 },
              },
              merchantBreakdown: {
                "7": {
                  feeCollectedFromMerchant: {
                    totalCollectedFee: 7107,
                    meta: {
                      transactionCount: 2369,
                      totalTransactionAmount: 1_789_121.11,
                    },
                  },
                  commission: 189.52,
                  fees: {
                    chargedFees: {
                      gatewayFeeTotal: 0,
                      salesFeeTotal: 0,
                      transactionFeeTotal: 189.52,
                      refundFeeTotal: 0,
                      rejected1FeeTotal: 0,
                      minimumFeeTotal: 0,
                      partialReturnFeeTotal: 0,
                    },
                    transactionCommission: 189.52,
                    salesCommission: 0,
                    rates: {
                      combineTotal: 2_556_234.12,
                      isCumulative: false,
                      gatewayFee: 0,
                      transactionTierItem: {
                        minAmount: 0,
                        maxAmount: 4_000_000,
                        salesFee: 0,
                        transactionFee: 0.08,
                      },
                    },
                  },
                  merchantName: "Merchant 1",
                  serviceNumber: "9990000007",
                  rateDeterminingInterval: {
                    fromDate: { year: 2025, month: 1, day: 1 },
                    toDate: { year: 2025, month: 1, day: 31 },
                  },
                },
                "8": {
                  feeCollectedFromMerchant: {
                    totalCollectedFee: 11_664,
                    meta: {
                      transactionCount: 3888,
                      totalTransactionAmount: 2_897_237.12,
                    },
                  },
                  commission: 388.8,
                  fees: {
                    chargedFees: {
                      gatewayFeeTotal: 0,
                      salesFeeTotal: 0,
                      transactionFeeTotal: 388.8,
                      refundFeeTotal: 0,
                      rejected1FeeTotal: 0,
                      minimumFeeTotal: 0,
                      partialReturnFeeTotal: 0,
                    },
                    transactionCommission: 388.8,
                    salesCommission: 0,
                    rates: {
                      combineTotal: 2_556_234.12,
                      isCumulative: false,
                      gatewayFee: 0,
                      transactionTierItem: {
                        minAmount: 0,
                        maxAmount: 4_000_000,
                        salesFee: 0,
                        transactionFee: 0.1,
                      },
                    },
                  },
                  merchantName: "Merchant 2",
                  serviceNumber: "9990000008",
                  rateDeterminingInterval: {
                    fromDate: { year: 2025, month: 1, day: 1 },
                    toDate: { year: 2025, month: 1, day: 31 },
                  },
                },
              },
            },
          },
        },
      },
    };

    expect(result).toEqual(expectedResult);
  });

  it("gets transactions total for each merchant individually if split", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: mockNonMerchantSettlementConfig,
        merchantsSettlementConfig: mockMerchantsSettlementConfig,
      },
      mockInterval,
      mockToken
    );

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(2);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[0]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [mockMerchantsSettlementConfig[1]!.serviceNumber],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[7]?.fees.rates.isCumulative
    ).toBe(false);
    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[8]?.fees.rates.isCumulative
    ).toBe(false);
  });

  it("gets cumulative transactions total if cumulative", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const merchantsSettlementConfigCumulative = structuredClone(
      mockMerchantsSettlementConfig
    );

    merchantsSettlementConfigCumulative[0]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = true;
    merchantsSettlementConfigCumulative[0]!.platformConfigurations[
      "RTO"
    ]![0]!.agent!.isCumulative = true;

    merchantsSettlementConfigCumulative[1]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = true;
    merchantsSettlementConfigCumulative[1]!.platformConfigurations[
      "RTO"
    ]![0]!.agent!.isCumulative = true;

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: mockNonMerchantSettlementConfig,
        merchantsSettlementConfig: merchantsSettlementConfigCumulative,
      },
      mockInterval,
      mockToken
    );

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(1);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [
        mockMerchantsSettlementConfig[0]!.serviceNumber,
        mockMerchantsSettlementConfig[1]!.serviceNumber,
      ],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[7]?.fees.rates.isCumulative
    ).toBe(true);
    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[8]?.fees.rates.isCumulative
    ).toBe(true);
  });

  it("gets cumulative and combined service numbers transactions total if combined service numbers enabled", async () => {
    vi.mocked(calculateMerchantSettlement)
      .mockResolvedValueOnce(mockMerchantSettlement1)
      .mockResolvedValueOnce(mockMerchantSettlement2);

    vi.mocked(getTransactionsTotalAmount).mockResolvedValue(
      mockTransactionsTotals
    );

    const combineConfig = {
      ...mockNonMerchantSettlementConfig,
      isCombineMultipleServices: true,
      combinedMerchantServiceNumbers: ["8881111111", "8882222222"],
    };

    const merchantsSettlementConfigCumulative = structuredClone(
      mockMerchantsSettlementConfig
    );

    merchantsSettlementConfigCumulative[0]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = true;
    merchantsSettlementConfigCumulative[0]!.platformConfigurations[
      "RTO"
    ]![0]!.agent!.isCumulative = true;

    merchantsSettlementConfigCumulative[1]!.platformConfigurations[
      "ETI"
    ]![0]!.agent!.isCumulative = true;
    merchantsSettlementConfigCumulative[1]!.platformConfigurations[
      "RTO"
    ]![0]!.agent!.isCumulative = true;

    const result = await calculateNonMerchantSettlement(
      {
        nonMerchantSettlementConfig: combineConfig,
        merchantsSettlementConfig: merchantsSettlementConfigCumulative,
      },
      mockInterval,
      mockToken
    );

    expect(getTransactionsTotalAmount).toHaveBeenCalledTimes(1);
    expect(getTransactionsTotalAmount).toHaveBeenCalledWith(
      [
        mockMerchantsSettlementConfig[0]!.serviceNumber,
        mockMerchantsSettlementConfig[1]!.serviceNumber,
        ...combineConfig.combinedMerchantServiceNumbers,
      ],
      startOfPreviousMonthOnly(mockInterval.fromDate),
      endOfPreviousMonthOnly(mockInterval.toDate),
      mockToken
    );

    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[7]?.fees.rates.isCumulative
    ).toBe(true);
    expect(
      (result.settlement["ETI"]?.feesBreakdown as NonMerchantFeesBreakdown)
        .merchantBreakdown[8]?.fees.rates.isCumulative
    ).toBe(true);
  });
});
