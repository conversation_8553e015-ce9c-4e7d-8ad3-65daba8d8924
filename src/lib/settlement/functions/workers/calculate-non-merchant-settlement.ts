import { settlementTypes, type SettlementType } from "@constants/settlement";
import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import {
  type NonMerchantPlatformConfiguration,
  type MerchantPlatformMember,
  type NonMerchantType,
  type MerchantSettlementConfiguration,
  type NonMerchantSettlementConfiguration,
  type TierItem,
} from "@lib/settlement/repository/types";

import { calculateMerchantSettlement } from "./calculate-merchant-settlement";
import { type SettlementResult, type PlatformSettlementDetail } from "./types";
import { getTransactionsTotalAmount } from "../../../../services/blusky/transactions";
import {
  type CalculationOptions,
  type DeterminedInterval,
} from "../../../../types/settlement";
import {
  endOfPreviousMonthOnly,
  type DateOnly,
  startOfPreviousMonthOnly,
  getHigherDate,
  isSameDay,
} from "../../../../utils/date-only";
import { SettlementError } from "../../../../utils/errors/settlement-error";
import { roundToDecimalPlaces } from "../../../../utils/math";
import {
  calculateFeeFromPartial,
  type ChargedFees,
  sumChargedFees,
} from "../helpers/fees";
import { determineBuyRate, determineCommissionRate } from "../helpers/rates";
import { type TransactionsSummary } from "../helpers/transactions-summary";

type FeeCollectedFromMerchant = {
  totalCollectedFee: number;
  meta: Pick<
    TransactionsSummary,
    "transactionCount" | "totalTransactionAmount"
  >;
};

type CommissionWithBreakdown = {
  totalCommission: number;
  totalTransactionCommission: number;
  totalSalesCommission: number;
  settlementType: string;
  interval: { fromDate: DateOnly; toDate: DateOnly };
  merchantBreakdown: Record<
    number,
    MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
    }
  >;
};

type MerchantsCommissionWithTrueup = Record<
  number,
  {
    merchantName: string;
    serviceNumber: string;
    data: {
      merchantCommission: MerchantPlatformCommission;
      rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
    };
    trueup?: {
      merchantCommission: MerchantPlatformCommission;
      rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
    };
  }
>;

type MerchantPlatformCommission = {
  feeCollectedFromMerchant: FeeCollectedFromMerchant;
  commission: number;
  fees: {
    chargedFees: ChargedFees;
    transactionCommission: number;
    salesCommission: number;
    rates: {
      combineTotal: number;
      isCumulative: boolean;
      gatewayFee: number;
      transactionTierItem?: TierItem;
      salesTierItem?: TierItem;
    };
  };
};

const calculateNonMerchantSettlement = async (
  config: {
    nonMerchantSettlementConfig: NonMerchantSettlementConfiguration;
    merchantsSettlementConfig: MerchantSettlementConfiguration[];
  },
  interval: DeterminedInterval,
  token: string,
  options?: CalculationOptions
): Promise<SettlementResult> => {
  const settlementResult: SettlementResult = { settlement: {} };
  const { nonMerchantSettlementConfig, merchantsSettlementConfig } = config;
  const { settlementType } = nonMerchantSettlementConfig;

  _validateSettlementType(settlementType);

  const platformsCommission = await _calculateCommissions(
    nonMerchantSettlementConfig,
    merchantsSettlementConfig,
    interval,
    token,
    options
  );

  for (const [platformCode, allCommissions] of Object.entries(
    platformsCommission
  )) {
    const {
      merchantBreakdown,
      platformId,
      platformDescription,
      platformDisplaySequence,
    } = allCommissions;

    const platformSettlement = _buildPlatformSettlementDetails({
      settlementType,
      platformId,
      platformDescription,
      platformDisplaySequence,
      merchantsCommissionData: merchantBreakdown,
      interval,
    });

    settlementResult.settlement[platformCode] = platformSettlement;
  }

  return settlementResult;
};

function _buildPlatformSettlementDetails({
  settlementType,
  platformId,
  platformDescription,
  platformDisplaySequence,
  merchantsCommissionData,
  interval,
}: {
  settlementType: SettlementType;
  platformId: number;
  platformDescription: string;
  platformDisplaySequence: number;
  merchantsCommissionData: MerchantsCommissionWithTrueup;
  interval: DeterminedInterval;
}): PlatformSettlementDetail & {
  trueUp?: PlatformSettlementDetail;
} {
  const chargedFeesArray: ChargedFees[] = [];
  const feeCollectedArray: FeeCollectedFromMerchant[] = [];
  let totalCommission = 0;
  let totalTransactionCommission = 0;
  let totalSalesCommission = 0;
  const merchantBreakdown: Record<
    number,
    MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
    }
  > = {};

  // For trueup if exists
  const chargedFeesArrayTrueup: ChargedFees[] = [];
  const feeCollectedArrayTrueup: FeeCollectedFromMerchant[] = [];
  let totalCommissionTrueup = 0;
  let totalTransactionCommissionTrueup = 0;
  let totalSalesCommissionTrueup = 0;
  const merchantBreakdownTrueup: Record<
    number,
    MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
    }
  > = {};

  for (const [merchantId, merchantData] of Object.entries(
    merchantsCommissionData
  )) {
    const { data, merchantName, serviceNumber } = merchantData;
    const { merchantCommission, rateDeterminingInterval } = data;

    chargedFeesArray.push(merchantCommission.fees.chargedFees);
    feeCollectedArray.push(merchantCommission.feeCollectedFromMerchant);

    totalCommission += merchantCommission.commission;
    totalTransactionCommission += merchantCommission.fees.transactionCommission;
    totalSalesCommission += merchantCommission.fees.salesCommission;

    merchantBreakdown[Number(merchantId)] = {
      ...merchantCommission,
      merchantName,
      serviceNumber,
      rateDeterminingInterval,
    };

    // Trueup commission accumulation if trueup data exists.
    if (merchantData?.trueup) {
      const {
        merchantCommission: trueupCommission,
        rateDeterminingInterval: trueupInterval,
      } = merchantData.trueup;

      chargedFeesArrayTrueup.push(trueupCommission.fees.chargedFees);
      feeCollectedArrayTrueup.push(trueupCommission.feeCollectedFromMerchant);

      totalCommissionTrueup += trueupCommission.commission;
      totalTransactionCommissionTrueup +=
        trueupCommission.fees.transactionCommission;
      totalSalesCommissionTrueup += trueupCommission.fees.salesCommission;

      merchantBreakdownTrueup[Number(merchantId)] = {
        ...trueupCommission,
        merchantName,
        serviceNumber,
        rateDeterminingInterval: trueupInterval,
      };
    }
  }

  const totalFeeCollectedFromMerchant = _sumFeeCollected(feeCollectedArray);
  const totalChargedFees = sumChargedFees(chargedFeesArray);

  const platformSettlement: PlatformSettlementDetail = {
    totalChargedFees,
    totalTransactionSummary:
      totalFeeCollectedFromMerchant.meta as TransactionsSummary,
    platformId,
    platformDescription,
    platformDisplaySequence,
    isContractChange: false,
    feesBreakdown: {
      totalCommission: roundToDecimalPlaces(totalCommission),
      totalTransactionCommission: roundToDecimalPlaces(
        totalTransactionCommission
      ),
      totalSalesCommission: roundToDecimalPlaces(totalSalesCommission),
      settlementType,
      interval,
      merchantBreakdown,
    },
  };

  // Calculate totals for trueup settlement if any trueup data exists.
  let trueupSettlement: PlatformSettlementDetail | undefined;

  if (feeCollectedArrayTrueup.length > 0) {
    trueupSettlement = {
      totalChargedFees: sumChargedFees(chargedFeesArrayTrueup),
      totalTransactionSummary: _sumFeeCollected(feeCollectedArrayTrueup)
        .meta as TransactionsSummary,
      platformId,
      platformDescription,
      platformDisplaySequence,
      isContractChange: false,
      feesBreakdown: {
        totalCommission: roundToDecimalPlaces(totalCommissionTrueup),
        totalTransactionCommission: roundToDecimalPlaces(
          totalTransactionCommissionTrueup
        ),
        totalSalesCommission: roundToDecimalPlaces(totalSalesCommissionTrueup),
        settlementType,
        interval,
        merchantBreakdown: merchantBreakdownTrueup,
      },
    };
  }

  return {
    ...platformSettlement,
    ...(trueupSettlement && { trueUp: trueupSettlement }),
  };
}

/**
 * Helper function to select the non-merchant configuration from an array
 * of MerchantPlatformMember objects for a given platform. It returns the
 * candidate (agent, integrator, or subAgent) with the highest fromDate.
 */
function _selectNonMerchantPlatformConfiguration(
  members: MerchantPlatformMember[],
  nonMerchantType: NonMerchantType
): NonMerchantPlatformConfiguration | undefined {
  let selectedConfig: NonMerchantPlatformConfiguration | undefined;
  let highestFromDate: DateOnly | undefined;

  for (const member of members) {
    let candidate: NonMerchantPlatformConfiguration | undefined;

    switch (nonMerchantType) {
      case "Agent": {
        candidate = member.agent;

        break;
      }

      case "Integrator": {
        candidate = member.integrator;

        break;
      }

      case "Sub Agent": {
        candidate = member.subAgent;

        break;
      }
    }

    if (candidate && member.fromDate) {
      if (highestFromDate) {
        const newHighest = getHigherDate(highestFromDate, member.fromDate);

        // Update only if the candidate's fromDate is strictly higher than the current highest.
        if (
          newHighest === member.fromDate &&
          !isSameDay(newHighest, highestFromDate)
        ) {
          highestFromDate = member.fromDate;
          selectedConfig = candidate;
        }
      } else {
        highestFromDate = member.fromDate;
        selectedConfig = candidate;
      }
    }
  }

  return selectedConfig;
}

function _sumTransactionAmountsAndCounts(
  transactionSummary: TransactionsSummary
): {
  transactionCount: number;
  totalTransactionAmount: number;
} {
  let transactionCount = 0;
  let transactionAmount = 0;

  transactionCount +=
    transactionSummary.transactionCount +
    transactionSummary.minimumAmountCount +
    transactionSummary.partialReturnCount +
    transactionSummary.rejected1Count +
    transactionSummary.refundCount;
  transactionAmount +=
    transactionSummary.totalTransactionAmount +
    transactionSummary.totalMinimumAmount +
    transactionSummary.totalPartialReturnAmount +
    transactionSummary.totalRejected1Amount +
    transactionSummary.totalRefundAmount;

  return {
    transactionCount,
    totalTransactionAmount: roundToDecimalPlaces(transactionAmount),
  };
}

function _sumAllFees(chargedFees: ChargedFees) {
  let totalChargedFee = 0;

  for (const key in chargedFees) {
    if (Object.hasOwn(chargedFees, key)) {
      const value = (chargedFees as Record<string, number>)[key];

      if (typeof value === "number") {
        totalChargedFee += value;
      }
    }
  }

  return roundToDecimalPlaces(totalChargedFee);
}

function _sumFeeCollected(
  fees: FeeCollectedFromMerchant[]
): FeeCollectedFromMerchant {
  const merged: FeeCollectedFromMerchant = {
    totalCollectedFee: 0,
    meta: {
      transactionCount: 0,
      totalTransactionAmount: 0,
    },
  };

  for (const fee of fees) {
    merged.totalCollectedFee += fee.totalCollectedFee;
    merged.meta.transactionCount += fee.meta.transactionCount;
    merged.meta.totalTransactionAmount += fee.meta.totalTransactionAmount;
  }

  merged.totalCollectedFee = roundToDecimalPlaces(merged.totalCollectedFee);
  merged.meta.totalTransactionAmount = roundToDecimalPlaces(
    merged.meta.totalTransactionAmount
  );

  return merged;
}

/**
 * Validates that the provided settlement type is one of the allowed types.
 * Throws an error if the settlement type is invalid.
 *
 * @param settlementType - The settlement type to validate.
 */
function _validateSettlementType(
  settlementType: string
): asserts settlementType is SettlementType {
  const allowedSettlementTypes = Object.values(settlementTypes);

  if (!allowedSettlementTypes.includes(settlementType as SettlementType)) {
    throw new SettlementError(
      `Invalid settlementType: ${settlementType}. Must be one of: ${allowedSettlementTypes.join(", ")}`
    );
  }
}

// eslint-disable-next-line max-params
async function _calculateCommissions(
  nonMerchantSettlementConfig: NonMerchantSettlementConfiguration,
  merchantsSettlementConfig: MerchantSettlementConfiguration[],
  interval: DeterminedInterval,
  token: string,
  options?: CalculationOptions
): Promise<
  Record<
    string,
    {
      platformId: number;
      platformDescription: string;
      platformDisplaySequence: number;
      merchantBreakdown: MerchantsCommissionWithTrueup;
    }
  >
> {
  const { settlementType, nonMerchantType } = nonMerchantSettlementConfig;
  const result: Record<
    string,
    {
      platformId: number;
      platformDescription: string;
      platformDisplaySequence: number;
      merchantBreakdown: MerchantsCommissionWithTrueup;
    }
  > = {};

  const merchantServiceNumbers = merchantsSettlementConfig.map(
    (m) => m.serviceNumber
  );
  const combinedServiceNumbers: string[] =
    (nonMerchantSettlementConfig?.isCombineMultipleServices &&
      nonMerchantSettlementConfig?.combinedMerchantServiceNumbers) ||
    [];

  const combinedTotal = await _getCombineTotals(
    [...merchantServiceNumbers, ...combinedServiceNumbers],
    interval,
    token,
    options
  );

  for (const merchantConfig of merchantsSettlementConfig) {
    let merchantSettlement: SettlementResult = {
      settlement: {},
    };

    try {
      // eslint-disable-next-line no-await-in-loop
      merchantSettlement = await calculateMerchantSettlement({
        merchantConfig,
        interval,
        token,
        options: options ?? {},
      });
    } catch (error) {
      if (error instanceof SettlementError && error.isSkipped) {
        continue;
      }
    }

    for (const [platformCode, platformSettlement] of Object.entries(
      merchantSettlement.settlement
    )) {
      const platformConfigs =
        merchantConfig.platformConfigurations[platformCode] ?? [];

      const nonMerchantPlatformConfig = _selectNonMerchantPlatformConfiguration(
        platformConfigs,
        nonMerchantType.customerTypeName as NonMerchantType
      );

      if (!nonMerchantPlatformConfig) {
        continue;
      }

      if (
        nonMerchantSettlementConfig.isCombineMultipleServices &&
        !nonMerchantPlatformConfig.isCumulative
      ) {
        throw new SettlementError(
          `${nonMerchantSettlementConfig.customerName} merchants are configured as split, please update before combining volume.`
        );
      }

      const total =
        _getCumulativeOrSplitTotal(
          combinedTotal.data.totals,
          nonMerchantPlatformConfig.isCumulative
        )[platformCode as Platform] ??
        _sumTransactionAmountsAndCounts(
          platformSettlement.totalTransactionSummary
        ).totalTransactionAmount ??
        0;

      // Calculate the merchant commission
      const merchantCommission = _calculateCommissionForSingleMerchant({
        settlementType: settlementType as SettlementType,
        platform: platformCode as Platform,
        platformSettlement,
        configuration: nonMerchantPlatformConfig,
        rateDeterminingTotal: total,
      });

      // Calculate trueup commission if available
      let trueupCommission: MerchantPlatformCommission | undefined;

      if (platformSettlement?.trueUp) {
        const trueupTotal =
          _getCumulativeOrSplitTotal(
            combinedTotal.trueup!.totals,
            nonMerchantPlatformConfig.isCumulative
          )[platformCode as Platform] ??
          _sumTransactionAmountsAndCounts(
            platformSettlement.totalTransactionSummary
          ).totalTransactionAmount ??
          0;

        trueupCommission = _calculateCommissionForSingleMerchant({
          settlementType: settlementType as SettlementType,
          platform: platformCode as Platform,
          platformSettlement: platformSettlement.trueUp,
          configuration: nonMerchantPlatformConfig,
          rateDeterminingTotal: trueupTotal,
        });
      }

      result[platformCode] ||= {
        platformId: platformSettlement.platformId,
        platformDescription: platformSettlement.platformDescription,
        platformDisplaySequence: platformSettlement.platformDisplaySequence,
        merchantBreakdown: {},
      };

      result[platformCode]!.merchantBreakdown[merchantConfig.customerId] = {
        merchantName: merchantConfig.customerName,
        serviceNumber: merchantConfig.serviceNumber,
        data: {
          merchantCommission,
          rateDeterminingInterval: combinedTotal.data.rateDeterminingInterval,
        },
        ...(trueupCommission && {
          trueup: {
            merchantCommission: trueupCommission,
            rateDeterminingInterval:
              combinedTotal.trueup!.rateDeterminingInterval,
          },
        }),
      };
    }
  }

  return result;
}

async function _getCombineTotals(
  serviceNumbers: string[],
  interval: { fromDate: DateOnly; toDate: DateOnly },
  token: string,
  options?: CalculationOptions
): Promise<{
  data: {
    totals: Record<Platform, number>;
    rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
  };
  trueup?: {
    totals: Record<Platform, number>;
    rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
  };
}> {
  const rateDeterminingInterval = {
    fromDate: options?.rateDeterminingInterval
      ? options.rateDeterminingInterval.fromDate
      : startOfPreviousMonthOnly(interval.fromDate),
    toDate: options?.rateDeterminingInterval
      ? options.rateDeterminingInterval.toDate
      : endOfPreviousMonthOnly(interval.fromDate),
  };

  const totals = await getTransactionsTotalAmount(
    serviceNumbers,
    rateDeterminingInterval.fromDate,
    rateDeterminingInterval.toDate,
    token
  );

  // Calculate trueup if available
  let trueup:
    | {
        totals: Record<Platform, number>;
        rateDeterminingInterval: { fromDate: DateOnly; toDate: DateOnly };
      }
    | undefined;

  if (options?.trueUp) {
    const trueupInterval = options.trueUp.rateDeterminingInterval;
    const trueupTotals = await getTransactionsTotalAmount(
      serviceNumbers,
      trueupInterval.fromDate,
      trueupInterval.toDate,
      token
    );

    trueup = {
      totals: trueupTotals,
      rateDeterminingInterval: trueupInterval,
    };
  }

  return {
    data: {
      totals,
      rateDeterminingInterval,
    },
    ...(trueup && {
      trueup,
    }),
  };
}

/**
 * Returns a combined total for RFM and ETI if isCumulative is true,
 * otherwise returns the original combineTotals.
 *
 * NOTE: The current logic for cumulative totals is temporary.
 *       Update this function once the final logic is provided by the Finance department.
 */
function _getCumulativeOrSplitTotal(
  combineTotals: Record<Platform, number>,
  isCumulative: boolean
): Record<Platform, number> {
  const totals = { ...combineTotals };

  if (isCumulative) {
    const eti = totals.ETI || 0;
    const rfm = totals.RFM || 0;
    const sum = eti + rfm;
    totals.ETI = sum;
    totals.RFM = sum;
  }

  return totals;
}

function _calculateFee({
  feeCollectedFromMerchant,
  settlementType,
  platform,
  configuration,
  rateDeterminingTotal,
}: {
  feeCollectedFromMerchant: FeeCollectedFromMerchant;
  settlementType: SettlementType;
  platform: Platform;
  configuration: Omit<NonMerchantPlatformConfiguration, "nonMerchantId">;
  rateDeterminingTotal: number;
}): {
  chargedFees: ChargedFees;
  transactionCommission: number;
  salesCommission: number;
  transactionTierItem?: TierItem;
  salesTierItem?: TierItem;
} {
  const buyRate = determineBuyRate(rateDeterminingTotal, configuration);
  const commissionRate = determineCommissionRate(
    rateDeterminingTotal,
    configuration
  );
  const transactionTierItem = commissionRate.tierItem;
  const salesTierItem = buyRate.tierItem;

  // Hard coded to use only sales fee for pay-ins and only transaction fee for pay-outs
  // Update in FA-999 to allow for more flexibility
  if (payInGroup.includes(platform)) {
    commissionRate.transactionFee = 0;
  } else if (payOutGroup.includes(platform)) {
    commissionRate.salesFee = 0;
  }

  switch (settlementType) {
    case settlementTypes.buyRate: {
      const chargedFees = calculateFeeFromPartial(
        feeCollectedFromMerchant.meta,
        buyRate
      );

      return {
        chargedFees,
        transactionCommission: 0,
        salesCommission: roundToDecimalPlaces(
          feeCollectedFromMerchant.totalCollectedFee - _sumAllFees(chargedFees)
        ),
        salesTierItem,
      };
    }

    case settlementTypes.commissionRate: {
      const chargedFees = calculateFeeFromPartial(
        feeCollectedFromMerchant.meta,
        commissionRate
      );

      return {
        transactionCommission: _sumAllFees(chargedFees),
        chargedFees,
        salesCommission: 0,
        transactionTierItem,
      };
    }

    case settlementTypes.combination: {
      const buyRateFee = calculateFeeFromPartial(
        feeCollectedFromMerchant.meta,
        buyRate
      );
      const commissionFee = calculateFeeFromPartial(
        feeCollectedFromMerchant.meta,
        commissionRate
      );

      return {
        chargedFees: buyRateFee,
        transactionCommission: _sumAllFees(commissionFee),
        salesCommission: roundToDecimalPlaces(
          feeCollectedFromMerchant.totalCollectedFee - _sumAllFees(buyRateFee)
        ),
        transactionTierItem,
        salesTierItem,
      };
    }
  }
}

function _calculateCommissionForSingleMerchant({
  settlementType,
  platform,
  platformSettlement,
  configuration,
  rateDeterminingTotal,
}: {
  settlementType: SettlementType;
  platform: Platform;
  platformSettlement: PlatformSettlementDetail;
  configuration: Omit<NonMerchantPlatformConfiguration, "nonMerchantId">;
  rateDeterminingTotal: number;
}): MerchantPlatformCommission {
  const feeCollectedFromMerchant: FeeCollectedFromMerchant = {
    totalCollectedFee: _sumAllFees(platformSettlement.totalChargedFees),
    meta: _sumTransactionAmountsAndCounts(
      platformSettlement.totalTransactionSummary
    ),
  };

  const chargedFeesWithMeta = _calculateFee({
    feeCollectedFromMerchant,
    settlementType,
    platform,
    configuration,
    rateDeterminingTotal,
  });

  const commission = roundToDecimalPlaces(
    chargedFeesWithMeta.transactionCommission +
      chargedFeesWithMeta.salesCommission
  );

  return {
    feeCollectedFromMerchant,
    commission,
    fees: {
      chargedFees: chargedFeesWithMeta.chargedFees,
      transactionCommission: chargedFeesWithMeta.transactionCommission,
      salesCommission: chargedFeesWithMeta.salesCommission,
      rates: {
        combineTotal: roundToDecimalPlaces(rateDeterminingTotal),
        isCumulative: configuration.isCumulative,
        gatewayFee: configuration.gatewayFeePercentage,
        ...(chargedFeesWithMeta?.salesTierItem && {
          salesTierItem: chargedFeesWithMeta.salesTierItem,
        }),
        ...(chargedFeesWithMeta?.transactionTierItem && {
          transactionTierItem: chargedFeesWithMeta.transactionTierItem,
        }),
      },
    },
  };
}

export {
  calculateNonMerchantSettlement,
  type CommissionWithBreakdown,
  type MerchantPlatformCommission,
};
