import { showAllSettlements } from "../read/services";
import { SettlementStateEnum } from "../read/types";

import type { PrismaClient } from "@prisma/client";

export type SettlementConflict = {
  customerCustomerTypeId: number;
  customerName: string;
  serviceNumber: string;
  fromDate: string;
  toDate: string;
  status: "approved" | "generated";
};

export type SettlementConflictResult = {
  hasConflicts: boolean;
  approvedSettlements: SettlementConflict[];
  generatedSettlements: SettlementConflict[];
  approvedCustomerCustomerTypeIds: number[];
};

// Detect settlements in the given period for a frequency split into approved vs generated
export const checkForSettlementConflicts = async (
  prisma: PrismaClient,
  fromDate: string,
  toDate: string,
  frequencyName: string
): Promise<SettlementConflictResult> => {
  const { settlements } = await showAllSettlements(prisma, {
    offset: 0,
    limit: 10_000,
    sortKey: "fromDate",
    sortOrder: "desc",
    nameOrServiceNumber: "",
    clientType: "",
    displayAdjusted: "",
    state: "",
    status: "",
    frequencyName,
    startDate: fromDate,
    endDate: toDate,
  });
  console.log(4444444, fromDate, toDate, frequencyName);
  console.log(5555555, settlements.length);

  const approvedSettlements: SettlementConflict[] = [];
  const generatedSettlements: SettlementConflict[] = [];

  for (const settlement of settlements) {
    const {
      customerCustomerTypeId,
      customerName,
      serviceNumber,
      fromDate,
      toDate,
      status,
    } = settlement;

    const isApproved = status === SettlementStateEnum.APPROVAL_SUCCESS;

    const currentConflictSettlement: SettlementConflict = {
      customerCustomerTypeId,
      customerName,
      serviceNumber,
      fromDate,
      toDate,
      status: isApproved ? "approved" : "generated",
    };

    if (isApproved) {
      approvedSettlements.push(currentConflictSettlement);
    } else {
      generatedSettlements.push(currentConflictSettlement);
    }
  }

  const approvedCustomerCustomerTypeIds = approvedSettlements.map(
    (settlement) => settlement.customerCustomerTypeId
  );

  return {
    hasConflicts:
      approvedSettlements.length > 0 || generatedSettlements.length > 0,
    approvedSettlements,
    generatedSettlements,
    approvedCustomerCustomerTypeIds,
  };
};
