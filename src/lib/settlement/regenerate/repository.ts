import { type PrismaClient, type Prisma } from "@prisma/client";

import { init } from "../constants/default-parameters";

export const incrementRegenerationCountAndChangeStatus = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  customerSettlementIds: number[]
) => {
  const result = await prisma.customerSettlements.updateMany({
    where: {
      customerSettlementsId: {
        in: customerSettlementIds,
      },
    },
    data: {
      regenerationCount: { increment: 1 },
      updatedAt: new Date(),
      status: init,
    },
  });

  return result;
};

export const getAllSettlementsIdsByCustomerAndPeriod = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  customerId: number,
  fromDate: Date,
  toDate: Date
) => {
  const settlements = await prisma.customerSettlements.findMany({
    where: {
      customerId,
      fromDate,
      toDate,
    },
  });

  return settlements.map((settlement) => settlement.customerSettlementsId);
};

export const cancelWiresForSettlements = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  settlementsIds: number[],
  userId: number
) => {
  const cancelledWires = await prisma.wire.updateMany({
    where: {
      settlementId: {
        in: settlementsIds,
      },
      deletedAt: null,
    },
    data: {
      isCancelled: true,
      cancelledById: userId,
      cancelledAt: new Date(),
      updatedAt: new Date(),
    },
  });

  return cancelledWires;
};

export const getSettlementEmails = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  settlementsIds: number[]
) => {
  // Get all emails associated with wires for this settlement
  const emails = await prisma.email.findMany({
    select: {
      emailId: true,
      sentAt: true,
      deletedAt: true,
    },
    where: {
      wire: {
        settlementId: {
          in: settlementsIds,
        },
        deletedAt: null,
      },
      deletedAt: null,
    },
  });

  return emails;
};

export const softDeleteUnsentEmails = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  emails: Array<{
    emailId: number;
  }>
) => {
  await prisma.email.updateMany({
    where: {
      emailId: {
        in: emails.map((email) => email.emailId),
      },
      sentAt: null,
      deletedAt: null,
    },
    data: {
      deletedAt: new Date(),
      updatedAt: new Date(),
    },
  });
};

export const addAuditRecord = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  {
    table,
    settlementsIds,
    preValues,
    postValues,
    type,
    comment,
    userId,
    createdAt = new Date(),
    updatedAt = new Date(),
  }: {
    table: string;
    settlementsIds: string;
    preValues: string;
    postValues: string;
    type: string;
    comment?: string;
    userId: number;
    createdAt?: Date;
    updatedAt?: Date;
  }
) => {
  await prisma.audit.create({
    data: {
      table,
      tableId: settlementsIds,
      preValues,
      postValues,
      type,
      comment: comment ?? null,
      userId,
      createdAt,
      updatedAt,
    },
  });
};
