import { type Prisma } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import {
  incrementRegenerationCountAndChangeStatus,
  getAllSettlementsIdsByCustomerAndPeriod,
  cancelWiresForSettlements,
  getSettlementEmails,
  softDeleteUnsentEmails,
  addAuditRecord,
} from "./repository";

describe("Regenerate Settlement Repository", () => {
  let mockTransaction: Prisma.TransactionClient;

  beforeEach(() => {
    mockTransaction = {
      customerSettlements: {
        updateMany: vi.fn(),
        findMany: vi.fn(),
      },
      wire: {
        updateMany: vi.fn(),
      },
      email: {
        findMany: vi.fn(),
        updateMany: vi.fn(),
      },
      audit: {
        create: vi.fn(),
      },
    } as unknown as Prisma.TransactionClient;

    vi.clearAllMocks();
  });

  describe("incrementRegenerationCountAndChangeStatus", () => {
    it("should increment regeneration count and change status to INIT", async () => {
      const customerSettlementIds = [1, 2, 3];
      const mockResult = { count: 3 };

      vi.mocked(
        mockTransaction.customerSettlements.updateMany
      ).mockResolvedValue(mockResult);

      const result = await incrementRegenerationCountAndChangeStatus(
        mockTransaction,
        customerSettlementIds
      );

      expect(
        mockTransaction.customerSettlements.updateMany
      ).toHaveBeenCalledWith({
        where: {
          customerSettlementsId: {
            in: customerSettlementIds,
          },
        },
        data: {
          regenerationCount: { increment: 1 },
          updatedAt: expect.any(Date) as Date,
          status: "INIT",
        },
      });

      expect(result).toEqual(mockResult);
    });
  });

  describe("getAllSettlementsIdsByCustomerAndPeriod", () => {
    it("should return settlement IDs for customer and period", async () => {
      const customerId = 123;
      const fromDate = new Date("2025-01-01");
      const toDate = new Date("2025-01-31");
      const mockSettlements = [
        { customerSettlementsId: 1 },
        { customerSettlementsId: 2 },
        { customerSettlementsId: 3 },
      ];

      vi.mocked(mockTransaction.customerSettlements.findMany).mockResolvedValue(
        mockSettlements as any
      );

      const result = await getAllSettlementsIdsByCustomerAndPeriod(
        mockTransaction,
        customerId,
        fromDate,
        toDate
      );

      expect(mockTransaction.customerSettlements.findMany).toHaveBeenCalledWith(
        {
          where: {
            customerId,
            fromDate,
            toDate,
          },
        }
      );

      expect(result).toEqual([1, 2, 3]);
    });

    it("should return empty array when no settlements found", async () => {
      const customerId = 123;
      const fromDate = new Date("2025-01-01");
      const toDate = new Date("2025-01-31");

      vi.mocked(mockTransaction.customerSettlements.findMany).mockResolvedValue(
        []
      );

      const result = await getAllSettlementsIdsByCustomerAndPeriod(
        mockTransaction,
        customerId,
        fromDate,
        toDate
      );

      expect(result).toEqual([]);
    });
  });

  describe("cancelWiresForSettlements", () => {
    it("should cancel wires for settlements", async () => {
      const settlementsIds = [1, 2, 3];
      const userId = 123;
      const mockResult = { count: 3 };

      vi.mocked(mockTransaction.wire.updateMany).mockResolvedValue(mockResult);

      const result = await cancelWiresForSettlements(
        mockTransaction,
        settlementsIds,
        userId
      );

      expect(mockTransaction.wire.updateMany).toHaveBeenCalledWith({
        where: {
          settlementId: {
            in: settlementsIds,
          },
          deletedAt: null,
        },
        data: {
          isCancelled: true,
          cancelledById: userId,
          cancelledAt: expect.any(Date) as Date,
          updatedAt: expect.any(Date) as Date,
        },
      });

      expect(result).toEqual(mockResult);
    });
  });

  describe("getSettlementEmails", () => {
    it("should return emails for settlements", async () => {
      const settlementsIds = [1, 2, 3];
      const mockEmails = [
        { emailId: 1, sentAt: null, deletedAt: null },
        { emailId: 2, sentAt: new Date(), deletedAt: null },
        { emailId: 3, sentAt: null, deletedAt: new Date() },
      ];

      vi.mocked(mockTransaction.email.findMany).mockResolvedValue(
        mockEmails as any
      );

      const result = await getSettlementEmails(mockTransaction, settlementsIds);

      expect(mockTransaction.email.findMany).toHaveBeenCalledWith({
        select: {
          emailId: true,
          sentAt: true,
          deletedAt: true,
        },
        where: {
          wire: {
            settlementId: {
              in: settlementsIds,
            },
            deletedAt: null,
          },
          deletedAt: null,
        },
      });

      expect(result).toEqual(mockEmails);
    });
  });

  describe("softDeleteUnsentEmails", () => {
    it("should soft delete unsent emails", async () => {
      const emails = [{ emailId: 1 }, { emailId: 2 }, { emailId: 3 }];

      vi.mocked(mockTransaction.email.updateMany).mockResolvedValue({
        count: 3,
      });

      await softDeleteUnsentEmails(mockTransaction, emails);

      expect(mockTransaction.email.updateMany).toHaveBeenCalledWith({
        where: {
          emailId: {
            in: [1, 2, 3],
          },
          sentAt: null,
          deletedAt: null,
        },
        data: {
          deletedAt: expect.any(Date) as Date,
          updatedAt: expect.any(Date) as Date,
        },
      });
    });
  });

  describe("addAuditRecord", () => {
    it("should add audit record with all required fields", async () => {
      const auditData = {
        table: "customerSettlements",
        settlementsIds: "1,2,3",
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        comment: "Test regeneration",
        userId: 123,
      };

      vi.mocked(mockTransaction.audit.create).mockResolvedValue({} as any);

      await addAuditRecord(mockTransaction, auditData);

      expect(mockTransaction.audit.create).toHaveBeenCalledWith({
        data: {
          table: auditData.table,
          tableId: auditData.settlementsIds,
          preValues: auditData.preValues,
          postValues: auditData.postValues,
          type: auditData.type,
          comment: auditData.comment,
          userId: auditData.userId,
          createdAt: expect.any(Date) as Date,
          updatedAt: expect.any(Date) as Date,
        },
      });
    });

    it("should add audit record with null comment when not provided", async () => {
      const auditData = {
        table: "customerSettlements",
        settlementsIds: "1,2,3",
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        userId: 123,
      };

      vi.mocked(mockTransaction.audit.create).mockResolvedValue({} as any);

      await addAuditRecord(mockTransaction, auditData);

      expect(mockTransaction.audit.create).toHaveBeenCalledWith({
        data: {
          table: auditData.table,
          tableId: auditData.settlementsIds,
          preValues: auditData.preValues,
          postValues: auditData.postValues,
          type: auditData.type,
          comment: null,
          userId: auditData.userId,
          createdAt: expect.any(Date) as Date,
          updatedAt: expect.any(Date) as Date,
        },
      });
    });

    it("should add audit record with custom dates when provided", async () => {
      const customDate = new Date("2025-01-01T00:00:00.000Z");
      const auditData = {
        table: "customerSettlements",
        settlementsIds: "1,2,3",
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        comment: "Test regeneration",
        userId: 123,
        createdAt: customDate,
        updatedAt: customDate,
      };

      vi.mocked(mockTransaction.audit.create).mockResolvedValue({} as any);

      await addAuditRecord(mockTransaction, auditData);

      expect(mockTransaction.audit.create).toHaveBeenCalledWith({
        data: {
          table: auditData.table,
          tableId: auditData.settlementsIds,
          preValues: auditData.preValues,
          postValues: auditData.postValues,
          type: auditData.type,
          comment: auditData.comment,
          userId: auditData.userId,
          createdAt: customDate,
          updatedAt: customDate,
        },
      });
    });
  });
});
