import { createCustomerSettlementGenerationSchedule } from "@lib/customer-settlement-generation-schedule/update/repository";
import { type PrismaClient } from "@prisma/client";

import {
  incrementRegenerationCountAndChangeStatus,
  getAllSettlementsIdsByCustomerAndPeriod,
  getSettlementEmails,
  cancelWiresForSettlements,
  addAuditRecord,
  softDeleteUnsentEmails,
} from "./repository";
import { getSettlementStateAndMessage } from "../read/services/get-settlement-state-and-message";
import { SettlementStateEnum } from "../read/types";
import { getSettlementWires } from "../repository/delete-settlements";
import { getSettlementById } from "../repository/settlement-frequency";

export const regenerateSettlement = async (
  prisma: PrismaClient,
  settlementIds: string[],
  reason: string,
  userId: number
): Promise<void> => {
  await prisma.$transaction(
    async (tx) => {
      const settlementIdsToUnapprove: number[] = [];

      for (const settlementId of settlementIds) {
        // eslint-disable-next-line no-await-in-loop
        const settlement = await getSettlementById(tx, Number(settlementId));

        if (!settlement) {
          throw new Error("Settlement not found");
        }

        const {
          customerCustomerTypeId,
          customerId,
          serviceNumber,
          fromDate,
          toDate,
        } = settlement;

        if (!customerCustomerTypeId) {
          throw new Error("Customer type ID not found");
        }

        // eslint-disable-next-line no-await-in-loop
        const { state } = await getSettlementStateAndMessage(
          {
            customerCustomerTypeId,
            fromDate,
            toDate,
          },
          tx
        );

        // eslint-disable-next-line no-await-in-loop
        const settlementIds = await getAllSettlementsIdsByCustomerAndPeriod(
          tx,
          customerId,
          fromDate,
          toDate
        );

        settlementIdsToUnapprove.push(...settlementIds);

        const isApprovedSettlement =
          state === SettlementStateEnum.APPROVAL_SUCCESS;

        if (isApprovedSettlement) {
          // eslint-disable-next-line no-await-in-loop
          await incrementRegenerationCountAndChangeStatus(
            tx,
            settlementIdsToUnapprove
          );
          // eslint-disable-next-line no-await-in-loop
          await createCustomerSettlementGenerationSchedule(tx, {
            serviceNumber,
            fromDate: fromDate.toISOString(),
            toDate: toDate.toISOString(),
            customerCustomerTypeId,
            generationType: "INITIAL",
            generationStatus: "COMPLETE",
          });
        }
      }

      const wires = await getSettlementWires(settlementIdsToUnapprove, tx);

      const hasApprovedWires = wires.some(
        (wire) => wire.wireApprovedById && !wire.isCancelled
      );

      if (hasApprovedWires) {
        throw new Error(
          "Settlements with approved wires cannot be regenerated"
        );
      }

      await cancelWiresForSettlements(tx, settlementIdsToUnapprove, userId);

      const emails = await getSettlementEmails(tx, settlementIdsToUnapprove);

      const unsentEmails = emails.filter(
        (email) => !email.sentAt && !email.deletedAt
      );

      if (unsentEmails.length > 0) {
        await softDeleteUnsentEmails(tx, unsentEmails);
      }

      await addAuditRecord(tx, {
        table: "customerSettlements",
        settlementsIds: settlementIdsToUnapprove.join(","),
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        comment: reason,
        userId,
      });
    },
    {
      timeout: 60_000, // 60 seconds timeout
      maxWait: 10_000, // 10 seconds max wait
    }
  );
};
