import { type PrismaClient } from "@prisma/client";
import { type FastifyReply, type FastifyRequest } from "fastify";

import { handler } from "./handler";
import * as service from "./service";
import { type RegenerateRequestBody } from "./types";

type MockPrisma = {
  $transaction: ReturnType<typeof vi.fn>;
};

type MockFastifyRequest = FastifyRequest<{ Body: RegenerateRequestBody }> & {
  server: {
    prisma: PrismaClient & MockPrisma;
  };
  userProfile: {
    id: number;
  };
  log: {
    error: ReturnType<typeof vi.fn>;
  };
};

describe("Regenerate Settlement Handler", () => {
  let mockRequest: MockFastifyRequest;
  let mockReply: FastifyReply;

  beforeEach(() => {
    mockRequest = {
      body: {
        reason: "Test reason",
        settlementIds: ["1", "2", "3"],
      },
      server: {
        prisma: {
          $transaction: vi.fn(),
        } as unknown as PrismaClient & MockPrisma,
      },
      userProfile: {
        id: 123,
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as MockFastifyRequest;

    mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("handler", () => {
    it("should regenerate settlement successfully and return 200", async () => {
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockResolvedValue();

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        ["1", "2", "3"],
        "Test reason",
        123
      );

      expect(mockReply.send).toHaveBeenCalledWith({});
    });

    it("should handle settlement not found error", async () => {
      const error = new Error("Settlement not found");
      (
        service.regenerateSettlement as unknown as ReturnType<typeof vi.fn>
      ).mockRejectedValue(error);

      await handler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({
        message: "Settlement not found.",
      });
      expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    });

    it("should handle approved settlement regeneration", async () => {
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockResolvedValue();

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        ["1", "2", "3"],
        "Test reason",
        123
      );

      expect(mockReply.send).toHaveBeenCalledWith({});
    });

    it("should handle generic errors", async () => {
      const error = new Error("Database connection failed");
      (
        service.regenerateSettlement as unknown as ReturnType<typeof vi.fn>
      ).mockRejectedValue(error);

      await handler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        message: "Database connection failed",
      });
      expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    });
  });
});
