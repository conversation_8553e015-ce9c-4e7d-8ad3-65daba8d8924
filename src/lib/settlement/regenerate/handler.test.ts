import { type PrismaClient } from "@prisma/client";
import { type FastifyReply, type FastifyRequest } from "fastify";

import { handler } from "./handler";
import * as service from "./service";
import { type RegenerateRequestBody } from "./types";

type MockPrisma = {
  $transaction: ReturnType<typeof vi.fn>;
};

type MockFastifyRequest = FastifyRequest<{ Body: RegenerateRequestBody }> & {
  server: {
    prisma: PrismaClient & MockPrisma;
  };
  userProfile: {
    id: number;
  };
  log: {
    error: ReturnType<typeof vi.fn>;
  };
};

describe("Regenerate Settlement Handler", () => {
  let mockRequest: MockFastifyRequest;
  let mockReply: FastifyReply;

  beforeEach(() => {
    mockRequest = {
      body: {
        reason: "Test reason",
        settlementId: "1",
      },
      server: {
        prisma: {
          $transaction: vi.fn(),
        } as unknown as PrismaClient & MockPrisma,
      },
      userProfile: {
        id: 123,
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as MockFastifyRequest;

    mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("handler", () => {
    it("should regenerate settlement successfully and return 200", async () => {
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockResolvedValue({
          success: true,
        });

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        "1",
        123,
        "Test reason"
      );

      expect(mockReply.code).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
      });
    });

    it("should handle settlement not found error", async () => {
      const error = new Error("Settlement with id 999 not found");
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockRejectedValue(error);

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        "1",
        123,
        "Test reason"
      );

      expect(mockReply.code).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: "Settlement not found.",
      });
    });

    it("should handle approved settlement regeneration", async () => {
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockResolvedValue({
          success: true,
        });

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        "1",
        123,
        "Test reason"
      );

      expect(mockReply.code).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: true,
      });
    });

    it("should handle generic errors", async () => {
      const error = new Error("Database connection failed");
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockRejectedValue(error);

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        "1",
        123,
        "Test reason"
      );

      expect(mockReply.code).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: "Database connection failed",
      });
      expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    });

    it("should handle approved wires error", async () => {
      const error = new Error(
        "Settlements with approved wires cannot be regenerated"
      );
      const spyRegenerateSettlement = vi
        .spyOn(service, "regenerateSettlement")
        .mockRejectedValue(error);

      await handler(mockRequest, mockReply);

      expect(spyRegenerateSettlement).toHaveBeenCalledWith(
        mockRequest.server.prisma,
        "1",
        123,
        "Test reason"
      );

      expect(mockReply.code).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        success: false,
        error: "Settlements with approved wires cannot be regenerated",
      });
      expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    });
  });
});
