import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Regenerate Settlement",
  description:
    "Regenerate a settlement with updated data and increment regeneration count.",
  tags: [tags.settlement],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      reason: {
        type: "string",
        description: "The reason for regenerating the settlement",
      },
      settlementIds: {
        type: "array",
        items: {
          type: "string",
          description: "The ID of the customer settlement to regenerate",
        },
      },
    },
    required: ["settlementIds"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful settlement regeneration.",
      type: "object",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    403: {
      description: "Cannot regenerate an already approved settlement.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement not found.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error during regeneration.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
