import * as CustomerSettlementGenerationScheduleRepository from "@lib/customer-settlement-generation-schedule/update/repository";
import * as SettlementReadService from "@lib/settlement/read/services/get-settlement-state-and-message";
import { SettlementStateEnum } from "@lib/settlement/read/types";
import * as SettlementRepository from "@lib/settlement/repository/delete-settlements";
import * as SettlementFrequencyRepository from "@lib/settlement/repository/settlement-frequency";
import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import * as Repository from "./repository";
import { regenerateSettlement } from "./service";

vi.mock("@lib/customer-settlement-generation-schedule/update/repository");
vi.mock("@lib/settlement/read/services/get-settlement-state-and-message");
vi.mock("@lib/settlement/repository/delete-settlements");
vi.mock("@lib/settlement/repository/settlement-frequency");
vi.mock("./repository");

type MockSettlement = {
  customerId: number;
  customerName: string;
  serviceNumber: string;
  customerType: string;
  customerCustomerTypeId: number | undefined;
  fromDate: Date;
  toDate: Date;
};

describe("regenerateSettlement", () => {
  let mockPrisma: PrismaClient;
  let mockTransaction: PrismaClient;

  beforeEach(() => {
    mockPrisma = {
      $transaction: vi.fn(),
    } as unknown as PrismaClient;

    mockTransaction = {} as unknown as PrismaClient;

    vi.clearAllMocks();

    vi.mocked(mockPrisma.$transaction).mockImplementation(async (callback) =>
      callback(mockTransaction)
    );
  });

  describe("successful regeneration", () => {
    it("should regenerate settlement successfully for approved settlement", async () => {
      const settlementId = "1";
      const reason = "Test regeneration";
      const userId = 123;

      const mockSettlement = {
        customerId: 789,
        customerName: "Test Customer",
        serviceNumber: "SN123456",
        customerType: "Merchant",
        customerCustomerTypeId: 456,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
      };

      const mockWires = [
        { wireId: 1, wireApprovedById: null, isCancelled: false },
      ];
      const mockEmails = [{ emailId: 1, sentAt: null, deletedAt: null }];

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(mockSettlement);
      vi.mocked(
        SettlementReadService.getSettlementStateAndMessage
      ).mockResolvedValue({
        state: SettlementStateEnum.APPROVAL_SUCCESS,
        message: "Approved",
      });
      vi.mocked(
        Repository.getAllSettlementIdsByCustomerAndPeriod
      ).mockResolvedValue([1]);

      vi.mocked(SettlementRepository.getSettlementWires).mockResolvedValue(
        mockWires
      );
      vi.mocked(Repository.getSettlementEmails).mockResolvedValue(mockEmails);
      vi.mocked(Repository.softDeleteUnsentEmails).mockResolvedValue();
      vi.mocked(Repository.addAuditRecord).mockResolvedValue();

      const result = await regenerateSettlement(
        mockPrisma,
        settlementId,
        userId,
        reason
      );

      expect(result).toEqual({ success: true });
      expect(
        Repository.incrementRegenerationCountAndChangeStatus
      ).toHaveBeenCalledWith(mockTransaction, [1]);
      expect(SettlementRepository.getSettlementWires).toHaveBeenCalledWith(
        [1],
        mockTransaction
      );
      expect(Repository.cancelWiresForSettlements).toHaveBeenCalledWith(
        mockTransaction,
        [1],
        userId
      );
      expect(Repository.getSettlementEmails).toHaveBeenCalledWith(
        mockTransaction,
        [1]
      );
      expect(Repository.softDeleteUnsentEmails).toHaveBeenCalledWith(
        mockTransaction,
        [{ emailId: 1, sentAt: null, deletedAt: null }]
      );
      expect(Repository.addAuditRecord).toHaveBeenCalledWith(mockTransaction, {
        table: "customerSettlements",
        settlementsIds: "1",
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        comment: reason,
        userId,
      });
      expect(
        CustomerSettlementGenerationScheduleRepository.createCustomerSettlementGenerationSchedule
      ).toHaveBeenCalledWith(mockTransaction, {
        serviceNumber: "SN123456",
        fromDate: "2025-01-01T00:00:00.000Z",
        toDate: "2025-01-31T00:00:00.000Z",
        customerCustomerTypeId: 456,
        generationType: "INITIAL",
        generationStatus: "COMPLETE",
        completionDate: expect.any(Date) as Date,
      });
    });
  });

  describe("error handling", () => {
    it("should return error when settlement not found", async () => {
      const settlementId = "999";
      const reason = "Test regeneration";
      const userId = 123;

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(undefined as unknown as MockSettlement);

      const result = await regenerateSettlement(
        mockPrisma,
        settlementId,
        userId,
        reason
      );

      expect(result).toEqual({
        success: false,
        error: "Regeneration failed: Settlement with id 999 not found",
      });
    });

    it("should return error when settlements have approved wires", async () => {
      const settlementId = "1";
      const reason = "Test regeneration";
      const userId = 123;

      const mockSettlement = {
        customerId: 789,
        customerName: "Test Customer",
        serviceNumber: "SN123456",
        customerType: "Merchant",
        customerCustomerTypeId: 456,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
      };

      const mockSettlementIds = [1];
      const mockWires = [
        { wireId: 1, wireApprovedById: 123, isCancelled: false },
        { wireId: 2, wireApprovedById: null, isCancelled: false },
      ];

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(mockSettlement);
      vi.mocked(
        SettlementReadService.getSettlementStateAndMessage
      ).mockResolvedValue({
        state: SettlementStateEnum.APPROVAL_SUCCESS,
      });
      vi.mocked(
        Repository.getAllSettlementIdsByCustomerAndPeriod
      ).mockResolvedValue(mockSettlementIds);
      vi.mocked(SettlementRepository.getSettlementWires).mockResolvedValue(
        mockWires
      );

      const result = await regenerateSettlement(
        mockPrisma,
        settlementId,
        userId,
        reason
      );

      expect(result).toEqual({
        success: false,
        error:
          "Regeneration failed: Settlements with approved wires cannot be regenerated",
      });
    });
  });
});
