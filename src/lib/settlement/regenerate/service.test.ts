import * as CustomerSettlementGenerationScheduleRepository from "@lib/customer-settlement-generation-schedule/update/repository";
import * as SettlementReadService from "@lib/settlement/read/services/get-settlement-state-and-message";
import { SettlementStateEnum } from "@lib/settlement/read/types";
import * as SettlementRepository from "@lib/settlement/repository/delete-settlements";
import * as SettlementFrequencyRepository from "@lib/settlement/repository/settlement-frequency";
import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import * as Repository from "./repository";
import { regenerateSettlement } from "./service";

vi.mock("@lib/customer-settlement-generation-schedule/update/repository");
vi.mock("@lib/settlement/read/services/get-settlement-state-and-message");
vi.mock("@lib/settlement/repository/delete-settlements");
vi.mock("@lib/settlement/repository/settlement-frequency");
vi.mock("./repository");

type MockSettlement = {
  customerId: number;
  customerName: string;
  serviceNumber: string;
  customerType: string;
  customerCustomerTypeId: number | undefined;
  fromDate: Date;
  toDate: Date;
};

describe("regenerateSettlement", () => {
  let mockPrisma: PrismaClient;
  let mockTransaction: PrismaClient;

  beforeEach(() => {
    mockPrisma = {
      $transaction: vi.fn(),
    } as unknown as PrismaClient;

    mockTransaction = {} as unknown as PrismaClient;

    vi.clearAllMocks();

    vi.mocked(mockPrisma.$transaction).mockImplementation(async (callback) =>
      callback(mockTransaction)
    );
  });

  describe("successful regeneration", () => {
    it("should regenerate settlement successfully for non-approved settlement", async () => {
      const settlementIds = ["1", "2"];
      const reason = "Test regeneration";
      const userId = 123;

      const mockSettlement = {
        customerId: 789,
        customerName: "Test Customer",
        serviceNumber: "SN123456",
        customerType: "Merchant",
        customerCustomerTypeId: 456,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
      };

      const mockSettlementIds = [1, 2, 3];
      const mockWires = [
        { wireId: 1, wireApprovedById: null, isCancelled: false },
        { wireId: 2, wireApprovedById: null, isCancelled: true },
      ];
      const mockEmails = [
        { emailId: 1, sentAt: null, deletedAt: null },
        { emailId: 2, sentAt: new Date(), deletedAt: null },
      ];

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(mockSettlement);
      vi.mocked(
        SettlementReadService.getSettlementStateAndMessage
      ).mockResolvedValue({
        state: SettlementStateEnum.PROCESSING,
        message: "Processing",
      });
      vi.mocked(
        Repository.getAllSettlementsIdsByCustomerAndPeriod
      ).mockResolvedValue(mockSettlementIds);
      vi.mocked(SettlementRepository.getSettlementWires).mockResolvedValue(
        mockWires
      );
      vi.mocked(Repository.cancelWiresForSettlements).mockResolvedValue({
        count: 2,
      });
      vi.mocked(Repository.getSettlementEmails).mockResolvedValue(mockEmails);
      vi.mocked(Repository.softDeleteUnsentEmails).mockResolvedValue();
      vi.mocked(Repository.addAuditRecord).mockResolvedValue();

      await regenerateSettlement(mockPrisma, settlementIds, reason, userId);

      expect(mockPrisma.$transaction).toHaveBeenCalled();
      expect(
        SettlementFrequencyRepository.getSettlementById
      ).toHaveBeenCalledWith(mockTransaction, 1);
      expect(
        SettlementFrequencyRepository.getSettlementById
      ).toHaveBeenCalledWith(mockTransaction, 2);
      expect(
        SettlementReadService.getSettlementStateAndMessage
      ).toHaveBeenCalledWith(
        {
          customerCustomerTypeId: 456,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-31"),
        },
        mockTransaction
      );
      expect(
        Repository.getAllSettlementsIdsByCustomerAndPeriod
      ).toHaveBeenCalledWith(
        mockTransaction,
        789,
        new Date("2025-01-01"),
        new Date("2025-01-31")
      );
      expect(SettlementRepository.getSettlementWires).toHaveBeenCalledWith(
        [1, 2, 3, 1, 2, 3],
        mockTransaction
      );
      expect(Repository.cancelWiresForSettlements).toHaveBeenCalledWith(
        mockTransaction,
        [1, 2, 3, 1, 2, 3],
        userId
      );
      expect(Repository.getSettlementEmails).toHaveBeenCalledWith(
        mockTransaction,
        [1, 2, 3, 1, 2, 3]
      );
      expect(Repository.softDeleteUnsentEmails).toHaveBeenCalledWith(
        mockTransaction,
        [{ emailId: 1, sentAt: null, deletedAt: null }]
      );
      expect(Repository.addAuditRecord).toHaveBeenCalledWith(mockTransaction, {
        table: "customerSettlements",
        settlementsIds: "1,2,3,1,2,3",
        preValues: JSON.stringify({ status: "APPROVED" }),
        postValues: JSON.stringify({ status: "INIT" }),
        type: "REGENERATE",
        comment: reason,
        userId,
      });
    });

    it("should regenerate settlement successfully for approved settlement", async () => {
      const settlementIds = ["1"];
      const reason = "Test regeneration";
      const userId = 123;

      const mockSettlement = {
        customerId: 789,
        customerName: "Test Customer",
        serviceNumber: "SN123456",
        customerType: "Merchant",
        customerCustomerTypeId: 456,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
      };

      const mockSettlementIds = [1];
      const mockWires = [
        { wireId: 1, wireApprovedById: null, isCancelled: false },
      ];
      const mockEmails = [{ emailId: 1, sentAt: null, deletedAt: null }];

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(mockSettlement);
      vi.mocked(
        SettlementReadService.getSettlementStateAndMessage
      ).mockResolvedValue({
        state: SettlementStateEnum.APPROVAL_SUCCESS,
        message: "Approved",
      });
      vi.mocked(
        Repository.getAllSettlementsIdsByCustomerAndPeriod
      ).mockResolvedValue(mockSettlementIds);
      vi.mocked(
        Repository.incrementRegenerationCountAndChangeStatus
      ).mockResolvedValue({
        count: 1,
      });

      vi.mocked(SettlementRepository.getSettlementWires).mockResolvedValue(
        mockWires
      );
      vi.mocked(Repository.cancelWiresForSettlements).mockResolvedValue({
        count: 1,
      });
      vi.mocked(Repository.getSettlementEmails).mockResolvedValue(mockEmails);
      vi.mocked(Repository.softDeleteUnsentEmails).mockResolvedValue();
      vi.mocked(Repository.addAuditRecord).mockResolvedValue();

      await regenerateSettlement(mockPrisma, settlementIds, reason, userId);

      expect(
        Repository.incrementRegenerationCountAndChangeStatus
      ).toHaveBeenCalledWith(mockTransaction, [1]);
      expect(
        CustomerSettlementGenerationScheduleRepository.createCustomerSettlementGenerationSchedule
      ).toHaveBeenCalledWith(mockTransaction, {
        serviceNumber: "SN123456",
        fromDate: "2025-01-01T00:00:00.000Z",
        toDate: "2025-01-31T00:00:00.000Z",
        customerCustomerTypeId: 456,
        generationType: "INITIAL",
        generationStatus: "COMPLETE",
      });
    });
  });

  describe("error handling", () => {
    it("should throw error when settlement not found", async () => {
      const settlementIds = ["999"];
      const reason = "Test regeneration";
      const userId = 123;

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(undefined as unknown as MockSettlement);

      await expect(
        regenerateSettlement(mockPrisma, settlementIds, reason, userId)
      ).rejects.toThrow("Settlement not found");
    });

    it("should throw error when settlements have approved wires", async () => {
      const settlementIds = ["1"];
      const reason = "Test regeneration";
      const userId = 123;

      const mockSettlement = {
        customerId: 789,
        customerName: "Test Customer",
        serviceNumber: "SN123456",
        customerType: "Merchant",
        customerCustomerTypeId: 456,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
      };

      const mockSettlementIds = [1];
      const mockWires = [
        { wireId: 1, wireApprovedById: 123, isCancelled: false },
        { wireId: 2, wireApprovedById: null, isCancelled: false },
      ];

      vi.mocked(
        SettlementFrequencyRepository.getSettlementById
      ).mockResolvedValue(mockSettlement);
      vi.mocked(
        SettlementReadService.getSettlementStateAndMessage
      ).mockResolvedValue({
        state: SettlementStateEnum.PROCESSING,
        message: "Processing",
      });
      vi.mocked(
        Repository.getAllSettlementsIdsByCustomerAndPeriod
      ).mockResolvedValue(mockSettlementIds);
      vi.mocked(SettlementRepository.getSettlementWires).mockResolvedValue(
        mockWires
      );

      await expect(
        regenerateSettlement(mockPrisma, settlementIds, reason, userId)
      ).rejects.toThrow(
        "Settlements with approved wires cannot be regenerated"
      );
    });
  });
});
