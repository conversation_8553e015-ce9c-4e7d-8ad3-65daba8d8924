import { regenerateSettlement } from "./service";

import type { RegenerateRequestBody } from "./types";
import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{ Body: RegenerateRequestBody }>,
  reply: FastifyReply
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { settlementId, reason } = request.body;

    const result = await regenerateSettlement(
      request.server.prisma,
      settlementId,
      request.userProfile.id,
      reason
    );

    return await reply.code(200).send(result);
  } catch (error) {
    request.log.error(error);

    if ((error as Error).message.includes("not found")) {
      return reply.code(404).send({
        success: false,
        error: "Settlement not found.",
      });
    }

    return reply.code(500).send({
      success: false,
      error: (error as Error).message,
    });
  }
};
