import { regenerateSettlement } from "./service";

import type { RegenerateRequestBody } from "./types";
import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{ Body: RegenerateRequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { reason, settlementIds } = request.body;

    await regenerateSettlement(
      request.server.prisma,
      settlementIds,
      reason,
      request.userProfile.id
    );

    return await reply.code(200).send({});
  } catch (error) {
    request.log.error(error);

    if ((error as Error).message.includes("Settlement not found")) {
      return reply.code(404).send({
        message: "Settlement not found.",
      });
    }

    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
