// General Constants
export const descending = "desc";
export const ascending = "asc";
export const internalServerError = "Internal Server Error";
export const summary = "SUMMARY";
export const skipped = "Skipped";
export const notFound = "Not Found";
export const unknown = "Unknown";
export const error = "Error";
export const processing = "Processing";
export const approvalError = "Approval Error";
export const statusUnknown = "Status Unknown";
export const na = "NA";

// Settlement Filter Constants
export const showAll = "Show All";
export const adjustmentOnly = "Adjustments Only";
export const noAdjustments = "No Adjustments";
export const noFilter = "No Filter";
export const settlementApproval = "Settlement Approval";
export const approval = "Approval";
export const settlementGeneration = "Settlement Generation";

// Settlement Details Constants
export const transactionCount = "transactionCount";
export const totalTransactionAmount = "totalTransactionAmount";
export const refundCount = "refundCount";
export const totalRefundAmount = "totalRefundAmount";
export const gatewayFee = "gatewayFee";
export const transactionFee = "transactionFee";
export const salesFee = "salesFee";
export const refundFee = "refundFee";
export const totalFailedAmount = "totalFailedAmount";
export const total2FaRejectAmount = "total2FaRejectAmount";
export const total2FaRejectCount = "total2FaRejectCount";
// eslint-disable-next-line @typescript-eslint/naming-convention
export const txnAmountRTO_R = "txnAmountRTO_R";
// eslint-disable-next-line @typescript-eslint/naming-convention
export const txnCountETI_R1 = "txnCountETI_R1";
export const minimumFeeTotal = "minimumFeeTotal";
export const minimumFeeCount = "minimumFeeCount";
export const totalMinimumAmount = "totalMinimumAmount";
// eslint-disable-next-line @typescript-eslint/naming-convention
export const partialReturnAmountRTO = "partialReturnAmountRTO";
// eslint-disable-next-line @typescript-eslint/naming-convention
export const partialReturnCountRTO = "partialReturnCountRTO";
