import { getAllCustomersByFrequency } from "@lib/customer/repository/get-customer";
import { getFrequency } from "@lib/frequency/repository";
import { getUserProfile } from "@lib/session/user-profile/functions";
import { generateExcel } from "@lib/settlement/excel/generate-excel";
import { calculateEndBalances } from "@lib/settlement/functions/calculate-end-balances";
import {
  createFrequencySettlementSchedule,
  saveFrequencySettlements,
} from "@lib/settlement/repository/settlement-frequency";
import {
  createFrequencySettlementJob,
  getFrequencySettlementJobSuccess,
  getFrequencySettlementJobInProgress,
  updateFrequencySettlementJob,
} from "@lib/settlement/repository/settlement-frequency-job";
import {
  SettlementStatus,
  type FrequencySettlementSchedule,
  type FrequencySettlementsResult,
} from "@lib/settlement/repository/types";

import { type CalculationOptions } from "../../../../../types/settlement";
import { chunkArray } from "../../../../../utils/chunk-array";
import {
  endOfPreviousMonthOnly,
  startOfPreviousMonthOnly,
  dateToString,
  toDateOnly,
  fromDateOnlyToString,
} from "../../../../../utils/date-only";
import { calculateSettlements } from "../../../functions/calculate-settlements";
import { validateFrequency } from "../../../functions/helpers/frequency-validator";

import type { FastifyReply, FastifyRequest } from "fastify";

type SettlementSuccessResponse = {
  success: true;
  message: string;
  data: {
    settlement: {
      jobId: number;
      frequencyName: string;
      fromDate: string;
      toDate: string;
    };
  };
};

type ErrorResponse = {
  success: false;
  message: string;
  error?: string;
};

type FrequencySettlementResponse = SettlementSuccessResponse | ErrorResponse;

type FrequencyCode = "WM" | "WF" | "M" | "SM" | "TaW";
type RequestBody = {
  frequencyId: number;
  fromDate: string;
  toDate: string;
  test?: boolean;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { frequencyId, fromDate, toDate, test } = request.body;

  if (!frequencyId) {
    return reply.status(400).send({
      success: false,
      message: "Frequency is required",
    } satisfies FrequencySettlementResponse);
  }

  if (!fromDate) {
    return reply.status(400).send({
      success: false,
      message: "From value is required",
    } satisfies FrequencySettlementResponse);
  }

  if (!toDate) {
    return reply.status(400).send({
      success: false,
      message: "To value is required",
    } satisfies FrequencySettlementResponse);
  }

  const fromDateObject = new Date(fromDate);
  const toDateObject = new Date(toDate);

  if (
    Number.isNaN(fromDateObject.getTime()) ||
    Number.isNaN(toDateObject.getTime())
  ) {
    return reply.status(400).send({
      success: false,
      message: "Send a valid FROM and TO time period",
    } satisfies FrequencySettlementResponse);
  }

  const frequency = await getFrequency(frequencyId, request.server.prisma);

  if (!frequency) {
    return reply.status(400).send({
      success: false,
      message: "Frequency is required",
    } satisfies FrequencySettlementResponse);
  }

  const fromDateString = dateToString(fromDateObject);
  const toDateString = dateToString(toDateObject);

  const formattedFromDate = toDateOnly(fromDateObject);
  const formattedToDate = toDateOnly(toDateObject);

  if (!frequency) {
    return reply.status(400).send({
      success: false,
      message: "Frequency is required",
    } satisfies FrequencySettlementResponse);
  }

  const doesFrequencyMatchDateRange = validateFrequency(
    fromDateObject,
    toDateObject,
    frequency?.frequencyCode as FrequencyCode
  );

  if (!doesFrequencyMatchDateRange) {
    return reply.status(400).send({
      success: false,
      message: "Send a valid FROM and TO time period",
    } satisfies FrequencySettlementResponse);
  }

  const user = await getUserProfile(
    request.server.prisma,
    request.userProfile.id
  );

  request.log.info(
    { userId: user!.userProfile.id, userName: user!.userProfile.fullName },
    `Starting frequency settlement generation for ${frequency.frequencyName} from ${fromDateString} to ${toDateString}.`
  );

  const progressJob = await getFrequencySettlementJobInProgress(
    request.server.prisma
  );

  if (progressJob) {
    const message = `Settlement generation is currently in progress for ${progressJob.frequencyName} from ${dateToString(progressJob.fromDate)} to ${dateToString(progressJob.toDate)}.`;

    request.log.info(progressJob, message);

    return reply.status(200).send({
      success: false,
      message,
    } satisfies FrequencySettlementResponse);
  }

  // Regardless of the time passed in from postman or the UI
  // (UI will always send 00:00:000), we will only check for dates with
  // time 00:00:000 as the dates stored are only date month year
  const adjustedFromDateString = `${fromDateString}T00:00:00`;
  const adjustedToDateString = `${toDateString}T00:00:00`;

  const existingJob = await getFrequencySettlementJobSuccess(
    new Date(adjustedFromDateString),
    new Date(adjustedToDateString),
    request.server.prisma
  );

  if (existingJob && !test) {
    const message = `Settlement generation for ${frequency.frequencyName} from ${fromDateString} to ${toDateString} has already been generated.`;
    request.log.info(existingJob, message);

    return reply.status(200).send({
      success: false,
      message:
        "Settlement generation has already been generated for this period.",
    } satisfies FrequencySettlementResponse);
  }

  const start = performance.now();

  let successCount = 0;
  let errorCount = 0;
  let skippedCount = 0;
  let totalCount = 0;

  const jobId = await createFrequencySettlementJob(
    {
      fromDate: new Date(adjustedFromDateString),
      toDate: new Date(adjustedToDateString),
    },
    frequency.frequencyId,
    request.userProfile.id,
    request.server.prisma
  );

  request.log.info({ jobId }, "Created frequency settlement job.");

  await reply.status(200).send({
    success: true,
    message:
      "Processing Settlements, please refresh the page in a bit to see results.",
    data: {
      settlement: {
        jobId,
        frequencyName: frequency.frequencyName,
        fromDate: fromDateString,
        toDate: toDateString,
      },
    },
  } satisfies FrequencySettlementResponse);

  try {
    const customers = await getAllCustomersByFrequency(
      request.server.prisma,
      frequency.frequencyId
    );
    const batchSize = 20;
    // Split the customers array into chunks of batchSize for memory management
    const customerChunks = chunkArray(customers, batchSize);

    const options: CalculationOptions = {
      rateDeterminingInterval: {
        fromDate: startOfPreviousMonthOnly(formattedFromDate),
        toDate: endOfPreviousMonthOnly(formattedFromDate),
      },
      includeKyc: true,
    };

    for (const chunk of customerChunks) {
      const customerNames = chunk.map((customer) => customer.customerName);

      request.log.info(
        { customers: customerNames },
        "Starting settlement calculation for this chunk of customers."
      );

      const schedules: Record<number, FrequencySettlementSchedule> = {};

      const schedulePromises = chunk.map(async (customer) => {
        const schedule = await createFrequencySettlementSchedule(
          customer,
          formattedFromDate,
          formattedToDate,
          request.server.prisma
        );
        schedules[customer.customerCustomerTypeId] = schedule;
      });

      // eslint-disable-next-line no-await-in-loop
      await Promise.all(schedulePromises);

      request.log.info(
        { customers: customerNames },
        "Created frequency settlement schedules. Calculating settlements."
      );

      // eslint-disable-next-line no-await-in-loop
      const settlements = await calculateSettlements(
        chunk,
        {
          fromDate: formattedFromDate,
          toDate: formattedToDate,
        },
        request.server.prisma,
        options
      );

      const successObjects = Object.values(settlements).filter(
        (value) => value.status === "SUCCESS"
      );
      successCount += successObjects.length;

      const errorObjects = Object.values(settlements).filter(
        (value) => value.status === "ERROR"
      );
      errorCount += errorObjects.length;

      const skippedObjects = Object.values(settlements).filter(
        (value) => value.status === "SKIPPED"
      );
      skippedCount += skippedObjects.length;

      totalCount += Object.keys(settlements).length;

      if (
        Object.keys(settlements).length !==
        skippedObjects.length + successObjects.length + errorObjects.length
      ) {
        // Logging a warning
        // not throwing an error as this is a minor issue
        request.log.warn(
          {
            skipped: skippedObjects.length,
            success: successObjects.length,
            error: errorObjects.length,
            totalCount: Object.keys(settlements).length,
            customers: customerNames,
          },
          "Mismatch in settlement generation count meta for customers in this chunk."
        );
      }

      let settlementsResult: FrequencySettlementsResult = {};

      for (const [customerCustomerTypeId, result] of Object.entries(
        settlements
      )) {
        settlementsResult[Number(customerCustomerTypeId)] = {
          ...schedules[Number(customerCustomerTypeId)]!,
          ...result,
        };
      }

      request.log.info(
        { customers: customerNames },
        "Calculated settlements for this chunk. Calculating end balances."
      );

      // eslint-disable-next-line no-await-in-loop
      settlementsResult = await calculateEndBalances(
        settlementsResult,
        formattedFromDate,
        formattedToDate,
        request.server.prisma
      );

      request.log.info(
        { customers: customerNames },
        "Calculated end balances."
      );

      const nonMerchantCustomers = chunk.filter(
        (customer) => customer.customerTypeName !== "Merchant"
      );

      for (const customer of nonMerchantCustomers) {
        const settlementResult =
          settlementsResult[customer.customerCustomerTypeId]?.data?.result;

        if (settlementResult) {
          request.log.info(
            `Starting Excel file generation for ${customer.customerName}.`
          );

          // eslint-disable-next-line max-depth
          try {
            const fileName = `${customer.customerTradingName}-${fromDateOnlyToString(formattedFromDate)}-${fromDateOnlyToString(formattedToDate)}-init.xlsx`;

            // eslint-disable-next-line no-await-in-loop
            await generateExcel(settlementResult, customer, fileName);

            request.log.info(
              `Generated Excel file for ${customer.customerName}.`
            );
          } catch (error) {
            request.log.error(
              error,
              `Error generating Excel file for ${customer.customerName}.`
            );

            settlementsResult[customer.customerCustomerTypeId]!.status =
              "ERROR";
            settlementsResult[customer.customerCustomerTypeId]!.message =
              error instanceof Error ? error.message : "Unknown error";
          }
        }
      }

      // eslint-disable-next-line no-await-in-loop
      await saveFrequencySettlements(
        settlementsResult,
        formattedFromDate,
        formattedToDate,
        request.server.prisma
      );

      request.log.info(
        { customers: customerNames },
        "Settlements saved to the database."
      );
    }

    request.log.info(
      "All frequency settlements have been calculated and saved to database. Updating frequency settlement job."
    );

    const meta = {
      timeTakenInSec: Math.round((performance.now() - start) / 1000),
      totalCount,
      successCount,
      errorCount,
      skippedCount,
    };
    await updateFrequencySettlementJob(
      jobId,
      meta,
      SettlementStatus.SUCCESS,
      request.server.prisma
    );

    request.log.info(
      `Completed frequency settlement generation for ${frequency.frequencyName} from ${fromDateString} to ${toDateString}.`
    );
  } catch (error) {
    request.log.error(
      error,
      `Error calculating frequency settlements for ${frequency.frequencyName} from ${fromDateString} to ${toDateString}.`
    );

    const meta = {
      timeTakenInSec: Math.round((performance.now() - start) / 1000),
    };
    await updateFrequencySettlementJob(
      jobId,
      meta,
      SettlementStatus.ERROR,
      request.server.prisma
    );

    return reply.status(500).send({
      success: false,
      message: "Internal server error.",
      error: "An unexpected error occurred.",
    } satisfies FrequencySettlementResponse);
  }
};
