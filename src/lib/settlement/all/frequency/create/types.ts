export type SettlementSuccessResponse = {
  success: true;
  message: string;
  data: {
    settlement: {
      jobId: number;
      frequencyName: string;
      fromDate: string;
      toDate: string;
    };
  };
};

export type ErrorResponse = {
  success: false;
  message: string;
  error?: string;
  hasConflicts?: boolean;
  conflicts?: {
    generatedSettlements: Array<{
      customerCustomerTypeId: number;
      customerName: string;
      serviceNumber: string;
      fromDate: string;
      toDate: string;
    }>;
    approvedSettlements: Array<{
      customerCustomerTypeId: number;
      customerName: string;
      serviceNumber: string;
      fromDate: string;
      toDate: string;
    }>;
  };
};

export type FrequencySettlementResponse =
  | SettlementSuccessResponse
  | ErrorResponse;

export type FrequencyCode = "WM" | "WF" | "M" | "SM" | "TaW";
export type RequestBody = {
  frequencyId: number;
  fromDate: string;
  toDate: string;
  test?: boolean;
  bypassConflictCheck?: boolean;
  conflicts?: {
    generatedSettlements: Array<{
      customerCustomerTypeId: number;
      customerName: string;
      serviceNumber: string;
      fromDate: string;
      toDate: string;
    }>;
    approvedSettlements: Array<{
      customerCustomerTypeId: number;
      customerName: string;
      serviceNumber: string;
      fromDate: string;
      toDate: string;
    }>;
  };
  approvedCustomerCustomerTypeIds?: number[];
};
