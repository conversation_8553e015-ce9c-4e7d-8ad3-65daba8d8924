import { type ChargedFeeWithMeta } from "../functions/workers/calculate-merchant-settlement";
import { type AdjustmentDetails, type KycDetails } from "../repository/types";

import type ExcelJS from "exceljs";

export type SettlementMetadata = {
  feesBreakdown: ChargedFeeWithMeta[];
};

export type ApproveRequestBody = {
  serviceNumber: number;
  fromDate: string;
  toDate: string;
  customerCustomerTypeId: number;
  customerSettlementId: number;
  netPayout: number;
};

export type ApproveParameters = {
  serviceNumber: string;
  fromDate: string;
  toDate: string;
  customerCustomerTypeId: number;
  customerSettlementsId: number;
  netPayout: number;
  userId: number;
};

export type SummaryInformation = Record<
  string,
  {
    labelName: string | undefined;
    transactionCount: number | undefined;
    totalTransactionAmount: number | undefined;
    refundCount: number | undefined;
    totalRefundAmount: number | undefined;
    gatewayFee: number | undefined;
    transactionFee: number | undefined;
    salesFee: number | undefined;
    refundFee: number | undefined;
    totalFailedAmount: number | undefined;
    endBalance: number | undefined;
    displaySequence: number | undefined;
    minimumFeeTotal: number | undefined;
    adjustments: AdjustmentDetails[];
    kycDetails?: KycDetails | undefined;
  }
>;

export type ExcelConfig = {
  platformCode: string;
  customerTradingName: string;
  period: {
    fromDate: Date;
    toDate: Date;
  };
  fileName: string;
  folderPath: string;
  data: SettlementExcelData;
  customer?: {
    entityName: string;
    entityLogoName: string;
  };
  kycDescriptions?: ;
};

export type SettlementExcelData = {
  labelName: string;
  transactionCount: number;
  totalTransactionAmount: number;
  refundCount: number;
  totalRefundAmount: number;
  gatewayFee: number;
  transactionFee: number;
  salesFee: number;
  refundFee: number;
  totalFailedAmount: number;
  endBalance: number;
  total2FaRejectAmount: number;
  total2FaRejectCount: number;
  txnAmountRTO_R: number;
  txnCountETI_R1: number;
  minimumFeeTotal: number;
  minimumFeeCount: number;
  totalMinimumAmount: number;
  partialReturnAmountRTO: number;
  partialReturnCountRTO: number;
  isAdjusted: boolean;
  adjustments: Array<{
    id: number;
    label: string;
    amount: number;
    displayCommentExcel: boolean;
    comment: string;
  }>;
  kycDetails?: KycDetails;
  meta?: SettlementMetadata;
};

export type PlatformData = {
  labelName: string | undefined;
  transactionCount: number | undefined;
  totalTransactionAmount: number | undefined;
  totalFailedAmount: number | undefined;
  transactionFee: number | undefined;
  gatewayFee: number | undefined;
  salesFee: number | undefined;
  refundFee: number | undefined;
  totalRefundAmount: number | undefined;
  minimumFeeTotal: number | undefined;
};

export type WorksheetCollection = {
  transactionsWorksheet: ExcelJS.Worksheet;
  refundsOrFailedWorksheet?: ExcelJS.Worksheet;
  rejected1Worksheet?: ExcelJS.Worksheet;
  RTO_R_Worksheet?: ExcelJS.Worksheet;
  RTO_P_Worksheet?: ExcelJS.Worksheet;
};

export type RowCounters = {
  transactions: number;
  refunds: number;
  failed: number;
  rejected1: number;
  RTO_R: number;
  RTO_P: number;
};

export type TransactionProcessingContext = {
  worksheets: WorksheetCollection;
  rowCounters: RowCounters;
  config: ExcelConfig;
  isPayInPlatform: boolean;
};
