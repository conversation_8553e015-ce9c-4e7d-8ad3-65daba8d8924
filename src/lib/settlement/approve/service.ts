import { createCustomerSettlementGenerationSchedule } from "@lib/customer-settlement-generation-schedule/update/repository";
import { fetchPreviousSettlementForEndBalance } from "@lib/customer-settlements/read/repository";
import { getSettlementForApproval } from "@lib/customer-settlements/read/service";
import {
  type ApprovalCustomerSettlement,
  type PreviousSettlement,
} from "@lib/customer-settlements/read/type";
import {
  updateCustomerSettlementsToApproved,
  updateCustomerSettlementWithEndBalance,
} from "@lib/customer-settlements/update/repository";
import { getWirePayments } from "@lib/customer-wire-in-outs/repository";
import { type WirePayment } from "@lib/customer-wire-in-outs/type";
import { createEmail } from "@lib/email/create/service";
import { createWire } from "@lib/wire/create/service";
import { toDateOnlyFromString } from "@utils/date-only";
import { subDays } from "date-fns";

import { type ApproveParameters } from "./types";
import { generateSettlementExcelFiles } from "../excel/generate-merchant-settlement-excel";

import type { PrismaClient } from "@prisma/client";

export const approveSettlement = async (
  prisma: PrismaClient,
  approveParameters: ApproveParameters
) => {
  const {
    serviceNumber,
    fromDate,
    toDate,
    customerSettlementsId,
    customerCustomerTypeId,
  } = approveParameters;

  try {
    // await createCustomerSettlementGenerationSchedule(prisma, {
    //   serviceNumber,
    //   fromDate,
    //   toDate,
    //   customerCustomerTypeId,
    //   generationType: "FINAL",
    //   generationStatus: "STARTED",
    // });

    const settlement = await getSettlementForApproval(
      prisma,
      customerSettlementsId
    );

    const customerType =
      settlement.customerCustomerType.customerType.customerTypeName;

    await (customerType === "Merchant"
      ? approveMerchantSettlement(prisma, settlement, approveParameters)
      : approveNonMerchantSettlement());

    // const scheduleRecord = await createCustomerSettlementGenerationSchedule(
    //   prisma,
    //   {
    //     serviceNumber,
    //     fromDate,
    //     toDate,
    //     customerCustomerTypeId,
    //     generationType: "FINAL",
    //     generationStatus: "COMPLETE",
    //     completionDate: new Date(),
    //   }
    // );

    return {
      success: true,
      message: "Settlement approved successfully",
      // scheduleId: scheduleRecord.customerSettlementGenerationScheduleId,
    };
  } catch (error) {
    // await createCustomerSettlementGenerationSchedule(prisma, {
    //   serviceNumber,
    //   fromDate,
    //   toDate,
    //   customerCustomerTypeId,
    //   generationType: "FINAL",
    //   generationStatus: "ERROR",
    //   errorMessage: (error as Error).message,
    // });

    throw error;
  }
};

export const approveMerchantSettlement = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement,
  approveParameters: ApproveParameters
) => {
  const { fromDate, toDate, netPayout, userId } = approveParameters;

  // Convert string dates to DateOnly format - renamed variables to avoid conflict
  const convertedFromDate = toDateOnlyFromString(fromDate);
  const convertedToDate = toDateOnlyFromString(toDate);

  const previousDate = getPreviousDay(fromDate);

  // const [previousSettlement, wires] = await Promise.all([
  //   fetchPreviousSettlementForEndBalance(
  //     prisma,
  //     settlement.customerId,
  //     settlement.platform.platformId,
  //     previousDate
  //   ),
  //   getWirePayments(
  //     settlement.customerId,
  //     convertedFromDate,
  //     convertedToDate,
  //     prisma
  //   ),
  // ]);

  // const newEndBalance = calculateEndBalance(
  //   wires,
  //   previousSettlement,
  //   netPayout
  // );

  // const [wire] = await Promise.all([
  //   createWire(prisma, {
  //     settlement,
  //     wireAmount: netPayout,
  //     toDate,
  //   }),
  //   updateCustomerSettlementsToApproved(prisma, {
  //     customerId: settlement.customerId,
  //     fromDate,
  //     toDate,
  //     approvedBy: userId,
  //   }),
  //   updateCustomerSettlementWithEndBalance(
  //     prisma,
  //     settlement.customerSettlementsId,
  //     newEndBalance,
  //     userId
  //   ),
  // ]);

  try {
    await generateSettlementExcelFiles(prisma, settlement);
  } catch (error) {
    throw new Error(
      "Failed to generate settlement Excel files: " + (error as Error).message
    );
  }

  // const emailConfiguration = settlement.customer?.emailConfiguration;

  // if (!emailConfiguration || emailConfiguration.isEmailEnabled) {
  //   await createEmail(prisma, settlement, wire);
  // }
};

const getPreviousDay = (fromDate: string): Date => {
  return subDays(new Date(fromDate), 1);
};

const calculateEndBalance = (
  wires: WirePayment[],
  previousSettlement: PreviousSettlement | undefined,
  netPayout: number
): number => {
  let totalWireAmount = 0;

  for (const wire of wires) {
    if (wire.transactionType === "I") {
      totalWireAmount += Number(wire.transactionAmount);
    } else {
      totalWireAmount -= Number(wire.transactionAmount);
    }
  }

  return (
    Number(previousSettlement?.endBalance ?? 0) +
    Number(totalWireAmount ?? 0) +
    Number(netPayout)
  );
};

export const approveNonMerchantSettlement = async () => {
  throw new Error(`NonMerchant settlement approval is not yet implemented.`);
};
