import * as CustomerSettlementGenerationScheduleRepository from "@lib/customer-settlement-generation-schedule/update/repository";
import * as CustomerSettlementsReadRepository from "@lib/customer-settlements/read/repository";
import * as CustomerSettlementsReadService from "@lib/customer-settlements/read/service";
import {
  type ApprovalCustomerSettlement,
  type PreviousSettlement,
} from "@lib/customer-settlements/read/type";
import * as CustomerSettlementsUpdateRepository from "@lib/customer-settlements/update/repository";
import * as CustomerWireInOutsRepository from "@lib/customer-wire-in-outs/repository";
import { type WirePayment } from "@lib/customer-wire-in-outs/type";
import * as EmailCreateService from "@lib/email/create/service";
import { generateSettlementExcelFiles } from "@lib/settlement/excel/generate-merchant-settlement-excel";
import * as WireCreateService from "@lib/wire/create/service";
import { type Wire } from "@lib/wire/create/types";
import { type PrismaClient } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { approveMerchantSettlement, approveSettlement } from "./service";
import { type ApproveParameters } from "./types";

vi.mock("@lib/customer-settlement-generation-schedule/update/repository");
vi.mock("@lib/customer-settlements/read/repository");
vi.mock("@lib/customer-settlements/read/service");
vi.mock("@lib/customer-settlements/update/repository");
vi.mock("@lib/customer-wire-in-outs/repository");
vi.mock("@lib/email/create/service");
vi.mock("@lib/wire/create/service");
vi.mock("@utils/date-only");
vi.mock("@lib/settlement/excel/generate-merchant-settlement-excel", () => ({
  generateSettlementExcelFiles: vi.fn(),
}));

const setupMocks = () => {
  const mockPreviousSettlement: PreviousSettlement = {
    customerSettlementsId: 99,
    toDate: new Date("2024-12-31"),
    customerId: 1,
    platformId: 2,
    status: "APPROVED",
    endBalance: "500.0",
  };

  const mockWirePayments: WirePayment[] = [
    {
      id: 1,
      transactionAmount: 200,
      transactionType: "I",
    },
    {
      id: 2,
      transactionAmount: 50,
      transactionType: "O",
    },
  ];

  const mockWire: Wire = {
    wireId: 789,
    expectedWireDate: new Date("2025-01-31"),
    finalWireDate: new Date("2025-01-31"),
    expectedWireAmount: 1000.5,
    finalWireAmount: 1000.5,
    customerId: 1,
    createdAt: "2025-01-01T00:00:00.000Z",
    updatedAt: "2025-01-01T00:00:00.000Z",
    reference: "TEST_REF",
    frequencyId: 1,
    isCancelled: false,
    settlementId: 100,
    paymentRailId: 1,
  };

  const mockDateOnly = "2025-01-01";

  return { mockPreviousSettlement, mockWirePayments, mockWire, mockDateOnly };
};

describe("approveSettlement", () => {
  let mockPrisma: PrismaClient;
  let mockApproveParameters: ApproveParameters;

  beforeEach(() => {
    mockPrisma = {} as unknown as PrismaClient;

    mockApproveParameters = {
      serviceNumber: "**********",
      fromDate: "2025-01-01",
      toDate: "2025-01-31",
      customerCustomerTypeId: 1,
      customerSettlementsId: 100,
      netPayout: 1000.5,
      userId: 123,
    };

    vi.clearAllMocks();

    vi.mocked(generateSettlementExcelFiles).mockResolvedValue();
  });

  describe("successful approval", () => {
    it("should approve merchant settlement successfully", async () => {
      const { mockPreviousSettlement, mockWirePayments, mockWire } =
        setupMocks();

      const mockScheduleRecord = {
        customerSettlementGenerationScheduleId: 456,
        serviceNumber: "**********",
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        customerCustomerTypeId: 1,
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        generationDate: null,
        startDate: null,
        completionDate: null,
        errorMessage: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentScheduleId: null,
        deletedAt: null,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        fromUI: 0,
      };

      const mockSettlement: ApprovalCustomerSettlement = {
        customerSettlementsId: 100,
        customerId: 1,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        endBalance: 0,
        platform: {
          platformId: 2,
          platformCode: "ETI",
        },
        customerCustomerType: {
          customerType: { customerTypeName: "Merchant" },
          statementFolderLocation: "/path/to/statements",
          statementFrequency: {
            statementFrequencyId: 1,
            statementFrequencyCode: "M",
          },
        },
        customer: {
          customerName: "Test Customer",
          serviceNumber: "**********",
          customerTradingName: "Test Trading",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: { isHold: false, thresholdAmount: 0 },
            beneficiary: {
              reference: "TEST",
              bankAccount: {
                bankPaymentRailId: 1,
              },
            },
          },
          emailConfiguration: { isEmailEnabled: true },
        },
      };

      vi.spyOn(
        CustomerSettlementGenerationScheduleRepository,
        "createCustomerSettlementGenerationSchedule"
      ).mockResolvedValue(mockScheduleRecord);
      vi.spyOn(
        CustomerSettlementsReadService,
        "getSettlementForApproval"
      ).mockResolvedValue(mockSettlement);

      vi.spyOn(
        CustomerSettlementsReadRepository,
        "fetchPreviousSettlementForEndBalance"
      ).mockResolvedValue(mockPreviousSettlement);
      vi.spyOn(
        CustomerWireInOutsRepository,
        "getWirePayments"
      ).mockResolvedValue(mockWirePayments);
      vi.spyOn(WireCreateService, "createWire").mockResolvedValue(mockWire);
      vi.spyOn(
        CustomerSettlementsUpdateRepository,
        "updateCustomerSettlementsToApproved"
      ).mockResolvedValue({ count: 1 });

      const result = await approveSettlement(mockPrisma, mockApproveParameters);

      expect(result).toEqual({
        success: true,
        message: "Settlement approved successfully",
        scheduleId: 456,
      });

      expect(generateSettlementExcelFiles).toHaveBeenCalledWith(
        mockPrisma,
        mockSettlement
      );
    });

    it("should skip non-merchant settlements", async () => {
      const mockScheduleRecord = {
        customerSettlementGenerationScheduleId: 456,
        serviceNumber: "**********",
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        customerCustomerTypeId: 1,
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        generationDate: null,
        startDate: null,
        completionDate: null,
        errorMessage: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentScheduleId: null,
        deletedAt: null,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        fromUI: 0,
      };

      const nonMerchantSettlement: ApprovalCustomerSettlement = {
        customerSettlementsId: 100,
        customerId: 1,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        endBalance: 0,
        platform: {
          platformId: 2,
          platformCode: "ETI",
        },
        customerCustomerType: {
          customerType: { customerTypeName: "NonMerchant" },
          statementFolderLocation: "/path/to/statements",
          statementFrequency: {
            statementFrequencyId: 1,
            statementFrequencyCode: "M",
          },
        },
        customer: {
          customerName: "Test Customer",
          serviceNumber: "**********",
          customerTradingName: "Test Trading",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: { isHold: false, thresholdAmount: 0 },
            beneficiary: {
              reference: "TEST",
              bankAccount: {
                bankPaymentRailId: 1,
              },
            },
          },
          emailConfiguration: { isEmailEnabled: true },
        },
      };

      vi.spyOn(
        CustomerSettlementGenerationScheduleRepository,
        "createCustomerSettlementGenerationSchedule"
      ).mockResolvedValue(mockScheduleRecord);
      vi.spyOn(
        CustomerSettlementsReadService,
        "getSettlementForApproval"
      ).mockResolvedValue(nonMerchantSettlement);

      await expect(
        approveSettlement(mockPrisma, mockApproveParameters)
      ).rejects.toThrow(
        "NonMerchant settlement approval is not yet implemented."
      );
    });
  });

  describe("error handling", () => {
    it("should handle errors and update schedule status to ERROR", async () => {
      const mockError = new Error("Database connection failed");
      const mockScheduleRecord = {
        customerSettlementGenerationScheduleId: 456,
        serviceNumber: "**********",
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        customerCustomerTypeId: 1,
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        generationDate: null,
        startDate: null,
        completionDate: null,
        errorMessage: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentScheduleId: null,
        deletedAt: null,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        fromUI: 0,
      };

      vi.spyOn(
        CustomerSettlementGenerationScheduleRepository,
        "createCustomerSettlementGenerationSchedule"
      ).mockResolvedValue(mockScheduleRecord);
      vi.spyOn(
        CustomerSettlementsReadService,
        "getSettlementForApproval"
      ).mockRejectedValue(mockError);

      await expect(
        approveSettlement(mockPrisma, mockApproveParameters)
      ).rejects.toThrow("Database connection failed");
    });

    it("should handle errors when no schedule record exists", async () => {
      const mockError = new Error("Database connection failed");

      vi.spyOn(
        CustomerSettlementGenerationScheduleRepository,
        "createCustomerSettlementGenerationSchedule"
      ).mockRejectedValue(mockError);

      await expect(
        approveSettlement(mockPrisma, mockApproveParameters)
      ).rejects.toThrow("Database connection failed");
    });
  });
});

describe("approveMerchantSettlement", () => {
  let mockPrisma: PrismaClient;
  let mockSettlement: ApprovalCustomerSettlement;
  let mockApproveParameters: ApproveParameters;

  beforeEach(() => {
    mockPrisma = {} as unknown as PrismaClient;

    mockSettlement = {
      customerSettlementsId: 100,
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-31"),
      endBalance: 0,
      platform: {
        platformId: 2,
        platformCode: "ETI",
      },
      customerCustomerType: {
        customerType: { customerTypeName: "Merchant" },
        statementFolderLocation: "/path/to/statements",
        statementFrequency: {
          statementFrequencyId: 1,
          statementFrequencyCode: "M",
        },
      },
      customer: {
        customerName: "Test Customer",
        serviceNumber: "**********",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: { isHold: false, thresholdAmount: 0 },
          beneficiary: {
            reference: "TEST",
            bankAccount: {
              bankPaymentRailId: 1,
            },
          },
        },
        emailConfiguration: { isEmailEnabled: true },
      },
    };

    mockApproveParameters = {
      serviceNumber: "**********",
      fromDate: "2025-01-01",
      toDate: "2025-01-31",
      customerCustomerTypeId: 1,
      customerSettlementsId: 100,
      netPayout: 1000.5,
      userId: 123,
    };

    vi.clearAllMocks();
  });

  it("should approve merchant settlement with email enabled", async () => {
    const { mockPreviousSettlement, mockWirePayments, mockWire } = setupMocks();

    vi.spyOn(
      CustomerSettlementsReadRepository,
      "fetchPreviousSettlementForEndBalance"
    ).mockResolvedValue(mockPreviousSettlement);
    vi.spyOn(CustomerWireInOutsRepository, "getWirePayments").mockResolvedValue(
      mockWirePayments
    );
    vi.spyOn(WireCreateService, "createWire").mockResolvedValue(mockWire);
    vi.spyOn(
      CustomerSettlementsUpdateRepository,
      "updateCustomerSettlementsToApproved"
    ).mockResolvedValue({ count: 1 });

    await approveMerchantSettlement(
      mockPrisma,
      mockSettlement,
      mockApproveParameters
    );

    expect(CustomerWireInOutsRepository.getWirePayments).toHaveBeenCalled();
    expect(WireCreateService.createWire).toHaveBeenCalled();
    expect(
      CustomerSettlementsUpdateRepository.updateCustomerSettlementsToApproved
    ).toHaveBeenCalled();
    expect(
      CustomerSettlementsUpdateRepository.updateCustomerSettlementWithEndBalance
    ).toHaveBeenCalled();
    expect(EmailCreateService.createEmail).toHaveBeenCalled();
  });

  it("should approve merchant settlement with email disabled", async () => {
    const { mockPreviousSettlement, mockWirePayments, mockWire } = setupMocks();
    const settlementWithEmailDisabled: ApprovalCustomerSettlement = {
      ...mockSettlement,
      customer: {
        ...mockSettlement.customer,
        emailConfiguration: { isEmailEnabled: false as true },
      },
    };

    vi.spyOn(
      CustomerSettlementsReadRepository,
      "fetchPreviousSettlementForEndBalance"
    ).mockResolvedValue(mockPreviousSettlement);
    vi.spyOn(CustomerWireInOutsRepository, "getWirePayments").mockResolvedValue(
      mockWirePayments
    );
    vi.spyOn(WireCreateService, "createWire").mockResolvedValue(mockWire);
    vi.spyOn(
      CustomerSettlementsUpdateRepository,
      "updateCustomerSettlementsToApproved"
    ).mockResolvedValue({ count: 1 });
    const spyCreateEmail = vi.spyOn(EmailCreateService, "createEmail");

    await approveMerchantSettlement(
      mockPrisma,
      settlementWithEmailDisabled,
      mockApproveParameters
    );

    expect(spyCreateEmail).not.toHaveBeenCalled();
  });

  it("should handle empty wire payments array", async () => {
    const { mockPreviousSettlement, mockWire } = setupMocks();

    vi.spyOn(
      CustomerSettlementsReadRepository,
      "fetchPreviousSettlementForEndBalance"
    ).mockResolvedValue(mockPreviousSettlement);
    vi.spyOn(CustomerWireInOutsRepository, "getWirePayments").mockResolvedValue(
      []
    );
    vi.spyOn(WireCreateService, "createWire").mockResolvedValue(mockWire);
    vi.spyOn(
      CustomerSettlementsUpdateRepository,
      "updateCustomerSettlementsToApproved"
    ).mockResolvedValue({ count: 1 });

    await approveMerchantSettlement(
      mockPrisma,
      mockSettlement,
      mockApproveParameters
    );

    expect(CustomerWireInOutsRepository.getWirePayments).toHaveBeenCalled();
    expect(WireCreateService.createWire).toHaveBeenCalled();
  });
});
