import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Approve Settlement",
  description: "Approve settlement for a client.",
  tags: [tags.settlement],
  body: {
    type: "object",
    properties: {
      serviceNumber: {
        type: "integer",
        description: "A service number of the customer",
        maximum: 9_999_999_999,
        minimum: 1,
      },
      fromDate: {
        type: "string",
        format: "date-time",
        description:
          "From date of the generated settlement in ISO 8601 format (YYYY-MM-DDTHH:MM:SS.000Z)",
      },
      toDate: {
        type: "string",
        format: "date-time",
        description:
          "To date of the generated settlement in ISO 8601 format (YYYY-MM-DDTHH:MM:SS.000Z)",
      },
      customerSettlementId: {
        type: "integer",
        description: "Customer settlement ID",
      },
      customerCustomerTypeId: {
        type: "integer",
        description: "Customer customer type ID",
      },
      netPayout: {
        type: "number",
        description: "Net payout of the settlement",
      },
    },
    required: [
      "serviceNumber",
      "fromDate",
      "toDate",
      "customerCustomerTypeId",
      "customerSettlementId",
      "netPayout",
    ],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful approve settlement.",
      type: "object",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement not found.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error during settlement approval.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
