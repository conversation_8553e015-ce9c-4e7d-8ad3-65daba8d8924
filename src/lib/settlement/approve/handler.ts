import { approveSettlement } from "@lib/settlement/approve/service";
import {
  type ApproveParameters,
  type ApproveRequestBody,
} from "@lib/settlement/approve/types";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const body = request.body as ApproveRequestBody;

    const approveParameters: ApproveParameters = {
      serviceNumber: body.serviceNumber.toString(),
      fromDate: body.fromDate,
      toDate: body.toDate,
      customerCustomerTypeId: body.customerCustomerTypeId,
      customerSettlementsId: body.customerSettlementId,
      netPayout: body.netPayout,
      userId: request.userProfile.id,
    };

    await approveSettlement(request.server.prisma, approveParameters);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    if ((error as Error).message.includes("settlement was not found")) {
      return reply.code(404).send({
        message: "Settlement not found.",
      });
    }

    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
