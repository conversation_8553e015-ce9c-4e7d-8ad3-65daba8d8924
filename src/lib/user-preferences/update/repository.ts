import type { PrismaClient, userPreference } from "@prisma/client";

export const updateDarkModePreference = async (
  userId: number,
  darkMode: boolean,
  prisma: PrismaClient
): Promise<Pick<userPreference, "darkModeEnabled">> => {
  const darkModeResponse = await prisma.userPreference.upsert({
    where: { userId },
    update: {
      darkModeEnabled: darkMode,
    },
    create: {
      userId,
      darkModeEnabled: darkMode,
    },
    select: {
      darkModeEnabled: true,
    },
  });

  if (!darkModeResponse) {
    throw new Error("Failed to save dark mode preference");
  }

  return darkModeResponse;
};
