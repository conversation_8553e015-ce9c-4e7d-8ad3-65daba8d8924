import { updateDarkModePreferenceService } from "./service";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{ Body: { darkMode: boolean } }>,
  reply: FastifyReply
) => {
  try {
    const { prisma } = request.server;
    const userId = request.userProfile.id;
    const { body } = request;

    const preference = await updateDarkModePreferenceService(
      userId,
      body.darkMode,
      prisma
    );

    return await reply.code(200).send(preference);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
