import { handler } from "./handler";
import { updateDarkModePreferenceService } from "./service";

vi.mock("./service", () => ({
  updateDarkModePreferenceService: vi.fn(),
}));

describe("update user preferences handler", () => {
  it("should return 200 and user preferences", async () => {
    vi.mocked(updateDarkModePreferenceService).mockResolvedValue({
      darkModeEnabled: true,
    });

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    const mockRequest = {
      server: {
        prisma: {},
      },
      userProfile: {
        id: 1,
      },
      body: {
        darkMode: true,
      },
    };

    // @ts-expect-error Mocking FastifyReply and request
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith({ darkModeEnabled: true });
  });

  it("should return 500 on error", async () => {
    vi.mocked(updateDarkModePreferenceService).mockRejectedValue(
      new Error("Database error")
    );

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    const mockRequest = {
      server: {
        prisma: {},
      },
      userProfile: {
        id: 1,
      },
      body: {
        darkMode: true,
      },
      log: {
        error: vi.fn(),
      },
    };

    // @ts-expect-error Mocking FastifyReply and request
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Database error",
    });
  });
});
