import { updateDarkModePreference } from "./repository";

describe("User Dark Mode Update Repository", () => {
  it("should update user preferences successfully", async () => {
    const mockPrisma = {
      userPreference: {
        upsert: vi.fn().mockResolvedValue({
          darkModeEnabled: true,
        }),
      },
    };

    const userId = 1;

    // @ts-expect-error mocking prisma
    const result = await updateDarkModePreference(userId, true, mockPrisma);

    expect(result).toEqual({
      darkModeEnabled: true,
    });

    expect(mockPrisma.userPreference.upsert).toHaveBeenCalledWith({
      where: { userId },
      update: {
        darkModeEnabled: true,
      },
      create: {
        userId,
        darkModeEnabled: true,
      },
      select: {
        darkModeEnabled: true,
      },
    });
  });

  it("should throw an error if update fails", async () => {
    const mockPrisma = {
      userPreference: {
        upsert: vi.fn().mockRejectedValue(new Error("Update failed")),
      },
    };

    const userId = 1;

    await expect(
      // @ts-expect-error mocking prisma
      updateDarkModePreference(userId, true, mockPrisma)
    ).rejects.toThrow("Update failed");
    expect(mockPrisma.userPreference.upsert).toHaveBeenCalledWith({
      where: { userId },
      update: {
        darkModeEnabled: true,
      },
      create: {
        userId,
        darkModeEnabled: true,
      },
      select: {
        darkModeEnabled: true,
      },
    });
  });

  it("should throw an error if no response is received", async () => {
    const mockPrisma = {
      userPreference: {
        upsert: vi.fn().mockResolvedValue(null),
      },
    };

    const userId = 1;

    await expect(
      // @ts-expect-error mocking prisma
      updateDarkModePreference(userId, false, mockPrisma)
    ).rejects.toThrow("Failed to save dark mode preference");

    expect(mockPrisma.userPreference.upsert).toHaveBeenCalledWith({
      where: { userId },
      update: {
        darkModeEnabled: false,
      },
      create: {
        userId,
        darkModeEnabled: false,
      },
      select: {
        darkModeEnabled: true,
      },
    });
  });
});
