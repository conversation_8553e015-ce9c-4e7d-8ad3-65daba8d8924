import { updateDarkModePreference } from "./repository";

import type { PrismaClient, userPreference } from "@prisma/client";

export const updateDarkModePreferenceService = async (
  userId: number,
  darkMode: boolean,
  prisma: PrismaClient
): Promise<Pick<userPreference, "darkModeEnabled">> => {
  const preference = await updateDarkModePreference(userId, darkMode, prisma);

  return preference;
};
