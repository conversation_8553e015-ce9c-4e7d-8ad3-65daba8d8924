/* eslint-disable @typescript-eslint/naming-convention */
import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Update Dark Mode Preference of User",
  description:
    "Saves the dark mode preference of the user, or defaults to false if not set.",
  tags: [tags.userPreference],
  body: {
    type: "object",
    properties: {
      darkMode: {
        type: "boolean",
        description: "Dark mode preference of the user",
        default: false,
      },
    },
    required: ["darkMode"],
    additionalProperties: false,
  },
  response: {
    200: {
      description: "Dark mode preference data",
    },
    500: {
      description: "Internal server error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
      additionalProperties: false,
    },
  },
};
