import type { PrismaClient, userPreference } from "@prisma/client";

export const getDarkModePreference = async (
  userId: number,
  prisma: PrismaClient
): Promise<Pick<userPreference, "darkModeEnabled">> => {
  const userPreference = await prisma.userPreference.findUnique({
    where: { userId },
    select: {
      darkModeEnabled: true,
    },
  });

  return userPreference ?? { darkModeEnabled: false };
};
