import { getDarkModePreferenceService } from "./service";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;
    const userId = request.userProfile.id;

    const preferences = await getDarkModePreferenceService(userId, prisma);

    return await reply.code(200).send(preferences);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
