import { getDarkModePreference } from "./repository";

describe("get user dark mode preferences respository", () => {
  it("should return dark mode preferences for a user", async () => {
    const mockPrisma = {
      userPreference: {
        findUnique: vi.fn().mockResolvedValue({ darkModeEnabled: true }),
      },
    };

    const userId = 1;
    // @ts-expect-error Mocking PrismaClient
    const result = await getDarkModePreference(userId, mockPrisma);

    expect(result).toEqual({ darkModeEnabled: true });
    expect(mockPrisma.userPreference.findUnique).toHaveBeenCalledWith({
      where: { userId },
      select: {
        darkModeEnabled: true,
      },
    });
  });

  it("should return darkModeEnabled false if no preferences found", async () => {
    const mockPrisma = {
      userPreference: {
        findUnique: vi.fn().mockResolvedValue(null),
      },
    };

    const userId = 1;
    // @ts-expect-error Mocking PrismaClient
    const result = await getDarkModePreference(userId, mockPrisma);

    expect(result).toEqual({ darkModeEnabled: false });
    expect(mockPrisma.userPreference.findUnique).toHaveBeenCalledWith({
      where: { userId },
      select: {
        darkModeEnabled: true,
      },
    });
  });
});
