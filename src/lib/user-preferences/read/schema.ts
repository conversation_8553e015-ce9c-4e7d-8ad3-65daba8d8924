/* eslint-disable @typescript-eslint/naming-convention */
import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Dark Mode Preference of User",
  description: "Fetches the dark mode preference of the user.",
  tags: [tags.userPreference],
  response: {
    200: {
      description: "Dark Mode preference data",
      type: "object",
      properties: {
        darkModeEnabled: {
          type: "boolean",
          description: "Dark mode preference of the user",
          default: false,
        },
      },
    },
    500: {
      description: "Internal server error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
      additionalProperties: false,
    },
  },
};
