import { handler } from "./handler";
import { getDarkModePreferenceService } from "./service";

vi.mock("./service", () => ({
  getDarkModePreferenceService: vi.fn(),
}));

describe("read user preferences handler", () => {
  it("should return 200 and user preferences", async () => {
    vi.mocked(getDarkModePreferenceService).mockResolvedValue({
      darkModeEnabled: true,
    });

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    const mockRequest = {
      server: {
        prisma: {},
      },
      userProfile: {
        id: 1,
      },
    };

    // @ts-expect-error Mocking FastifyReply and request
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith({ darkModeEnabled: true });
  });

  it("should return 500 on error", async () => {
    vi.mocked(getDarkModePreferenceService).mockRejectedValue(
      new Error("Database error")
    );

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    const mockRequest = {
      server: {
        prisma: {},
      },
      userProfile: {
        id: 1,
      },
      log: {
        error: vi.fn(),
      },
    };

    // @ts-expect-error Mocking FastifyReply and request
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Database error",
    });
  });
});
