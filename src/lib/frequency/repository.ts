import { type SettlementFrequency } from "@lib/settlement/read/types";

import { type Frequency } from "../../types/frequency/frequency";

import type { PrismaClient } from "@prisma/client";

export const getAllFrequencies = async (
  prisma: PrismaClient
): Promise<Frequency[]> => {
  const frequencies = await prisma.statementFrequency.findMany({
    select: {
      statementFrequencyId: true,
      statementFrequencyCode: true,
      statementFrequencyName: true,
    },
  });

  return frequencies.map((frequency) => ({
    frequencyId: frequency.statementFrequencyId,
    frequencyCode: frequency.statementFrequencyCode,
    frequencyName: frequency.statementFrequencyName,
  }));
};

export const getFrequency = async (
  frequencyId: number,
  prisma: PrismaClient
): Promise<Frequency | undefined> => {
  const frequency = await prisma.statementFrequency.findFirst({
    where: {
      statementFrequencyId: frequencyId,
    },
    select: {
      statementFrequencyId: true,
      statementFrequencyCode: true,
      statementFrequencyName: true,
    },
  });

  if (!frequency) {
    return;
  }

  return {
    frequencyId: frequency.statementFrequencyId,
    frequencyCode: frequency.statementFrequencyCode,
    frequencyName: frequency.statementFrequencyName,
  };
};

export const getFrequencyByName = async (
  prisma: PrismaClient,
  frequencyName: SettlementFrequency
): Promise<Frequency | undefined> => {
  const frequency = await prisma.statementFrequency.findFirst({
    where: {
      statementFrequencyName: frequencyName,
    },
    select: {
      statementFrequencyId: true,
      statementFrequencyCode: true,
      statementFrequencyName: true,
    },
  });

  if (!frequency) {
    return;
  }

  return {
    frequencyId: frequency.statementFrequencyId,
    frequencyCode: frequency.statementFrequencyCode,
    frequencyName: frequency.statementFrequencyName,
  };
};
