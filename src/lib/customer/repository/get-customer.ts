import { type PrismaClient } from "@prisma/client";
import { fromDateOnly, type DateOnly } from "@utils/date-only";

import { customerSelectClause } from "./helpers/customer-select-clause";
import { customerTypeSelectClause } from "./helpers/customer-type-select-clause";
import { flattenCustomer } from "./helpers/flatten-customer";
import { type Customer } from "./types";
import { type NonMerchantType } from "../../settlement/repository/types";

const getAllCustomers = async (
  prisma: PrismaClient,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All"
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      deletedAt: null,
      customer: {
        deletedAt: null,
      },
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

const getAllCustomersByFrequency = async (
  prisma: PrismaClient,
  frequencyId: number,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All",
  isActive = true
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      deletedAt: null,
      statementFrequencyId: frequencyId,
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
      customer: {
        enabled: isActive,
        deletedAt: null,
      },
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

const getCustomers = async (
  customerCustomerTypeIds: number[],
  prisma: PrismaClient,
  type: "Merchant" | "Agent" | "Sub Agent" | "Integrator" | "All" = "All"
): Promise<Customer[]> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      customerCustomerTypeId: { in: customerCustomerTypeIds },
      deletedAt: null,
      ...(type !== "All" && {
        customerType: {
          customerTypeName: type,
        },
      }),
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customerType: {
        select: customerTypeSelectClause,
      },
      customer: {
        select: customerSelectClause,
      },
    },
  });

  return customers.map((customer) => flattenCustomer(customer));
};

/**
 * Returns the unique merchant IDs (that are enabled) associated with a given non-merchant.
 */
const getAssociatedMerchantIds = async (
  prisma: PrismaClient,
  nonMerchantId: number,
  type: NonMerchantType,
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  includeDisabled?: boolean
): Promise<number[]> => {
  // Determine the field name based on the type.
  let fieldName:
    | "integratorCustomerId"
    | "agentCustomerId"
    | "subAgentCustomerId";

  switch (type) {
    case "Integrator": {
      fieldName = "integratorCustomerId";
      break;
    }

    case "Agent": {
      fieldName = "agentCustomerId";
      break;
    }

    case "Sub Agent": {
      fieldName = "subAgentCustomerId";
      break;
    }
  }

  const platforms = await prisma.merchantPlatform.findMany({
    where: {
      deletedAt: null,
      [fieldName]: nonMerchantId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      customer_merchantPlatform_clientCustomerIdTocustomer: {
        ...(includeDisabled ? {} : { enabled: true }),
        deletedAt: null,
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      AND: [
        // Ensure the settlement period is within the merchantPlatform dates.
        {
          fromDate: {
            lte: fromDateOnly(effectiveDate.toDate),
          },
        },
        {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          OR: [
            {
              toDate: {
                gte: fromDateOnly(effectiveDate.fromDate),
              },
            },
            {
              // Currently active merchantPlatforms do not have a toDate
              toDate: null,
            },
          ],
        },
      ],
    },
    distinct: ["clientCustomerId"],
    select: { clientCustomerId: true },
  });

  return [...new Set(platforms.map((p) => p.clientCustomerId))];
};

/**
 * Returns the unique serviceNumbers for all merchants associated
 * with a given non-merchant.
 */
const getAssociatedMerchantServiceNumbers = async (
  nonMerchantId: number,
  type: NonMerchantType,
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<string[]> => {
  const merchantIds = await getAssociatedMerchantIds(
    prisma,
    nonMerchantId,
    type,
    effectiveDate,
    false
  );

  const customers = await prisma.customer.findMany({
    where: {
      customerId: { in: merchantIds },
      deletedAt: null,
    },
    select: { serviceNumber: true },
  });

  return [...new Set(customers.map((c) => c.serviceNumber).filter(Boolean))];
};

/**
 * Given an array of non-merchant serviceNumbers,
 * fetch all their associated merchant serviceNumbers.
 */
export async function fetchCombinedMerchantServiceNumbers(
  nonMerchantServiceNumbers: string[],
  effectiveDate: { fromDate: DateOnly; toDate: DateOnly },
  prisma: PrismaClient
): Promise<string[]> {
  const nestedArrays = await Promise.all(
    nonMerchantServiceNumbers.map(async (serviceNumber) => {
      const customer = await prisma.customer.findFirst({
        select: {
          customerId: true,
          customerCustomerType: {
            select: {
              customerType: {
                select: {
                  customerTypeName: true,
                },
              },
            },
          },
        },
        where: { serviceNumber },
      });

      const typeName =
        customer?.customerCustomerType[0]?.customerType.customerTypeName;

      if (!typeName) {
        return [];
      }

      return getAssociatedMerchantServiceNumbers(
        customer.customerId,
        typeName as NonMerchantType,
        effectiveDate,
        prisma
      );
    })
  );

  return nestedArrays.flat();
}

export {
  getAllCustomers,
  getAllCustomersByFrequency,
  getCustomers,
  getAssociatedMerchantIds,
};
