import { promises as fs } from "node:fs";
import path from "node:path";

import { environment } from "@constants/environment";
import { getPaymentEnabledPlatformCodes } from "@lib/platform/read/repositories";
import { format } from "date-fns";

import type { ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import type { PrismaClient } from "@prisma/client";

export const getAttachments = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement
): Promise<string[]> => {
  const attachments: string[] = [];
  const statementFolderLocation =
    settlement.customerCustomerType?.statementFolderLocation;
  const customerTradingName = settlement.customer?.customerTradingName;
  const customerTypeName =
    settlement.customerCustomerType?.customerType?.customerTypeName;
  const { fromDate, toDate } = settlement;

  if (!statementFolderLocation) {
    return attachments;
  }

  const formattedFromDate = format(new Date(fromDate), "yyyy-MM-dd");
  const formattedToDate = format(new Date(toDate), "yyyy-MM-dd");

  if (customerTypeName === "Merchant") {
    const platforms = await getPaymentEnabledPlatformCodes(prisma);

    const platformCodes = [...platforms.map((p) => p.platformCode), "Summary"];

    const platformPromises = platformCodes.map(async (platformCode) => {
      const folderPath = path.join(statementFolderLocation, platformCode);
      const baseFileName = `${customerTradingName}-${formattedFromDate}-${formattedToDate}-${platformCode}`;

      try {
        const files = await fs.readdir(
          path.join(environment.baseFileLocation!, folderPath)
        );

        const relevantFiles = files.filter((file) =>
          file.startsWith(baseFileName)
        );

        if (relevantFiles.length === 0) {
          return null;
        }

        const highestRevFile = getHighestRevFile(relevantFiles);

        if (!highestRevFile) {
          return null;
        }

        return path.join(folderPath, highestRevFile);
      } catch {
        // Ignore error if directory doesn't exist
        return null;
      }
    });

    const results = await Promise.all(platformPromises);

    attachments.push(
      ...results.filter((result): result is string => result !== null)
    );
  } else {
    const folderPath = path.join(statementFolderLocation);
    const baseFileName = `${customerTradingName}-${formattedFromDate}-${formattedToDate}`;

    try {
      const files = await fs.readdir(
        path.join(environment.baseFileLocation!, folderPath)
      );

      const relevantFiles = files.filter((file) =>
        file.startsWith(baseFileName)
      );

      if (relevantFiles.length === 0) {
        return attachments;
      }

      const highestRevFile = getHighestRevNonMerchantFile(relevantFiles);

      if (!highestRevFile) {
        return attachments;
      }

      const filePath = path.join(folderPath, highestRevFile);
      attachments.push(filePath);
    } catch {
      return attachments;
    }
  }

  return attachments;
};

function getHighestRevFile(files: string[]): string {
  let highestRevFile = files[0];
  let highestRev = 0;

  for (const file of files) {
    const match = / rev(\d+)\.xlsx$/i.exec(file);
    const rev = match ? Number.parseInt(match[1]!, 10) : 0;

    if (rev >= highestRev) {
      highestRev = rev;
      highestRevFile = file;
    }
  }

  return highestRevFile!;
}

/**
 * Extracts and returns the best candidate file (highest revision file)
 * from a list of files for non-merchant customers.
 *
 * @param files - An array of file names (filtered by base file name).
 * @returns The chosen file name or null if none found.
 */
function getHighestRevNonMerchantFile(files: string[]): string | undefined {
  // Filter out any files that contain '-init.xlsx'
  const filteredFiles = files.filter((file) => !file.includes("-init.xlsx"));

  let highestRevFile;
  let highestRev = -1;
  let finalFile;

  for (const file of filteredFiles) {
    const revMatch = /Rev(\d+)/.exec(file);

    if (revMatch) {
      const revNumber = Number.parseInt(revMatch[1]!, 10);

      // Only consider files that also indicate final (i.e., contain '-final.xlsx')
      if (file.includes("-final.xlsx") && revNumber > highestRev) {
        highestRev = revNumber;
        highestRevFile = file;
      }
    } else if (file.includes("-final.xlsx")) {
      finalFile = file;
    }
  }

  return highestRevFile ?? finalFile;
}
