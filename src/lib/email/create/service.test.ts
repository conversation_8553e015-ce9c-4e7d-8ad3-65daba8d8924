import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import { getAttachments } from "@lib/filesystem/attachments/service";
import { type Wire } from "@lib/wire/create/types";
import { type PrismaClient } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { createEmailWithWire } from "./repository";
import { createEmail } from "./service";

vi.mock("@lib/filesystem/attachments/service");
vi.mock("./repository");

const mockGetAttachments = vi.mocked(getAttachments);
const mockCreateEmailWithWire = vi.mocked(createEmailWithWire);

type MockPrismaClient = Record<string, unknown>;

const createMockEmail = (overrides: Record<string, unknown> = {}) => ({
  wireId: 123,
  subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
  scheduledSendDate: new Date("2024-01-15"),
  attachments: "[]",
  emailId: 1,
  transferInId: null,
  sender: null,
  toMeta: {},
  ccMeta: {},
  body: null,
  comment: null,
  sentAt: null,
  sentById: null,
  sendErrorMessage: null,
  isEndBalance: false,
  createdAt: new Date("2024-01-15"),
  updatedAt: new Date("2024-01-15"),
  deletedAt: null,
  ...overrides,
});

describe("createEmail", () => {
  let mockPrisma: MockPrismaClient;
  let mockSettlement: ApprovalCustomerSettlement;
  let mockWire: Wire;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();

    mockPrisma = {};

    const mockSettlementData: ApprovalCustomerSettlement = {
      customerSettlementsId: 1,
      customerId: 123,
      fromDate: new Date("2024-01-01"),
      toDate: new Date("2024-01-31"),
      endBalance: 1000,
      platform: {
        platformId: 1,
        platformCode: "SUMMARY",
      },
      customer: {
        customerName: "Test Customer Corp",
        serviceNumber: "SVC-12345",
        customerTradingName: "Test Trading",
        entity: {
          entityName: "Test Entity",
          logoName: "test-logo.png",
        },
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 1000,
          },
          beneficiary: {
            reference: "REF-123",
            bankAccount: {
              bankPaymentRailId: 1,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
      },
      customerCustomerType: {
        statementFolderLocation: "/path/to/statements",
        statementFrequency: {
          statementFrequencyId: 1,
          statementFrequencyCode: "M",
        },
        customerType: {
          customerTypeName: "Regular",
        },
      },
    };

    const mockWireData: Wire = {
      wireId: 123,
      expectedWireDate: new Date("2024-01-15"),
      finalWireDate: new Date("2024-01-15"),
      expectedWireAmount: 1000,
      finalWireAmount: 1000,
      isHold: false,
      reference: "WIRE-REF-123",
      frequencyId: 1,
      isCancelled: false,
      customerId: 123,
      settlementId: 1,
      paymentRailId: 1,
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-15T10:00:00Z",
    };

    mockSettlement = mockSettlementData;
    mockWire = mockWireData;
  });

  it("should create email with correct parameters", async () => {
    const mockAttachments = ["document1.pdf", "document2.pdf"];
    const mockCreatedEmail = createMockEmail({
      attachments: JSON.stringify(mockAttachments),
    });

    mockGetAttachments.mockResolvedValue(mockAttachments);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    const result = await createEmail(
      mockPrisma as unknown as PrismaClient,
      mockSettlement,
      mockWire
    );

    expect(mockGetAttachments).toHaveBeenCalledWith(mockPrisma, mockSettlement);
    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
      scheduledSendDate: expect.any(Date) as Date,
      attachments: JSON.stringify(mockAttachments),
    });
    expect(result).toEqual(mockCreatedEmail);
  });

  it("should handle empty attachments array", async () => {
    const mockAttachments: string[] = [];
    const mockCreatedEmail = createMockEmail();

    mockGetAttachments.mockResolvedValue(mockAttachments);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    const result = await createEmail(
      mockPrisma as unknown as PrismaClient,
      mockSettlement,
      mockWire
    );

    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
      scheduledSendDate: expect.any(Date) as Date,
      attachments: JSON.stringify([]),
    });
    expect(result).toEqual(mockCreatedEmail);
  });

  it("should format subject with service number and customer name", async () => {
    const customSettlement: ApprovalCustomerSettlement = {
      ...mockSettlement,
      customer: {
        ...mockSettlement.customer,
        serviceNumber: "ABC-999",
        customerName: "Another Customer LLC",
      },
    };

    const mockCreatedEmail = createMockEmail({
      subject: "ABC-999 - Another Customer LLC - Settlement Notification",
    });

    mockGetAttachments.mockResolvedValue([]);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    await createEmail(
      mockPrisma as unknown as PrismaClient,
      customSettlement,
      mockWire
    );

    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "ABC-999 - Another Customer LLC - Settlement Notification",
      scheduledSendDate: expect.any(Date) as Date,
      attachments: JSON.stringify([]),
    });
  });

  it("should set scheduledSendDate to current date", async () => {
    const mockDate = new Date("2024-01-15T00:00:00.000Z");
    vi.setSystemTime(mockDate);

    const mockCreatedEmail = createMockEmail({
      scheduledSendDate: mockDate,
    });

    mockGetAttachments.mockResolvedValue([]);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    await createEmail(
      mockPrisma as unknown as PrismaClient,
      mockSettlement,
      mockWire
    );

    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
      scheduledSendDate: mockDate,
      attachments: JSON.stringify([]),
    });

    vi.useRealTimers();
  });

  it("should propagate errors from getAttachments", async () => {
    const error = new Error("Failed to get attachments");
    mockGetAttachments.mockRejectedValue(error);

    await expect(
      createEmail(
        mockPrisma as unknown as PrismaClient,
        mockSettlement,
        mockWire
      )
    ).rejects.toThrow("Failed to get attachments");
  });

  it("should propagate errors from createEmailWithWire", async () => {
    const error = new Error("Failed to create email");
    mockGetAttachments.mockResolvedValue([]);
    mockCreateEmailWithWire.mockRejectedValue(error);

    await expect(
      createEmail(
        mockPrisma as unknown as PrismaClient,
        mockSettlement,
        mockWire
      )
    ).rejects.toThrow("Failed to create email");
  });

  it("should handle null attachments gracefully", async () => {
    const mockCreatedEmail = createMockEmail({
      attachments: "null",
    });

    mockGetAttachments.mockResolvedValue(null as unknown as string[]);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    await createEmail(
      mockPrisma as unknown as PrismaClient,
      mockSettlement,
      mockWire
    );

    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
      scheduledSendDate: expect.any(Date) as Date,
      attachments: JSON.stringify(null),
    });
  });

  it("should handle multiple attachment files", async () => {
    const mockAttachments = [
      "settlement_report.pdf",
      "wire_details.xlsx",
      "summary_document.docx",
    ];

    const mockCreatedEmail = createMockEmail({
      attachments: JSON.stringify(mockAttachments),
    });

    mockGetAttachments.mockResolvedValue(mockAttachments);
    mockCreateEmailWithWire.mockResolvedValue(mockCreatedEmail);

    await createEmail(
      mockPrisma as unknown as PrismaClient,
      mockSettlement,
      mockWire
    );

    expect(mockCreateEmailWithWire).toHaveBeenCalledWith(mockPrisma, {
      wireId: 123,
      subject: "SVC-12345 - Test Customer Corp - Settlement Notification",
      scheduledSendDate: expect.any(Date) as Date,
      attachments: JSON.stringify(mockAttachments),
    });
  });
});
