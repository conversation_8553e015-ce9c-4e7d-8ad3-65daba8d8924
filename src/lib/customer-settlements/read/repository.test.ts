import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import {
  fetchSettlementDetailsForApproval,
  fetchPreviousSettlementForEndBalance,
} from "./repository";

type MockPrismaClient = {
  customerSettlements: {
    findUnique: ReturnType<typeof vi.fn>;
    findFirst: ReturnType<typeof vi.fn>;
  };
};

const mockPrismaClient: MockPrismaClient = {
  customerSettlements: {
    findUnique: vi.fn(),
    findFirst: vi.fn(),
  },
};

type MockSettlementData = {
  customerSettlementsId: number;
  customerId: number;
  fromDate: Date;
  toDate: Date;
  endBalance: string | undefined;
  regenerationCount: number;
  platform: {
    platformId: number;
    platformCode: string;
  };
  customer: {
    customerName: string;
    serviceNumber: string;
    customerTradingName: string | undefined;
    entity: {
      entityName: string;
      logoName: string;
    };
    wireGroup:
      | {
          wireConfiguration: {
            thresholdAmount: string;
          };
          beneficiary:
            | {
                reference: string;
                bankAccount: {
                  bankPaymentRailId: number;
                };
                cryptoAccount:
                  | {
                      cryptoPaymentRailId: number;
                    }
                  | undefined;
              }
            | undefined;
        }
      | undefined;
    emailConfiguration: Record<string, unknown>;
    customerSettlementType: Record<string, unknown>;
  };
  customerCustomerType:
    | {
        statementFolderLocation: string | undefined;
        statementFrequency: {
          statementFrequencyId: number;
          statementFrequencyCode: string;
        };
        customerType: {
          customerTypeName: string;
        };
      }
    | undefined;
};

const createMockSettlementData = (
  overrides?: Partial<MockSettlementData>
): MockSettlementData => ({
  customerSettlementsId: 1,
  customerId: 123,
  fromDate: new Date("2025-01-01"),
  toDate: new Date("2025-01-31"),
  endBalance: "1000.50",
  regenerationCount: 0,
  platform: {
    platformId: 1,
    platformCode: "PLAT001",
  },
  customer: {
    customerName: "Test Customer",
    serviceNumber: "SN123456",
    customerTradingName: "Test Trading Name",
    entity: {
      entityName: "Test Entity",
      logoName: "test-logo.png",
    },
    wireGroup: {
      wireConfiguration: {
        thresholdAmount: "500.00",
      },
      beneficiary: {
        reference: "REF123",
        bankAccount: {
          bankPaymentRailId: 1,
        },
        cryptoAccount: {
          cryptoPaymentRailId: 2,
        },
      },
    },
    emailConfiguration: {},
    customerSettlementType: {},
  },
  customerCustomerType: {
    statementFolderLocation: "/statements/folder",
    statementFrequency: {
      statementFrequencyId: 1,
      statementFrequencyCode: "M",
    },
    customerType: {
      customerTypeName: "Corporate",
    },
  },
  ...overrides,
});

type MockPreviousSettlement = {
  customerSettlementsId: number;
  endBalance: string | undefined;
  toDate: Date;
  customerId: number;
  platformId: number;
  status: string | undefined;
};

const createMockPreviousSettlement = (
  overrides?: Partial<MockPreviousSettlement>
): MockPreviousSettlement => ({
  customerSettlementsId: 1,
  endBalance: "1000.50",
  toDate: new Date("2025-01-31"),
  customerId: 123,
  platformId: 1,
  status: "APPROVED",
  ...overrides,
});

describe("Customer Settlements Read Repository", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(null);
  });

  describe("fetchSettlementDetailsForApproval", () => {
    it("should return settlement details for approval when settlement exists", async () => {
      const mockSettlement = createMockSettlementData();
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(
        mockPrismaClient.customerSettlements.findUnique
      ).toHaveBeenCalledWith({
        where: {
          customerSettlementsId: 1,
        },
        include: {
          platform: true,
          customer: {
            include: {
              entity: true,
              wireGroup: {
                include: {
                  wireConfiguration: true,
                  beneficiary: {
                    include: {
                      bankAccount: true,
                      cryptoAccount: true,
                    },
                  },
                },
              },
              emailConfiguration: true,
              customerSettlementType: true,
            },
          },
          customerCustomerType: {
            include: {
              statementFrequency: true,
              customerType: true,
            },
          },
        },
      });

      expect(result).toEqual({
        customerSettlementsId: 1,
        customerId: 123,
        fromDate: new Date("2025-01-01"),
        toDate: new Date("2025-01-31"),
        endBalance: 1000.5,
        regenerationCount: 0,
        platform: {
          platformId: 1,
          platformCode: "PLAT001",
        },
        customer: {
          customerName: "Test Customer",
          serviceNumber: "SN123456",
          customerTradingName: "Test Trading Name",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: {
              isHold: false,
              thresholdAmount: 500,
            },
            beneficiary: {
              reference: "REF123",
              bankAccount: {
                bankPaymentRailId: 1,
              },
              cryptoAccount: {
                cryptoPaymentRailId: 2,
              },
            },
          },
          emailConfiguration: {
            isEmailEnabled: true,
          },
          customerSettlementType: {},
        },
        customerCustomerType: {
          statementFolderLocation: "/statements/folder",
          statementFrequency: {
            statementFrequencyId: 1,
            statementFrequencyCode: "M",
          },
          customerType: {
            customerTypeName: "Corporate",
          },
        },
      });
    });

    it("should return undefined when settlement does not exist", async () => {
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(null);

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        999
      );

      expect(result).toBeUndefined();
    });

    it("should handle settlement with undefined endBalance", async () => {
      const mockSettlement = createMockSettlementData({
        endBalance: undefined,
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(result?.endBalance).toBe(0);
    });

    it("should handle settlement with undefined customerTradingName", async () => {
      const mockSettlement = createMockSettlementData({
        customer: {
          customerName: "Test Customer",
          serviceNumber: "SN123456",
          customerTradingName: undefined,
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: {
              thresholdAmount: "500.00",
            },
            beneficiary: {
              reference: "REF123",
              bankAccount: {
                bankPaymentRailId: 1,
              },
              cryptoAccount: {
                cryptoPaymentRailId: 2,
              },
            },
          },
          emailConfiguration: {},
          customerSettlementType: {},
        },
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(result?.customer.customerTradingName).toBe("");
    });

    it("should handle settlement with undefined wireGroup", async () => {
      const mockSettlement = createMockSettlementData({
        customer: {
          customerName: "Test Customer",
          serviceNumber: "SN123456",
          customerTradingName: "Test Trading Name",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: undefined,
          emailConfiguration: {},
          customerSettlementType: {},
        },
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(result?.customer.wireGroup).toEqual({
        wireConfiguration: {
          isHold: false,
          thresholdAmount: 0,
        },
        beneficiary: {
          reference: "",
        },
      });
    });

    it("should handle settlement with undefined beneficiary", async () => {
      const mockSettlement = createMockSettlementData({
        customer: {
          customerName: "Test Customer",
          serviceNumber: "SN123456",
          customerTradingName: "Test Trading Name",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: {
              thresholdAmount: "500.00",
            },
            beneficiary: undefined,
          },
          emailConfiguration: {},
          customerSettlementType: {},
        },
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(result?.customer.wireGroup.beneficiary).toEqual({
        reference: "",
      });
    });

    it("should handle settlement without cryptoAccount", async () => {
      const mockSettlement = createMockSettlementData({
        customer: {
          customerName: "Test Customer",
          serviceNumber: "SN123456",
          customerTradingName: "Test Trading Name",
          entity: {
            entityName: "Test Entity",
            logoName: "test-logo.png",
          },
          wireGroup: {
            wireConfiguration: {
              thresholdAmount: "500.00",
            },
            beneficiary: {
              reference: "REF123",
              bankAccount: {
                bankPaymentRailId: 1,
              },
              cryptoAccount: undefined,
            },
          },
          emailConfiguration: {},
          customerSettlementType: {},
        },
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(
        result?.customer.wireGroup.beneficiary.cryptoAccount
      ).toBeUndefined();
    });

    it("should handle settlement with undefined customerCustomerType", async () => {
      const mockSettlement = createMockSettlementData({
        customerCustomerType: undefined,
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(result?.customerCustomerType).toEqual({
        statementFrequency: {
          statementFrequencyId: 0,
        },
        customerType: {
          customerTypeName: "",
        },
      });
    });

    it("should handle settlement without statementFolderLocation", async () => {
      const mockSettlement = createMockSettlementData({
        customerCustomerType: {
          statementFolderLocation: undefined,
          statementFrequency: {
            statementFrequencyId: 1,
            statementFrequencyCode: "M",
          },
          customerType: {
            customerTypeName: "Corporate",
          },
        },
      });
      mockPrismaClient.customerSettlements.findUnique.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchSettlementDetailsForApproval(
        mockPrismaClient as unknown as PrismaClient,
        1
      );

      expect(
        result?.customerCustomerType.statementFolderLocation
      ).toBeUndefined();
    });

    it("should handle database errors", async () => {
      const mockError = new Error("Database connection failed");
      mockPrismaClient.customerSettlements.findUnique.mockRejectedValue(
        mockError
      );

      await expect(
        fetchSettlementDetailsForApproval(
          mockPrismaClient as unknown as PrismaClient,
          1
        )
      ).rejects.toThrow("Database connection failed");
    });
  });

  describe("fetchPreviousSettlementForEndBalance", () => {
    it("should return previous settlement when it exists", async () => {
      const mockSettlement = createMockPreviousSettlement();
      mockPrismaClient.customerSettlements.findFirst.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchPreviousSettlementForEndBalance(
        mockPrismaClient as unknown as PrismaClient,
        123,
        1,
        new Date("2025-01-31")
      );

      expect(
        mockPrismaClient.customerSettlements.findFirst
      ).toHaveBeenCalledWith({
        where: {
          customerId: 123,
          platformId: 1,
          toDate: new Date("2025-01-31"),
          deletedAt: null,
        },
        orderBy: {
          toDate: "desc",
        },
        select: {
          customerSettlementsId: true,
          endBalance: true,
          toDate: true,
          customerId: true,
          platformId: true,
          status: true,
        },
      });

      expect(result).toEqual({
        customerSettlementsId: 1,
        endBalance: "1000.50",
        toDate: new Date("2025-01-31"),
        customerId: 123,
        platformId: 1,
        status: "APPROVED",
      });
    });

    it("should return undefined when no previous settlement exists", async () => {
      mockPrismaClient.customerSettlements.findFirst.mockResolvedValue(null);

      const result = await fetchPreviousSettlementForEndBalance(
        mockPrismaClient as unknown as PrismaClient,
        123,
        1,
        new Date("2025-01-31")
      );

      expect(result).toBeUndefined();
    });

    it("should handle settlement with undefined endBalance", async () => {
      const mockSettlement = createMockPreviousSettlement({
        endBalance: undefined,
      });
      mockPrismaClient.customerSettlements.findFirst.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchPreviousSettlementForEndBalance(
        mockPrismaClient as unknown as PrismaClient,
        123,
        1,
        new Date("2025-01-31")
      );

      expect(result?.endBalance).toBe("0");
    });

    it("should handle settlement with undefined status", async () => {
      const mockSettlement = createMockPreviousSettlement({
        status: undefined,
      });
      mockPrismaClient.customerSettlements.findFirst.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchPreviousSettlementForEndBalance(
        mockPrismaClient as unknown as PrismaClient,
        123,
        1,
        new Date("2025-01-31")
      );

      expect(result?.status).toBe("");
    });

    it("should handle different customer and platform combinations", async () => {
      const mockSettlement = createMockPreviousSettlement({
        customerId: 456,
        platformId: 2,
        customerSettlementsId: 999,
      });
      mockPrismaClient.customerSettlements.findFirst.mockResolvedValue(
        mockSettlement
      );

      const result = await fetchPreviousSettlementForEndBalance(
        mockPrismaClient as unknown as PrismaClient,
        456,
        2,
        new Date("2025-02-28")
      );

      expect(
        mockPrismaClient.customerSettlements.findFirst
      ).toHaveBeenCalledWith({
        where: {
          customerId: 456,
          platformId: 2,
          toDate: new Date("2025-02-28"),
          deletedAt: null,
        },
        orderBy: {
          toDate: "desc",
        },
        select: {
          customerSettlementsId: true,
          endBalance: true,
          toDate: true,
          customerId: true,
          platformId: true,
          status: true,
        },
      });

      expect(result).toEqual({
        customerSettlementsId: 999,
        endBalance: "1000.50",
        toDate: new Date("2025-01-31"),
        customerId: 456,
        platformId: 2,
        status: "APPROVED",
      });
    });

    it("should handle database errors", async () => {
      const mockError = new Error("Database connection failed");
      mockPrismaClient.customerSettlements.findFirst.mockRejectedValue(
        mockError
      );

      await expect(
        fetchPreviousSettlementForEndBalance(
          mockPrismaClient as unknown as PrismaClient,
          123,
          1,
          new Date("2025-01-31")
        )
      ).rejects.toThrow("Database connection failed");
    });

    it("should handle network timeout errors", async () => {
      const mockError = new Error("Network timeout");
      mockPrismaClient.customerSettlements.findFirst.mockRejectedValue(
        mockError
      );

      await expect(
        fetchPreviousSettlementForEndBalance(
          mockPrismaClient as unknown as PrismaClient,
          123,
          1,
          new Date("2025-01-31")
        )
      ).rejects.toThrow("Network timeout");
    });
  });
});
