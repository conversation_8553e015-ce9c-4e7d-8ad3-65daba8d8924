import { options as getDarkModeUserPreferenceOptions } from "../../lib/user-preferences/read/options";
import { options as saveDarkModeUserPreferenceOptions } from "../../lib/user-preferences/update/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.get("/", getDarkModeUserPreferenceOptions);
  server.post("/", saveDarkModeUserPreferenceOptions);
}
