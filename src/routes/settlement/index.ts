import { options as getSettlementFiltersOptions } from "@lib/settlement/read/filters/options";
import { options as getWiresForSettlementOptions } from "@lib/settlement/read/get-wires-for-settlement/options";
import { options as getSettlementOptions } from "@lib/settlement/read/options";

import { options as createAdjustmentOptions } from "../../lib/settlement/adjustment/create/options";
import { options as deleteAdjustmentOptions } from "../../lib/settlement/adjustment/delete/options";
import { options as allFrequencyCreateOptions } from "../../lib/settlement/all/frequency/create/options";
import { options as allFrequency } from "../../lib/settlement/all/frequency/read/options";
import { options as allHistoryOptions } from "../../lib/settlement/all/history/options";
import { options as allMonthlyCreateOptions } from "../../lib/settlement/all/monthly/create/options";
import { options as allMonthlyDeleteOptions } from "../../lib/settlement/all/monthly/delete/options";
import { options as allMonthlyHistoryOptions } from "../../lib/settlement/all/monthly/history/options";
import { options as allMonthlyReadOptions } from "../../lib/settlement/all/monthly/read/options";
import { options as allMonthlyRecalculateOptions } from "../../lib/settlement/all/monthly/recalculate/options";
import { options as approveSettlement } from "../../lib/settlement/approve/options";
import { options as deleteOptions } from "../../lib/settlement/delete/options";
import { options as settlementPreferenceReadOptions } from "../../lib/settlement/read/filter-preferences/options";
import { options as readByIdOptions } from "../../lib/settlement/read/read-by-id/options";
import { options as regenerateOptions } from "../../lib/settlement/regenerate/options";
import { options as settlementPreferenceUpdateOptions } from "../../lib/settlement/update/filter-preferences/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.post("/all/monthly/recalculate", allMonthlyRecalculateOptions);
  server.post("/all/monthly/read", allMonthlyReadOptions);
  server.post("/all/monthly", allMonthlyCreateOptions);
  server.get("/all/monthly/history", allMonthlyHistoryOptions);
  server.get("/all/frequency/read", allFrequency);
  server.post("/generate", allFrequencyCreateOptions);
  server.delete("/all/monthly/history", allMonthlyDeleteOptions);
  server.post("/all/frequency", allFrequencyCreateOptions);
  server.post("/", getSettlementOptions);
  server.get("/filters", getSettlementFiltersOptions);
  server.get("/:id", readByIdOptions);
  server.get("/wires", getWiresForSettlementOptions);
  server.get("/preference", settlementPreferenceReadOptions);
  server.put("/preference", settlementPreferenceUpdateOptions);
  server.post("/:id/adjustment", createAdjustmentOptions);
  server.delete("/:id/adjustment/:adjustmentId", deleteAdjustmentOptions);
  server.delete("/", deleteOptions);
  server.get("/all/history", allHistoryOptions);
  server.post("/approve", approveSettlement);
  server.post("/regenerate", regenerateOptions);
}
